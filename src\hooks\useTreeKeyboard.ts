/**
 * Tree Keyboard Navigation Hook
 * Provides comprehensive keyboard navigation for tree views
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { FileNode } from '../types'

interface FlattenedNode extends FileNode {
  depth: number
  index: number
  hasChildren: boolean
  isExpanded: boolean
}

interface UseTreeKeyboardOptions {
  nodes: FileNode[]
  expandedFolders: Set<string>
  onNodeSelect: (node: FileNode) => void
  onToggleExpand: (path: string) => void
  onNodeActivate?: (node: FileNode) => void
  enableTypeAhead?: boolean
  typeAheadDelay?: number
}

export const useTreeKeyboard = ({
  nodes,
  expandedFolders,
  onNodeSelect,
  onToggleExpand,
  onNodeActivate,
  enableTypeAhead = true,
  typeAheadDelay = 1000
}: UseTreeKeyboardOptions) => {
  const [focusedIndex, setFocusedIndex] = useState<number>(-1)
  const [typeAheadQuery, setTypeAheadQuery] = useState<string>('')
  const typeAheadTimeoutRef = useRef<NodeJS.Timeout>()
  const containerRef = useRef<HTMLElement>()

  // Flatten tree for keyboard navigation
  const flattenedNodes = useCallback((): FlattenedNode[] => {
    const flatten = (nodes: FileNode[], depth = 0): FlattenedNode[] => {
      const result: FlattenedNode[] = []
      
      nodes.forEach((node) => {
        const isExpanded = expandedFolders.has(node.path)
        const hasChildren = node.type === 'folder' && node.children && node.children.length > 0
        
        result.push({
          ...node,
          depth,
          index: result.length,
          hasChildren,
          isExpanded
        })

        // Add children if folder is expanded
        if (isExpanded && node.children) {
          result.push(...flatten(node.children, depth + 1))
        }
      })
      
      return result
    }

    return flatten(nodes)
  }, [nodes, expandedFolders])

  const flattened = flattenedNodes()

  // Get currently focused node
  const focusedNode = focusedIndex >= 0 && focusedIndex < flattened.length 
    ? flattened[focusedIndex] 
    : null

  // Type-ahead search
  const handleTypeAhead = useCallback((char: string) => {
    if (!enableTypeAhead) return

    // Clear existing timeout
    if (typeAheadTimeoutRef.current) {
      clearTimeout(typeAheadTimeoutRef.current)
    }

    const newQuery = typeAheadQuery + char.toLowerCase()
    setTypeAheadQuery(newQuery)

    // Find matching node
    const matchIndex = flattened.findIndex((node, index) => 
      index > focusedIndex && 
      node.name.toLowerCase().startsWith(newQuery)
    )

    if (matchIndex !== -1) {
      setFocusedIndex(matchIndex)
      onNodeSelect(flattened[matchIndex])
    } else {
      // Search from beginning if no match found after current position
      const matchFromStart = flattened.findIndex(node => 
        node.name.toLowerCase().startsWith(newQuery)
      )
      
      if (matchFromStart !== -1) {
        setFocusedIndex(matchFromStart)
        onNodeSelect(flattened[matchFromStart])
      }
    }

    // Clear type-ahead query after delay
    typeAheadTimeoutRef.current = setTimeout(() => {
      setTypeAheadQuery('')
    }, typeAheadDelay)
  }, [typeAheadQuery, flattened, focusedIndex, onNodeSelect, enableTypeAhead, typeAheadDelay])

  // Navigation functions
  const moveDown = useCallback(() => {
    if (flattened.length === 0) return
    const newIndex = Math.min(focusedIndex + 1, flattened.length - 1)
    setFocusedIndex(newIndex)
    onNodeSelect(flattened[newIndex])
  }, [flattened, focusedIndex, onNodeSelect])

  const moveUp = useCallback(() => {
    if (flattened.length === 0) return
    const newIndex = Math.max(focusedIndex - 1, 0)
    setFocusedIndex(newIndex)
    onNodeSelect(flattened[newIndex])
  }, [flattened, focusedIndex, onNodeSelect])

  const moveToParent = useCallback(() => {
    if (!focusedNode || focusedNode.depth === 0) return

    const parentDepth = focusedNode.depth - 1
    for (let i = focusedIndex - 1; i >= 0; i--) {
      if (flattened[i].depth === parentDepth) {
        setFocusedIndex(i)
        onNodeSelect(flattened[i])
        break
      }
    }
  }, [focusedNode, focusedIndex, flattened, onNodeSelect])

  const moveToFirstChild = useCallback(() => {
    if (!focusedNode || !focusedNode.hasChildren || !focusedNode.isExpanded) return

    const nextIndex = focusedIndex + 1
    if (nextIndex < flattened.length && flattened[nextIndex].depth > focusedNode.depth) {
      setFocusedIndex(nextIndex)
      onNodeSelect(flattened[nextIndex])
    }
  }, [focusedNode, focusedIndex, flattened, onNodeSelect])

  const expandNode = useCallback(() => {
    if (!focusedNode || !focusedNode.hasChildren || focusedNode.isExpanded) return
    onToggleExpand(focusedNode.path)
  }, [focusedNode, onToggleExpand])

  const collapseNode = useCallback(() => {
    if (!focusedNode || !focusedNode.hasChildren || !focusedNode.isExpanded) return
    onToggleExpand(focusedNode.path)
  }, [focusedNode, onToggleExpand])

  const activateNode = useCallback(() => {
    if (!focusedNode) return
    
    if (focusedNode.type === 'folder') {
      onToggleExpand(focusedNode.path)
    } else if (onNodeActivate) {
      onNodeActivate(focusedNode)
    }
  }, [focusedNode, onToggleExpand, onNodeActivate])

  const moveToFirst = useCallback(() => {
    if (flattened.length === 0) return
    setFocusedIndex(0)
    onNodeSelect(flattened[0])
  }, [flattened, onNodeSelect])

  const moveToLast = useCallback(() => {
    if (flattened.length === 0) return
    const lastIndex = flattened.length - 1
    setFocusedIndex(lastIndex)
    onNodeSelect(flattened[lastIndex])
  }, [flattened, onNodeSelect])

  // Keyboard event handler
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    // Only handle if focus is within the tree container
    if (!containerRef.current?.contains(document.activeElement)) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        moveDown()
        break
      
      case 'ArrowUp':
        e.preventDefault()
        moveUp()
        break
      
      case 'ArrowRight':
        e.preventDefault()
        if (focusedNode?.hasChildren) {
          if (focusedNode.isExpanded) {
            moveToFirstChild()
          } else {
            expandNode()
          }
        }
        break
      
      case 'ArrowLeft':
        e.preventDefault()
        if (focusedNode?.hasChildren && focusedNode.isExpanded) {
          collapseNode()
        } else {
          moveToParent()
        }
        break
      
      case 'Enter':
      case ' ':
        e.preventDefault()
        activateNode()
        break
      
      case 'Home':
        e.preventDefault()
        moveToFirst()
        break
      
      case 'End':
        e.preventDefault()
        moveToLast()
        break
      
      case '*':
        e.preventDefault()
        // Expand all folders at current level
        if (focusedNode) {
          const currentDepth = focusedNode.depth
          flattened
            .filter(node => node.depth === currentDepth && node.hasChildren && !node.isExpanded)
            .forEach(node => onToggleExpand(node.path))
        }
        break
      
      default:
        // Type-ahead search
        if (e.key.length === 1 && !e.ctrlKey && !e.metaKey && !e.altKey) {
          e.preventDefault()
          handleTypeAhead(e.key)
        }
        break
    }
  }, [
    moveDown, moveUp, moveToParent, moveToFirstChild,
    expandNode, collapseNode, activateNode,
    moveToFirst, moveToLast, handleTypeAhead,
    focusedNode, flattened, onToggleExpand
  ])

  // Set up keyboard event listeners
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      if (typeAheadTimeoutRef.current) {
        clearTimeout(typeAheadTimeoutRef.current)
      }
    }
  }, [handleKeyDown])

  // Auto-focus first item when nodes change
  useEffect(() => {
    if (flattened.length > 0 && focusedIndex === -1) {
      setFocusedIndex(0)
      onNodeSelect(flattened[0])
    }
  }, [flattened.length, focusedIndex, onNodeSelect])

  // Reset focus when nodes change significantly
  useEffect(() => {
    if (focusedIndex >= flattened.length) {
      const newIndex = Math.max(0, flattened.length - 1)
      setFocusedIndex(newIndex)
      if (flattened[newIndex]) {
        onNodeSelect(flattened[newIndex])
      }
    }
  }, [flattened.length, focusedIndex, onNodeSelect])

  return {
    focusedIndex,
    focusedNode,
    flattenedNodes: flattened,
    typeAheadQuery,
    containerRef,
    
    // Navigation functions
    moveDown,
    moveUp,
    moveToParent,
    moveToFirstChild,
    expandNode,
    collapseNode,
    activateNode,
    moveToFirst,
    moveToLast,
    
    // Manual focus control
    setFocusedIndex: (index: number) => {
      if (index >= 0 && index < flattened.length) {
        setFocusedIndex(index)
        onNodeSelect(flattened[index])
      }
    },
    
    // Focus by path
    focusNodeByPath: (path: string) => {
      const index = flattened.findIndex(node => node.path === path)
      if (index !== -1) {
        setFocusedIndex(index)
        onNodeSelect(flattened[index])
      }
    }
  }
}
