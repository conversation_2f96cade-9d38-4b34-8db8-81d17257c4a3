# Requirements Document

## Introduction

Bu proje, Gemini 2.5 Flash Pro gibi modern AI modellerini entegre eden, Visual Studio Code benzeri bir AI kod editörü uygulamasıdır. <PERSON>y<PERSON>lama, kullan<PERSON><PERSON><PERSON><PERSON><PERSON>n projelerini açabilecekleri, dosya sistemini yönetebilecekleri, kod yazıp düzenleyebilecekleri ve AI chat agent ile etkileşime geçebilecekleri kapsamlı bir geliştirme ortamı sağlayacaktır.

### UI/UX Design Principles

- **Modern ve Minimalist**: <PERSON><PERSON><PERSON>, dü<PERSON>li ve odaklanmış arayüz
- **Responsive Design**: Farklı ekran boyutlarına uyum
- **Dark/Light Theme**: Kullanıcı tercihi ile tema değiştirme
- **Accessibility**: Klavye navigasyonu ve screen reader desteği
- **Performance**: Hızlı yükleme ve smooth animasyonlar
- **Intuitive**: <PERSON>ğren<PERSON> e<PERSON><PERSON>, se<PERSON><PERSON><PERSON> kull<PERSON>

## Requirements

### Requirement 1

**User Story:** <PERSON><PERSON> gelişti<PERSON> o<PERSON>, masaüstündeki projemi açabilmek istiyorum, böylece tüm dosya yapısını ve klasörleri görebilirim.

#### Acceptance Criteria

1. WHEN kullanıcı "Proje Aç" seçeneğini tıkladığında THEN sistem bir klasör seçici dialog açmalıdır
2. WHEN kullanıcı bir klasör seçtiğinde THEN sistem o klasörün tüm dosya ve alt klasörlerini tree view formatında göstermelidir
3. WHEN proje açıldığında THEN sistem VS Code benzeri bir dosya explorer paneli görüntülemelidir
4. IF seçilen klasör geçerli bir proje klasörü değilse THEN sistem uygun bir hata mesajı göstermelidir

### Requirement 2

**User Story:** Bir geliştirici olarak, farklı dosya türlerini (JS, TS, Python, Prisma schema, JSON, vb.) açıp düzenleyebilmek istiyorum, böylece projemde çalışabilirim.

#### Acceptance Criteria

1. WHEN kullanıcı dosya explorer'dan bir dosyaya tıkladığında THEN sistem o dosyayı kod editöründe açmalıdır
2. WHEN dosya açıldığında THEN sistem dosya türüne uygun syntax highlighting uygulamalıdır
3. WHEN kullanıcı dosyada değişiklik yaptığında THEN sistem değişiklikleri gerçek zamanlı olarak göstermelidir
4. WHEN kullanıcı Ctrl+S tuşlarına bastığında THEN sistem dosyayı kaydetmelidir
5. IF desteklenmeyen bir dosya türü açılırsa THEN sistem plain text olarak göstermelidir

### Requirement 3

**User Story:** Bir geliştirici olarak, yeni dosya ve klasörler oluşturabilmek istiyorum, böylece proje yapımı genişletebilirim.

#### Acceptance Criteria

1. WHEN kullanıcı dosya explorer'da sağ tıkladığında THEN sistem "Yeni Dosya" ve "Yeni Klasör" seçenekleri içeren context menu göstermelidir
2. WHEN kullanıcı "Yeni Dosya" seçtiğinde THEN sistem dosya adı girişi için input field göstermelidir
3. WHEN kullanıcı "Yeni Klasör" seçtiğinde THEN sistem klasör adı girişi için input field göstermelidir
4. WHEN kullanıcı geçerli bir ad girip Enter'a bastığında THEN sistem yeni dosya/klasörü oluşturup explorer'da göstermelidir

### Requirement 4

**User Story:** Bir geliştirici olarak, AI chat agent ile etkileşime geçebilmek istiyorum, böylece kod yazma konusunda yardım alabilirim.

#### Acceptance Criteria

1. WHEN uygulama açıldığında THEN sistem bir chat paneli göstermelidir
2. WHEN kullanıcı chat paneline mesaj yazdığında THEN sistem mesajı AI modeline göndermelidir
3. WHEN AI model yanıt verdiğinde THEN sistem yanıtı chat panelinde göstermelidir
4. WHEN AI kod önerisi yaptığında THEN sistem kodu syntax highlighting ile göstermelidir
5. IF AI bağlantısı kesilirse THEN sistem kullanıcıya uygun bir hata mesajı göstermelidir

### Requirement 5

**User Story:** Bir geliştirici olarak, AI agent'ın tüm proje dosyalarımı görebilmesini istiyorum, böylece context-aware yardım alabilirim.

#### Acceptance Criteria

1. WHEN proje açıldığında THEN AI agent tüm dosya içeriklerine erişebilmelidir
2. WHEN kullanıcı AI'dan kod yardımı istediğinde THEN AI mevcut proje yapısını dikkate almalıdır
3. WHEN AI kod önerisi yaptığında THEN öneriler mevcut kod stili ve proje yapısıyla uyumlu olmalıdır
4. WHEN kullanıcı belirli bir dosya hakkında soru sorduğunda THEN AI o dosyanın içeriğini analiz edebilmelidir

### Requirement 6

**User Story:** Bir geliştirici olarak, entegre terminal kullanabilmek ve AI agent'ın terminal komutları çalıştırabilmesini istiyorum, böylece komut satırı işlemlerimi editör içinde yapabilirim.

#### Acceptance Criteria

1. WHEN kullanıcı terminal panelini açtığında THEN sistem işletim sistemine uygun terminal göstermelidir
2. WHEN kullanıcı terminal'de komut çalıştırdığında THEN sistem komutu proje dizininde çalıştırmalıdır
3. WHEN AI agent komut çalıştırması gerektiğinde THEN sistem AI'ın terminal erişimi olmalıdır
4. WHEN AI terminal komutu çalıştırdığında THEN sistem komut çıktısını hem AI'a hem kullanıcıya göstermelidir
5. WHEN komut çıktısı olduğunda THEN sistem çıktıyı terminal panelinde göstermelidir
6. WHEN kullanıcı birden fazla terminal sekmesi açtığında THEN sistem her sekmeyi bağımsız olarak yönetmelidir

### Requirement 7

**User Story:** Bir geliştirici olarak, kod değişikliklerimi canlı olarak görebilmek istiyorum, böylece düzenlemelerimin sonucunu anında takip edebilirim.

#### Acceptance Criteria

1. WHEN kullanıcı kod editöründe yazı yazdığında THEN sistem değişiklikleri gerçek zamanlı olarak göstermelidir
2. WHEN dosyada kaydedilmemiş değişiklikler olduğunda THEN sistem dosya sekmesinde bir işaret göstermelidir
3. WHEN kullanıcı birden fazla dosya açtığında THEN sistem her dosyayı ayrı sekmede göstermelidir
4. WHEN kullanıcı sekmeler arasında geçiş yaptığında THEN sistem aktif dosyayı vurgulamalıdır

### Requirement 8

**User Story:** Bir geliştirici olarak, Gemini 2.5 Flash Pro gibi farklı AI modellerini seçebilmek ve API ayarlarını yönetebilmek istiyorum, böylece ihtiyacıma en uygun modeli kullanabilirim.

#### Acceptance Criteria

1. WHEN uygulama açıldığında THEN sistem ayarlar panelinde model seçim seçeneği göstermelidir
2. WHEN kullanıcı farklı bir model seçtiğinde THEN sistem yeni modele geçiş yapmalıdır
3. WHEN kullanıcı API key girdiğinde THEN sistem key'i güvenli şekilde saklamalıdır
4. WHEN kullanıcı geçersiz API key girdiğinde THEN sistem uygun hata mesajı göstermelidir
5. IF seçilen model erişilebilir değilse THEN sistem uygun bir hata mesajı göstermelidir

### Requirement 9

**User Story:** Bir geliştirici olarak, token kullanımımı ve kalan token miktarımı görebilmek istiyorum, böylece API maliyetlerimi takip edebilirim.

#### Acceptance Criteria

1. WHEN AI ile etkileşim kurduğumda THEN sistem kullanılan token miktarını göstermelidir
2. WHEN chat panelinde THEN sistem güncel token sayacı göstermelidir
3. WHEN token limiti yaklaştığında THEN sistem kullanıcıyı uyarmalıdır
4. WHEN token geçmişi görüntülendiğinde THEN sistem günlük/aylık kullanım istatistikleri göstermelidir

### Requirement 10

**User Story:** Bir geliştirici olarak, AI agent'ın web'den güncel dökümentasyon ve bilgi çekebilmesini istiyorum, böylece en güncel teknoloji bilgilerine erişebilirim.

#### Acceptance Criteria

1. WHEN AI'dan güncel dökümentasyon istediğimde THEN sistem web'den ilgili bilgileri çekmelidir
2. WHEN Google arama yapması gerektiğinde THEN AI Google API'sini kullanmalıdır
3. WHEN bir web sitesinden veri çekmesi gerektiğinde THEN sistem web scraping yapabilmelidir
4. WHEN çekilen veriler gösterildiğinde THEN sistem kaynak linklerini de göstermelidir

### Requirement 11

**User Story:** Bir geliştirici olarak, AI agent'ın Figma tasarımları oluşturabilmesini ve mevcut tasarımları analiz edebilmesini istiyorum, böylece UI/UX geliştirme sürecimi hızlandırabilirim.

#### Acceptance Criteria

1. WHEN AI'dan UI tasarımı istediğimde THEN sistem Figma formatında tasarım oluşturmalıdır
2. WHEN mevcut Figma tasarımını referans verdiğimde THEN AI o stilden esinlenerek yeni bileşenler oluşturmalıdır
3. WHEN tasarım tamamlandığında THEN sistem tasarımı Figma'ya kopyalama seçeneği sunmalıdır
4. WHEN Figma dosyası analiz edildiğinde THEN AI tasarım sistemini ve bileşenleri tanımlayabilmelidir

### Requirement 12

**User Story:** Bir geliştirici olarak, kod refactoring ve optimizasyon önerileri alabılmek istiyorum, böylece kod kalitemi artırabilirim.

#### Acceptance Criteria

1. WHEN mevcut kodu seçtiğimde THEN AI refactoring önerileri sunmalıdır
2. WHEN performans analizi istediğimde THEN sistem kod optimizasyon önerileri vermelidir
3. WHEN kod review istediğimde THEN AI potansiyel hataları ve iyileştirmeleri belirtmelidir
4. WHEN önerilen değişiklikler uygulandığında THEN sistem before/after karşılaştırması göstermelidir

### Requirement 13

**User Story:** Bir geliştirici olarak, AI agent'ın kodumda hataları otomatik tespit edip düzeltme önerileri sunmasını istiyorum, böylece hızlı şekilde hataları çözebilirim.

#### Acceptance Criteria

1. WHEN kod yazarken syntax hatası yaptığımda THEN AI gerçek zamanlı olarak hatayı göstermelidir
2. WHEN runtime hatası oluştuğunda THEN AI hatanın nedenini analiz edip çözüm önermelidir
3. WHEN logic hatası olduğunda THEN AI kodu analiz edip potansiyel sorunları belirtmelidir
4. WHEN AI hata düzeltme önerisi yaptığında THEN sistem tek tıkla düzeltme seçeneği sunmalıdır
5. IF hata çok karmaşıksa THEN AI adım adım debugging rehberi sağlamalıdır

### Requirement 14

**User Story:** Bir geliştirici olarak, gelişmiş debugging araçları kullanabilmek istiyorum, böylece kodumda sorun giderme işlemlerini etkili şekilde yapabilirim.

#### Acceptance Criteria

1. WHEN breakpoint koyduğumda THEN sistem kod çalıştırma sırasında o noktada durmalıdır
2. WHEN debugging modunda THEN sistem değişken değerlerini gerçek zamanlı göstermelidir
3. WHEN step-by-step debugging yaptığımda THEN AI her adımda ne olduğunu açıklamalıdır
4. WHEN call stack görüntülendiğinde THEN sistem fonksiyon çağrı zincirini göstermelidir
5. WHEN memory leak şüphesi olduğunda THEN AI memory kullanımını analiz etmelidir

### Requirement 15

**User Story:** Bir geliştirici olarak, kod kalitesi analizi ve performans optimizasyonu alabılmek istiyorum, böylece daha iyi kod yazabilirim.

#### Acceptance Criteria

1. WHEN kod yazdığımda THEN AI kod kalitesi skorunu gerçek zamanlı göstermelidir
2. WHEN performans sorunu tespit edildiğinde THEN AI optimizasyon önerileri sunmalıdır
3. WHEN kod complexity yüksek olduğunda THEN AI refactoring önerileri vermelidir
4. WHEN security vulnerability bulunduğunda THEN AI güvenlik açığını ve çözümünü göstermelidir
5. WHEN best practices ihlali olduğunda THEN AI uygun coding standards önermelidir

### Requirement 16

**User Story:** Bir geliştirici olarak, proje şablonları ve boilerplate kod oluşturabilmek istiyorum, böylece yeni projelere hızlı başlayabilirim.

#### Acceptance Criteria

1. WHEN yeni proje oluşturduğumda THEN sistem popüler framework şablonları sunmalıdır
2. WHEN özel şablon istediğimde THEN AI proje gereksinimlerime göre boilerplate oluşturmalıdır
3. WHEN şablon seçildiğinde THEN sistem tüm gerekli dosya yapısını ve bağımlılıkları kurmalıdır
4. WHEN şablon özelleştirildiğinde THEN sistem gelecekte kullanım için şablonu kaydetmelidir

### Requirement 17

**User Story:** Bir geliştirici olarak, modern ve sezgisel bir arayüz kullanmak istiyorum, böylece verimli şekilde çalışabilirim.

#### Acceptance Criteria

1. WHEN uygulama açıldığında THEN sistem VS Code benzeri layout ile sidebar, main editor ve panel alanları göstermelidir
2. WHEN kullanıcı panelleri yeniden boyutlandırdığında THEN sistem boyutları kaydetmeli ve hatırlamalıdır
3. WHEN kullanıcı drag&drop ile panelleri taşıdığında THEN sistem yeni düzeni kaydetmelidir
4. WHEN kullanıcı klavye kısayolları kullandığında THEN sistem hızlı navigasyon sağlamalıdır
5. WHEN loading işlemleri olduğunda THEN sistem skeleton loader ve progress indicator göstermelidir

### Requirement 18

**User Story:** Bir geliştirici olarak, responsive ve adaptive bir arayüz kullanmak istiyorum, böylece farklı ekran boyutlarında rahat çalışabilirim.

#### Acceptance Criteria

1. WHEN ekran boyutu küçüldüğünde THEN sistem panelleri otomatik olarak collapse etmelidir
2. WHEN tablet modunda THEN sistem touch-friendly kontroller göstermelidir
3. WHEN yüksek DPI ekranlarda THEN sistem crisp ve net görüntü sağlamalıdır
4. WHEN pencere boyutu değiştiğinde THEN sistem layout'u smooth transition ile uyarlamalıdır
5. WHEN minimum boyutun altına inildiğinde THEN sistem uygun uyarı göstermelidir

### Requirement 19

**User Story:** Bir geliştirici olarak, özelleştirilebilir tema ve görünüm seçenekleri kullanmak istiyorum, böylece kişisel tercihlerime göre çalışabilirim.

#### Acceptance Criteria

1. WHEN tema değiştirildiğinde THEN sistem tüm bileşenlerde tutarlı renk paleti uygulamalıdır
2. WHEN dark mode seçildiğinde THEN sistem göz yorgunluğunu azaltan koyu tonlar kullanmalıdır
3. WHEN font boyutu değiştirildiğinde THEN sistem tüm metin alanlarında tutarlı ölçekleme yapmalıdır
4. WHEN custom theme yüklendiğinde THEN sistem kullanıcı tanımlı renkleri uygulamalıdır
5. WHEN high contrast mode aktif olduğunda THEN sistem accessibility standartlarına uygun kontrastlar kullanmalıdır

### Requirement 20

**User Story:** Bir geliştirici olarak, hızlı ve akıcı animasyonlar görmek istiyorum, böylece premium bir deneyim yaşayabilirim.

#### Acceptance Criteria

1. WHEN panel açılıp kapandığında THEN sistem smooth slide animasyonları göstermelidir
2. WHEN dosya sekmesi değiştirildiğinde THEN sistem fade transition efekti uygulamalıdır
3. WHEN hover efektleri olduğunda THEN sistem subtle scale ve color transitions göstermelidir
4. WHEN loading durumlarında THEN sistem engaging micro-animations kullanmalıdır
5. WHEN animasyonlar disabled edildiğinde THEN sistem instant transitions kullanmalıdır

### Requirement 21

**User Story:** Bir geliştirici olarak, accessibility özelliklerini kullanabilmek istiyorum, böylece engelli kullanıcılar da uygulamayı kullanabilsin.

#### Acceptance Criteria

1. WHEN klavye navigasyonu kullanıldığında THEN sistem tüm interaktif elementlere erişim sağlamalıdır
2. WHEN screen reader kullanıldığında THEN sistem uygun ARIA labels ve descriptions sağlamalıdır
3. WHEN tab tuşu kullanıldığında THEN sistem logical focus order takip etmelidir
4. WHEN high contrast mode aktif olduğunda THEN sistem WCAG AA standartlarına uymalıdır
5. WHEN voice control kullanıldığında THEN sistem voice commands'leri desteklemelidir

### Requirement 22

**User Story:** Bir geliştirici olarak, context-aware menüler ve shortcuts kullanmak istiyorum, böylece hızlı işlem yapabilirim.

#### Acceptance Criteria

1. WHEN sağ tık yapıldığında THEN sistem context'e uygun menu seçenekleri göstermelidir
2. WHEN command palette açıldığında THEN sistem fuzzy search ile komutları filtrelemelidir
3. WHEN keyboard shortcuts kullanıldığında THEN sistem instant response sağlamalıdır
4. WHEN custom shortcuts tanımlandığında THEN sistem conflict detection yapmalıdır
5. WHEN tooltip gösterildiğinde THEN sistem keyboard shortcut bilgilerini de içermelidir

### Requirement 23

**User Story:** Bir geliştirici olarak, smart notifications ve feedback sistemi kullanmak istiyorum, böylece sistem durumunu anlayabilirim.

#### Acceptance Criteria

1. WHEN işlem başarılı olduğunda THEN sistem subtle success notification göstermelidir
2. WHEN hata oluştuğunda THEN sistem clear error message ve action suggestions sunmalıdır
3. WHEN background işlem çalıştığında THEN sistem progress indicator göstermelidir
4. WHEN notification çok fazla olduğunda THEN sistem notification grouping yapmalıdır
5. WHEN critical error olduğunda THEN sistem modal dialog ile immediate attention çekmelidir

### Requirement 24

**User Story:** Bir geliştirici olarak, advanced search ve filtering özellikleri kullanmak istiyorum, böylece büyük projelerde hızlı navigasyon yapabilirim.

#### Acceptance Criteria

1. WHEN global search yapıldığında THEN sistem file content, file names ve symbols'da arama yapmalıdır
2. WHEN search results gösterildiğinde THEN sistem relevance score ile sıralama yapmalıdır
3. WHEN filter uygulandığında THEN sistem real-time filtering sağlamalıdır
4. WHEN search history görüntülendiğinde THEN sistem recent searches göstermelidir
5. WHEN regex search kullanıldığında THEN sistem advanced pattern matching desteklemelidir

### Requirement 25

**User Story:** Bir geliştirici olarak, projemi anlık olarak preview edebilmek istiyorum, böylece değişikliklerin sonucunu gerçek zamanlı görebilirim.

#### Acceptance Criteria

1. WHEN preview sekmesi açıldığında THEN sistem projeyi live preview modunda göstermelidir
2. WHEN kod değişikliği yapıldığında THEN sistem preview'ı otomatik olarak güncellemeli
3. WHEN farklı cihaz boyutları seçildiğinde THEN sistem responsive preview göstermelidir
4. WHEN hot reload aktif olduğunda THEN sistem değişiklikleri anında yansıtmalıdır
5. WHEN preview error oluştuğunda THEN sistem detaylı hata mesajı ve çözüm önerisi göstermelidir

### Requirement 26

**User Story:** Bir geliştirici olarak, AI agent'ın benim talimatlarıma göre task oluşturabilmesini istiyorum, böylece proje yönetimimi otomatikleştirebilirim.

#### Acceptance Criteria

1. WHEN AI'a proje talimatı verdiğimde THEN sistem talimatı analiz edip task listesi oluşturmalıdır
2. WHEN task oluşturulduğunda THEN sistem priority, deadline ve dependency bilgilerini içermelidir
3. WHEN task akışı görüntülendiğinde THEN sistem Kanban board benzeri modern UI göstermelidir
4. WHEN task tamamlandığında THEN sistem otomatik olarak next task'ı önermelidir
5. WHEN task conflict oluştuğunda THEN sistem dependency çözümü için önerilerde bulunmalıdır

### Requirement 27

**User Story:** Bir geliştirici olarak, gelişmiş context engineering sistemi kullanmak istiyorum, böylece AI agent'ın daha akıllı ve context-aware yanıtlar vermesini sağlayabilirim.

#### Acceptance Criteria

1. WHEN proje analiz edildiğinde THEN sistem codebase'in semantic structure'ını çıkarmalıdır
2. WHEN AI ile konuştuğumda THEN sistem relevant context'i otomatik olarak dahil etmelidir
3. WHEN context çok büyük olduğunda THEN sistem intelligent context pruning yapmalıdır
4. WHEN farklı dosyalar arasında ilişki olduğunda THEN sistem cross-reference context sağlamalıdır
5. WHEN context history tutulduğunda THEN sistem conversation memory ile learning yapmalıdır

### Requirement 28

**User Story:** Bir geliştirici olarak, AI agent'ın profesyonel kalitede Figma tasarımları oluşturabilmesini istiyorum, böylece production-ready UI/UX tasarımları alabilirim.

#### Acceptance Criteria

1. WHEN Figma tasarımı istendiğinde THEN sistem modern design principles'a uygun tasarım oluşturmalıdır
2. WHEN design system kullanıldığında THEN sistem consistent colors, typography ve spacing uygulamalıdır
3. WHEN component library oluşturulduğunda THEN sistem reusable ve scalable bileşenler tasarlamalıdır
4. WHEN accessibility göz önünde bulundurulduğunda THEN sistem WCAG guidelines'a uygun tasarım yapmalıdır
5. WHEN tasarım export edildiğinde THEN sistem developer-friendly format ve annotations sağlamalıdır

### Requirement 29

**User Story:** Bir geliştirici olarak, AI agent'ın gelişmiş algoritma ve reasoning yetenekleri kullanmasını istiyorum, böylece daha akıllı ve doğru çözümler alabilirim.

#### Acceptance Criteria

1. WHEN karmaşık problem çözümü istendiğinde THEN AI multi-step reasoning yaklaşımı kullanmalıdır
2. WHEN kod analizi yapıldığında THEN sistem pattern recognition ve best practices detection uygulamalıdır
3. WHEN optimization önerileri verildiğinde THEN sistem performance impact analysis yapmalıdır
4. WHEN debugging yapıldığında THEN sistem root cause analysis ve systematic troubleshooting kullanmalıdır
5. WHEN learning yapıldığında THEN sistem user feedback'i ile continuous improvement sağlamalıdır

### Requirement 30

**User Story:** Bir geliştirici olarak, AI agent için özelleştirilebilir kural sistemi kullanmak istiyorum, böylece AI'ın davranışını kendi ihtiyaçlarıma göre ayarlayabilirim.

#### Acceptance Criteria

1. WHEN kural oluşturduğumda THEN sistem kuralı AI agent'ın tüm etkileşimlerinde uygulamalıdır
2. WHEN dil kuralı belirlediğimde THEN AI sadece belirtilen dilde yanıt vermelidir
3. WHEN kod stili kuralı koyduğumda THEN AI önerilerinde bu stili takip etmelidir
4. WHEN kural çakışması olduğunda THEN sistem priority sırasına göre kuralları uygulamalıdır
5. WHEN kural ihlali tespit edildiğinde THEN sistem kullanıcıyı bilgilendirmeli ve düzeltme önermelidir
6. WHEN kural şablonları kullanıldığında THEN sistem popüler kural setlerini (company standards, framework guidelines) sunmalıdır
7. WHEN kural geçmişi görüntülendiğinde THEN sistem hangi kuralların ne zaman uygulandığını göstermelidir

### Requirement 31

**User Story:** Bir geliştirici olarak, AI agent'ın tam otonom bir şekilde çalışabilmesini istiyorum, böylece minimal müdahale ile karmaşık görevleri tamamlayabilsin.

#### Acceptance Criteria

1. WHEN AI agent'a bir görev verildiğinde THEN sistem hangi dosyaları düzenlemesi gerektiğini otomatik olarak belirlemeli
2. WHEN terminal komutu çalıştırması gerektiğinde THEN AI hangi dizinde hangi komutu çalıştıracağını otomatik analiz etmelidir
3. WHEN kod yazması gerektiğinde THEN AI proje yapısını analiz edip en uygun dosya konumunu seçmelidir
4. WHEN dependency kurulumu gerektiğinde THEN AI package manager'ı (npm, yarn, pip) otomatik tespit edip kurulum yapmalıdır
5. WHEN hata oluştuğunda THEN AI hatayı analiz edip otomatik düzeltme adımları uygulamalıdır
6. WHEN proje build edilmesi gerektiğinde THEN AI build sistemini tespit edip uygun komutları çalıştırmalıdır
7. WHEN test çalıştırması gerektiğinde THEN AI test framework'ünü tespit edip testleri otomatik çalıştırmalıdır
8. WHEN dosya yapısı değişikliği gerektiğinde THEN AI import/export path'lerini otomatik güncellemeli
9. WHEN environment setup gerektiğinde THEN AI .env dosyalarını ve konfigürasyonları otomatik oluşturmalıdır
10. WHEN deployment hazırlığı yapılacağında THEN AI production build ve deployment scriptlerini otomatik hazırlamalıdır