/**
 * Key Manager for secure API key storage and validation
 */
export class KeyManager {
  private static instance: KeyManager | null = null
  private keyValidationCache: Map<string, { isValid: boolean; timestamp: number }> = new Map()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  private constructor() {}

  static getInstance(): KeyManager {
    if (!KeyManager.instance) {
      KeyManager.instance = new KeyManager()
    }
    return KeyManager.instance
  }

  // API Key validation
  async validateApiKey(provider: string, apiKey: string): Promise<boolean> {
    const cacheKey = `${provider}:${this.hashKey(apiKey)}`
    const cached = this.keyValidationCache.get(cacheKey)

    // Return cached result if still valid
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.isValid
    }

    let isValid = false

    try {
      switch (provider.toLowerCase()) {
        case 'gemini':
          isValid = await this.validateGeminiKey(apiK<PERSON>)
          break
        case 'openai':
          isValid = await this.validateOpenAIKey(apiKey)
          break
        default:
          isValid = this.validateKeyFormat(apiKey)
      }
    } catch (error) {
      console.warn(`API key validation failed for ${provider}:`, error)
      isValid = false
    }

    // Cache the result
    this.keyValidationCache.set(cacheKey, {
      isValid,
      timestamp: Date.now()
    })

    return isValid
  }

  // Provider-specific validation methods
  private async validateGeminiKey(apiKey: string): Promise<boolean> {
    // Gemini API keys typically start with 'AIza' and are 39 characters long
    if (!apiKey.startsWith('AIza') || apiKey.length !== 39) {
      return false
    }

    try {
      // Test the key with a minimal API call
      const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models?key=' + apiKey)
      return response.ok
    } catch (error) {
      return false
    }
  }

  private async validateOpenAIKey(apiKey: string): Promise<boolean> {
    // OpenAI API keys start with 'sk-' and are typically 51 characters long
    if (!apiKey.startsWith('sk-') || apiKey.length !== 51) {
      return false
    }

    try {
      // Test the key with a minimal API call
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      })
      return response.ok
    } catch (error) {
      return false
    }
  }

  // Generic key format validation
  private validateKeyFormat(apiKey: string): boolean {
    // Basic validation: key should be at least 20 characters and contain alphanumeric characters
    if (apiKey.length < 20) {
      return false
    }

    // Should contain only valid characters (letters, numbers, hyphens, underscores)
    const validPattern = /^[a-zA-Z0-9\-_]+$/
    return validPattern.test(apiKey)
  }

  // Key security utilities
  maskApiKey(apiKey: string): string {
    if (apiKey.length <= 8) {
      return '*'.repeat(apiKey.length)
    }

    const start = apiKey.substring(0, 4)
    const end = apiKey.substring(apiKey.length - 4)
    const middle = '*'.repeat(apiKey.length - 8)

    return start + middle + end
  }

  getKeyStrength(apiKey: string): 'weak' | 'medium' | 'strong' {
    if (apiKey.length < 20) return 'weak'
    if (apiKey.length < 32) return 'medium'
    return 'strong'
  }

  // Key rotation and management
  generateKeyId(provider: string, apiKey: string): string {
    const hash = this.hashKey(apiKey)
    return `${provider}_${hash.substring(0, 8)}`
  }

  // Security checks
  checkKeyCompromise(apiKey: string): Promise<boolean> {
    // In a real implementation, this could check against known compromised keys
    // For now, just check basic security patterns
    return Promise.resolve(this.hasSecurityIssues(apiKey))
  }

  private hasSecurityIssues(apiKey: string): boolean {
    // Check for common security issues
    const issues = [
      apiKey.includes('test'),
      apiKey.includes('demo'),
      apiKey.includes('example'),
      apiKey === 'your-api-key-here',
      apiKey.length < 16,
      /^[0-9]+$/.test(apiKey), // Only numbers
      /^[a-zA-Z]+$/.test(apiKey), // Only letters
    ]

    return issues.some(issue => issue)
  }

  // Key metadata and analytics
  getKeyMetadata(provider: string, apiKey: string): KeyMetadata {
    return {
      provider,
      keyId: this.generateKeyId(provider, apiKey),
      maskedKey: this.maskApiKey(apiKey),
      strength: this.getKeyStrength(apiKey),
      length: apiKey.length,
      format: this.detectKeyFormat(apiKey),
      createdAt: new Date(),
      lastValidated: this.getLastValidationTime(provider, apiKey)
    }
  }

  private detectKeyFormat(apiKey: string): string {
    if (apiKey.startsWith('AIza')) return 'Google API Key'
    if (apiKey.startsWith('sk-')) return 'OpenAI API Key'
    if (apiKey.startsWith('pk_')) return 'Stripe Public Key'
    if (apiKey.startsWith('sk_')) return 'Stripe Secret Key'
    return 'Unknown Format'
  }

  private getLastValidationTime(provider: string, apiKey: string): Date | null {
    const cacheKey = `${provider}:${this.hashKey(apiKey)}`
    const cached = this.keyValidationCache.get(cacheKey)
    return cached ? new Date(cached.timestamp) : null
  }

  // Utility methods
  private hashKey(apiKey: string): string {
    // Simple hash function for caching (not cryptographically secure)
    let hash = 0
    for (let i = 0; i < apiKey.length; i++) {
      const char = apiKey.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  // Cache management
  clearValidationCache(): void {
    this.keyValidationCache.clear()
  }

  getCacheStats(): { size: number; oldestEntry: Date | null } {
    let oldestTimestamp = Date.now()

    for (const entry of this.keyValidationCache.values()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp
      }
    }

    return {
      size: this.keyValidationCache.size,
      oldestEntry: this.keyValidationCache.size > 0 ? new Date(oldestTimestamp) : null
    }
  }

  // Cleanup expired cache entries
  cleanupCache(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    for (const [key, entry] of this.keyValidationCache.entries()) {
      if (now - entry.timestamp > this.CACHE_DURATION) {
        expiredKeys.push(key)
      }
    }

    for (const key of expiredKeys) {
      this.keyValidationCache.delete(key)
    }
  }
}

// Supporting interfaces
interface KeyMetadata {
  provider: string
  keyId: string
  maskedKey: string
  strength: 'weak' | 'medium' | 'strong'
  length: number
  format: string
  createdAt: Date
  lastValidated: Date | null
}