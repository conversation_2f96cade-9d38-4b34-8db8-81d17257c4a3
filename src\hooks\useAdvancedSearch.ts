/**
 * Advanced Search Hook
 * Comprehensive search functionality with regex, glob patterns, content search, and filtering
 */

import { useState, useCallback, useEffect, useMemo, useRef } from 'react'
import { FileNode } from '../types'
import { getFileTypeInfo, getFileCategory } from '../utils/fileTypeDetection'

export interface SearchOptions {
  query: string
  searchType: 'name' | 'content' | 'both'
  useRegex: boolean
  useGlob: boolean
  caseSensitive: boolean
  wholeWord: boolean
  includeHidden: boolean
  maxResults: number
}

export interface FilterOptions {
  fileTypes: string[]
  categories: string[]
  sizeRange: { min: number; max: number } | null
  dateRange: { start: Date; end: Date } | null
  excludePatterns: string[]
  includePatterns: string[]
}

export interface SearchResult {
  node: FileNode
  matches: SearchMatch[]
  score: number
  relevance: number
}

export interface SearchMatch {
  type: 'filename' | 'content' | 'path'
  text: string
  startIndex: number
  endIndex: number
  lineNumber?: number
  context?: string
}

export interface SearchHistory {
  id: string
  query: string
  options: SearchOptions
  filters: FilterOptions
  timestamp: Date
  resultCount: number
  isFavorite: boolean
}

export interface UseAdvancedSearchOptions {
  nodes: FileNode[]
  onSearchContent?: (filePath: string, query: string) => Promise<SearchMatch[]>
  debounceMs?: number
  maxHistoryItems?: number
}

export const useAdvancedSearch = (options: UseAdvancedSearchOptions) => {
  const {
    nodes,
    onSearchContent,
    debounceMs = 300,
    maxHistoryItems = 50
  } = options

  const [searchOptions, setSearchOptions] = useState<SearchOptions>({
    query: '',
    searchType: 'name',
    useRegex: false,
    useGlob: false,
    caseSensitive: false,
    wholeWord: false,
    includeHidden: false,
    maxResults: 100
  })

  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    fileTypes: [],
    categories: [],
    sizeRange: null,
    dateRange: null,
    excludePatterns: [],
    includePatterns: []
  })

  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([])
  const [searchStats, setSearchStats] = useState({
    totalFiles: 0,
    searchedFiles: 0,
    matchedFiles: 0,
    searchTime: 0
  })

  const searchTimeoutRef = useRef<NodeJS.Timeout>()
  const searchIndexRef = useRef<Map<string, string>>(new Map())

  // Build search index for performance
  const buildSearchIndex = useCallback(async () => {
    const index = new Map<string, string>()
    
    const indexNode = (node: FileNode) => {
      // Index file name and path
      index.set(node.path, node.name.toLowerCase())
      
      // Index file content if it's a text file
      if (node.type === 'file' && getFileTypeInfo(node.name).isText) {
        // In a real implementation, this would read file content
        // For now, we'll just index the filename
        index.set(`${node.path}:content`, node.name.toLowerCase())
      }
      
      // Recursively index children
      if (node.children) {
        node.children.forEach(indexNode)
      }
    }

    nodes.forEach(indexNode)
    searchIndexRef.current = index
  }, [nodes])

  // Build index when nodes change
  useEffect(() => {
    buildSearchIndex()
  }, [buildSearchIndex])

  // Fuzzy matching algorithm
  const fuzzyMatch = useCallback((query: string, text: string, caseSensitive = false): number => {
    if (!query || !text) return 0

    const q = caseSensitive ? query : query.toLowerCase()
    const t = caseSensitive ? text : text.toLowerCase()

    let score = 0
    let queryIndex = 0
    let lastMatchIndex = -1

    for (let i = 0; i < t.length && queryIndex < q.length; i++) {
      if (t[i] === q[queryIndex]) {
        score += 1
        
        // Bonus for consecutive matches
        if (i === lastMatchIndex + 1) {
          score += 0.5
        }
        
        // Bonus for word boundary matches
        if (i === 0 || t[i - 1] === ' ' || t[i - 1] === '/' || t[i - 1] === '.') {
          score += 0.3
        }
        
        lastMatchIndex = i
        queryIndex++
      }
    }

    // Return normalized score (0-1)
    return queryIndex === q.length ? score / (q.length + t.length) : 0
  }, [])

  // Glob pattern matching
  const globMatch = useCallback((pattern: string, text: string): boolean => {
    const regexPattern = pattern
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.')
    
    const regex = new RegExp(`^${regexPattern}$`, 'i')
    return regex.test(text)
  }, [])

  // Regex pattern matching
  const regexMatch = useCallback((pattern: string, text: string, caseSensitive: boolean): RegExpMatchArray | null => {
    try {
      const flags = caseSensitive ? 'g' : 'gi'
      const regex = new RegExp(pattern, flags)
      return text.match(regex)
    } catch {
      return null
    }
  }, [])

  // Search in file name
  const searchFileName = useCallback((node: FileNode, query: string, options: SearchOptions): SearchMatch[] => {
    const matches: SearchMatch[] = []
    const { caseSensitive, useRegex, useGlob, wholeWord } = options

    let searchText = node.name
    let searchQuery = query

    if (!caseSensitive) {
      searchText = searchText.toLowerCase()
      searchQuery = searchQuery.toLowerCase()
    }

    if (useRegex) {
      const regexMatches = regexMatch(query, node.name, caseSensitive)
      if (regexMatches) {
        regexMatches.forEach(match => {
          const startIndex = node.name.indexOf(match)
          matches.push({
            type: 'filename',
            text: match,
            startIndex,
            endIndex: startIndex + match.length
          })
        })
      }
    } else if (useGlob) {
      if (globMatch(query, node.name)) {
        matches.push({
          type: 'filename',
          text: node.name,
          startIndex: 0,
          endIndex: node.name.length
        })
      }
    } else {
      // Simple text search
      if (wholeWord) {
        const wordRegex = new RegExp(`\\b${searchQuery}\\b`, caseSensitive ? 'g' : 'gi')
        const regexMatches = searchText.match(wordRegex)
        if (regexMatches) {
          regexMatches.forEach(match => {
            const startIndex = searchText.indexOf(match)
            matches.push({
              type: 'filename',
              text: match,
              startIndex,
              endIndex: startIndex + match.length
            })
          })
        }
      } else {
        const index = searchText.indexOf(searchQuery)
        if (index !== -1) {
          matches.push({
            type: 'filename',
            text: searchQuery,
            startIndex: index,
            endIndex: index + searchQuery.length
          })
        }
      }
    }

    return matches
  }, [regexMatch, globMatch])

  // Search in file path
  const searchFilePath = useCallback((node: FileNode, query: string, options: SearchOptions): SearchMatch[] => {
    const matches: SearchMatch[] = []
    const { caseSensitive } = options

    let searchText = node.path
    let searchQuery = query

    if (!caseSensitive) {
      searchText = searchText.toLowerCase()
      searchQuery = searchQuery.toLowerCase()
    }

    const index = searchText.indexOf(searchQuery)
    if (index !== -1) {
      matches.push({
        type: 'path',
        text: searchQuery,
        startIndex: index,
        endIndex: index + searchQuery.length
      })
    }

    return matches
  }, [])

  // Apply filters to node
  const applyFilters = useCallback((node: FileNode, filters: FilterOptions): boolean => {
    const {
      fileTypes,
      categories,
      sizeRange,
      dateRange,
      excludePatterns,
      includePatterns
    } = filters

    // File type filter
    if (fileTypes.length > 0) {
      const extension = node.name.split('.').pop()?.toLowerCase() || ''
      if (!fileTypes.includes(extension)) return false
    }

    // Category filter
    if (categories.length > 0) {
      const category = getFileCategory(node.name)
      if (!categories.includes(category)) return false
    }

    // Size filter
    if (sizeRange && node.size !== undefined) {
      if (node.size < sizeRange.min || node.size > sizeRange.max) return false
    }

    // Date filter
    if (dateRange && node.modified) {
      const modifiedDate = new Date(node.modified)
      if (modifiedDate < dateRange.start || modifiedDate > dateRange.end) return false
    }

    // Exclude patterns
    if (excludePatterns.length > 0) {
      for (const pattern of excludePatterns) {
        if (globMatch(pattern, node.name)) return false
      }
    }

    // Include patterns (if specified, file must match at least one)
    if (includePatterns.length > 0) {
      let matchesInclude = false
      for (const pattern of includePatterns) {
        if (globMatch(pattern, node.name)) {
          matchesInclude = true
          break
        }
      }
      if (!matchesInclude) return false
    }

    // Hidden files filter
    if (!searchOptions.includeHidden && node.name.startsWith('.')) {
      return false
    }

    return true
  }, [searchOptions.includeHidden, globMatch])

  // Perform search
  const performSearch = useCallback(async (
    query: string,
    searchOpts: SearchOptions,
    filterOpts: FilterOptions
  ): Promise<SearchResult[]> => {
    if (!query.trim()) return []

    const startTime = Date.now()
    const results: SearchResult[] = []
    let searchedFiles = 0
    let matchedFiles = 0

    const searchNode = async (node: FileNode): Promise<void> => {
      searchedFiles++

      // Apply filters first
      if (!applyFilters(node, filterOpts)) return

      const matches: SearchMatch[] = []
      let score = 0

      // Search in filename
      if (searchOpts.searchType === 'name' || searchOpts.searchType === 'both') {
        const nameMatches = searchFileName(node, query, searchOpts)
        matches.push(...nameMatches)
        
        // Add fuzzy matching score for filename
        const fuzzyScore = fuzzyMatch(query, node.name, searchOpts.caseSensitive)
        score += fuzzyScore * 2 // Weight filename matches higher
      }

      // Search in file path
      const pathMatches = searchFilePath(node, query, searchOpts)
      matches.push(...pathMatches)
      score += pathMatches.length * 0.5

      // Search in file content
      if (searchOpts.searchType === 'content' || searchOpts.searchType === 'both') {
        if (node.type === 'file' && onSearchContent) {
          try {
            const contentMatches = await onSearchContent(node.path, query)
            matches.push(...contentMatches)
            score += contentMatches.length * 1.5 // Weight content matches
          } catch (error) {
            console.warn('Content search failed for', node.path, error)
          }
        }
      }

      // Calculate relevance score
      const relevance = score + (matches.length * 0.1)

      if (matches.length > 0) {
        matchedFiles++
        results.push({
          node,
          matches,
          score,
          relevance
        })
      }

      // Search in children
      if (node.children) {
        for (const child of node.children) {
          await searchNode(child)
        }
      }
    }

    // Search all nodes
    for (const node of nodes) {
      await searchNode(node)
    }

    // Sort by relevance and limit results
    const sortedResults = results
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, searchOpts.maxResults)

    const searchTime = Date.now() - startTime

    setSearchStats({
      totalFiles: searchedFiles,
      searchedFiles,
      matchedFiles,
      searchTime
    })

    return sortedResults
  }, [nodes, applyFilters, searchFileName, searchFilePath, fuzzyMatch, onSearchContent])

  // Debounced search
  const debouncedSearch = useCallback((
    query: string,
    searchOpts: SearchOptions,
    filterOpts: FilterOptions
  ) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current)
    }

    searchTimeoutRef.current = setTimeout(async () => {
      if (!query.trim()) {
        setSearchResults([])
        setIsSearching(false)
        return
      }

      setIsSearching(true)
      try {
        const results = await performSearch(query, searchOpts, filterOpts)
        setSearchResults(results)
      } catch (error) {
        console.error('Search failed:', error)
        setSearchResults([])
      } finally {
        setIsSearching(false)
      }
    }, debounceMs)
  }, [performSearch, debounceMs])

  // Update search options
  const updateSearchOptions = useCallback((newOptions: Partial<SearchOptions>) => {
    const updatedOptions = { ...searchOptions, ...newOptions }
    setSearchOptions(updatedOptions)
    
    if (updatedOptions.query) {
      debouncedSearch(updatedOptions.query, updatedOptions, filterOptions)
    }
  }, [searchOptions, filterOptions, debouncedSearch])

  // Update filter options
  const updateFilterOptions = useCallback((newFilters: Partial<FilterOptions>) => {
    const updatedFilters = { ...filterOptions, ...newFilters }
    setFilterOptions(updatedFilters)
    
    if (searchOptions.query) {
      debouncedSearch(searchOptions.query, searchOptions, updatedFilters)
    }
  }, [filterOptions, searchOptions, debouncedSearch])

  // Add to search history
  const addToHistory = useCallback((
    query: string,
    options: SearchOptions,
    filters: FilterOptions,
    resultCount: number
  ) => {
    const historyItem: SearchHistory = {
      id: Date.now().toString(),
      query,
      options: { ...options },
      filters: { ...filters },
      timestamp: new Date(),
      resultCount,
      isFavorite: false
    }

    setSearchHistory(prev => {
      const filtered = prev.filter(item => item.query !== query)
      const newHistory = [historyItem, ...filtered].slice(0, maxHistoryItems)
      return newHistory
    })
  }, [maxHistoryItems])

  // Toggle favorite
  const toggleFavorite = useCallback((historyId: string) => {
    setSearchHistory(prev =>
      prev.map(item =>
        item.id === historyId
          ? { ...item, isFavorite: !item.isFavorite }
          : item
      )
    )
  }, [])

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchOptions(prev => ({ ...prev, query: '' }))
    setSearchResults([])
    setIsSearching(false)
    
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current)
    }
  }, [])

  // Cleanup
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current)
      }
    }
  }, [])

  return {
    // State
    searchOptions,
    filterOptions,
    searchResults,
    isSearching,
    searchHistory,
    searchStats,

    // Actions
    updateSearchOptions,
    updateFilterOptions,
    addToHistory,
    toggleFavorite,
    clearSearch,

    // Utilities
    fuzzyMatch,
    globMatch,
    regexMatch,

    // Computed values
    hasResults: searchResults.length > 0,
    hasQuery: searchOptions.query.trim().length > 0,
    favoriteSearches: searchHistory.filter(item => item.isFavorite),
    recentSearches: searchHistory.filter(item => !item.isFavorite).slice(0, 10)
  }
}
