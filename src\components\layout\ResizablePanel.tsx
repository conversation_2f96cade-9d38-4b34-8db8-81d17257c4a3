/**
 * Resizable Panel Component
 * Provides drag-to-resize functionality for layout panels
 */

import React, { useRef, useCallback, useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '../../utils'
import { PanelPosition } from '../../stores/layoutStore'

interface ResizablePanelProps {
  children: React.ReactNode
  position: PanelPosition
  size: number
  minSize: number
  maxSize: number
  isVisible: boolean
  isCollapsed: boolean
  isResizable: boolean
  onResize: (size: number) => void
  onToggleCollapse?: () => void
  className?: string
  id?: string
}

export function ResizablePanel({
  children,
  position,
  size,
  minSize,
  maxSize,
  isVisible,
  isCollapsed,
  isResizable,
  onResize,
  onToggleCollapse,
  className,
  id,
}: ResizablePanelProps) {
  const panelRef = useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0, size: 0 })

  const isHorizontal = position === 'left' || position === 'right'
  const isVertical = position === 'top' || position === 'bottom'

  // Handle mouse down on resize handle
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!isResizable) return

    e.preventDefault()
    setIsDragging(true)
    setDragStart({
      x: e.clientX,
      y: e.clientY,
      size: size,
    })

    // Add global mouse event listeners
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return

      const deltaX = e.clientX - dragStart.x
      const deltaY = e.clientY - dragStart.y
      
      let newSize = dragStart.size
      
      if (isHorizontal) {
        if (position === 'left') {
          newSize = dragStart.size + deltaX
        } else {
          newSize = dragStart.size - deltaX
        }
      } else if (isVertical) {
        if (position === 'top') {
          newSize = dragStart.size + deltaY
        } else {
          newSize = dragStart.size - deltaY
        }
      }

      // Clamp size within bounds
      newSize = Math.max(minSize, Math.min(maxSize, newSize))
      onResize(newSize)
    }

    const handleMouseUp = () => {
      setIsDragging(false)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }, [isResizable, size, dragStart, isDragging, position, minSize, maxSize, onResize])

  // Handle double-click to toggle collapse
  const handleDoubleClick = useCallback(() => {
    if (onToggleCollapse) {
      onToggleCollapse()
    }
  }, [onToggleCollapse])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!panelRef.current?.contains(document.activeElement)) return

      // Ctrl/Cmd + [ to collapse
      if ((e.ctrlKey || e.metaKey) && e.key === '[') {
        e.preventDefault()
        if (onToggleCollapse && !isCollapsed) {
          onToggleCollapse()
        }
      }
      // Ctrl/Cmd + ] to expand
      else if ((e.ctrlKey || e.metaKey) && e.key === ']') {
        e.preventDefault()
        if (onToggleCollapse && isCollapsed) {
          onToggleCollapse()
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onToggleCollapse, isCollapsed])

  // Panel style based on position and size
  const panelStyle = {
    ...(isHorizontal && {
      width: isCollapsed ? 'auto' : `${size}px`,
      minWidth: isCollapsed ? 'auto' : `${minSize}px`,
      maxWidth: isCollapsed ? 'auto' : `${maxSize}px`,
    }),
    ...(isVertical && {
      height: isCollapsed ? 'auto' : `${size}px`,
      minHeight: isCollapsed ? 'auto' : `${minSize}px`,
      maxHeight: isCollapsed ? 'auto' : `${maxSize}px`,
    }),
  }

  // Resize handle position and cursor
  const getResizeHandleProps = () => {
    const baseClasses = 'absolute bg-border hover:bg-accent transition-colors z-10'
    
    switch (position) {
      case 'left':
        return {
          className: cn(baseClasses, 'top-0 right-0 w-1 h-full cursor-col-resize'),
          style: { right: '-2px' },
        }
      case 'right':
        return {
          className: cn(baseClasses, 'top-0 left-0 w-1 h-full cursor-col-resize'),
          style: { left: '-2px' },
        }
      case 'top':
        return {
          className: cn(baseClasses, 'bottom-0 left-0 w-full h-1 cursor-row-resize'),
          style: { bottom: '-2px' },
        }
      case 'bottom':
        return {
          className: cn(baseClasses, 'top-0 left-0 w-full h-1 cursor-row-resize'),
          style: { top: '-2px' },
        }
      default:
        return { className: '', style: {} }
    }
  }

  const resizeHandleProps = getResizeHandleProps()

  // Animation variants
  const panelVariants = {
    visible: {
      opacity: 1,
      ...(isHorizontal && { width: isCollapsed ? 'auto' : size }),
      ...(isVertical && { height: isCollapsed ? 'auto' : size }),
    },
    hidden: {
      opacity: 0,
      ...(isHorizontal && { width: 0 }),
      ...(isVertical && { height: 0 }),
    },
    collapsed: {
      opacity: 1,
      ...(isHorizontal && { width: 'auto' }),
      ...(isVertical && { height: 'auto' }),
    },
  }

  if (!isVisible) {
    return null
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        ref={panelRef}
        id={id}
        className={cn(
          'relative flex-shrink-0 bg-card border-border',
          position === 'left' && 'border-r',
          position === 'right' && 'border-l',
          position === 'top' && 'border-b',
          position === 'bottom' && 'border-t',
          isDragging && 'select-none',
          className
        )}
        style={panelStyle}
        variants={panelVariants}
        initial="hidden"
        animate={isCollapsed ? 'collapsed' : 'visible'}
        exit="hidden"
        transition={{
          type: 'spring',
          stiffness: 300,
          damping: 30,
          duration: 0.2,
        }}
      >
        {/* Panel Content */}
        <div className="flex flex-col h-full w-full overflow-hidden">
          {children}
        </div>

        {/* Resize Handle */}
        {isResizable && !isCollapsed && (
          <div
            {...resizeHandleProps}
            onMouseDown={handleMouseDown}
            onDoubleClick={handleDoubleClick}
            role="separator"
            aria-label={`Resize ${position} panel`}
            tabIndex={0}
          />
        )}

        {/* Collapse/Expand Button */}
        {onToggleCollapse && (
          <button
            onClick={onToggleCollapse}
            className={cn(
              'absolute z-20 p-1 rounded bg-background border border-border',
              'hover:bg-accent hover:text-accent-foreground transition-colors',
              'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
              position === 'left' && 'top-2 -right-3',
              position === 'right' && 'top-2 -left-3',
              position === 'top' && 'left-2 -bottom-3',
              position === 'bottom' && 'left-2 -top-3'
            )}
            title={isCollapsed ? 'Expand panel' : 'Collapse panel'}
          >
            <svg
              className={cn(
                'w-3 h-3 transition-transform',
                position === 'left' && (isCollapsed ? 'rotate-0' : 'rotate-180'),
                position === 'right' && (isCollapsed ? 'rotate-180' : 'rotate-0'),
                position === 'top' && (isCollapsed ? 'rotate-90' : 'rotate-270'),
                position === 'bottom' && (isCollapsed ? 'rotate-270' : 'rotate-90')
              )}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        )}
      </motion.div>
    </AnimatePresence>
  )
}
