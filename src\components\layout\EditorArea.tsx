/**
 * Editor Area Component
 * Main code editor area with tabs and welcome screen
 */

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Code2, 
  X, 
  Plus, 
  MoreHorizontal,
  FileText,
  Sparkles,
  FolderOpen,
  Search,
  Terminal,
  MessageSquare
} from 'lucide-react'
import { Button, Card, CardContent } from '../ui'
import { useLayoutStore } from '../../stores/layoutStore'
import { useFileSystemStore } from '../../stores/fileSystemStore'
import { cn } from '../../utils'
import { fadeVariants, slideVariants } from '../../design/animations'

interface EditorTab {
  id: string
  name: string
  path: string
  isDirty: boolean
  isActive: boolean
  language: string
}

// Mock editor tabs - start with empty tabs to show welcome screen
const mockTabs: EditorTab[] = []

export function EditorArea() {
  const [tabs, setTabs] = useState<EditorTab[]>(mockTabs)
  const [activeTabId, setActiveTabId] = useState<string | null>(null)
  const { showPanel, togglePanel } = useLayoutStore()
  const { openProject } = useFileSystemStore()

  const activeTab = activeTabId ? tabs.find(tab => tab.id === activeTabId) : null

  const handleTabClick = (tabId: string) => {
    setActiveTabId(tabId)
    setTabs(tabs.map(tab => ({
      ...tab,
      isActive: tab.id === tabId
    })))
  }

  const handleTabClose = (tabId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    const newTabs = tabs.filter(tab => tab.id !== tabId)
    setTabs(newTabs)

    // If closing active tab, switch to another tab or null
    if (tabId === activeTabId) {
      if (newTabs.length > 0) {
        setActiveTabId(newTabs[0].id)
      } else {
        setActiveTabId(null)
      }
    }
  }

  const handleNewTab = () => {
    const newTab: EditorTab = {
      id: Date.now().toString(),
      name: 'Untitled',
      path: '',
      isDirty: false,
      isActive: true,
      language: 'plaintext'
    }
    setTabs([...tabs, newTab])
    setActiveTabId(newTab.id)
  }

  const getLanguageIcon = (language: string) => {
    switch (language) {
      case 'typescript':
      case 'javascript':
        return <Code2 className="w-3 h-3 text-blue-400" />
      case 'json':
        return <FileText className="w-3 h-3 text-green-400" />
      default:
        return <FileText className="w-3 h-3 text-muted-foreground" />
    }
  }

  const WelcomeScreen = () => (
    <motion.div
      variants={fadeVariants}
      initial="hidden"
      animate="visible"
      className="flex flex-col items-center justify-center h-full p-8"
    >
      <div className="text-center space-y-6 max-w-md">
        {/* Logo */}
        <motion.div
          variants={slideVariants.up}
          className="relative mx-auto w-24 h-24"
        >
          <Code2 className="w-24 h-24 text-muted-foreground/50" />
          <motion.div
            animate={{ 
              scale: [1, 1.1, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{ 
              duration: 2, 
              repeat: Infinity, 
              ease: "easeInOut" 
            }}
            className="absolute top-0 left-0 w-24 h-24"
          >
            <Sparkles className="w-24 h-24 text-primary/30" />
          </motion.div>
        </motion.div>

        {/* Welcome Text */}
        <motion.div variants={slideVariants.up} className="space-y-2">
          <h1 className="text-2xl font-bold">Welcome to AI Code Editor</h1>
          <p className="text-muted-foreground">
            Start by opening a project or creating a new file
          </p>
        </motion.div>

        {/* Quick Actions */}
        <motion.div variants={slideVariants.up} className="space-y-3">
          <Button
            onClick={async () => {
              try {
                const projectPath = await window.electronAPI.openFolderDialog()
                if (projectPath) {
                  await openProject(projectPath)
                  showPanel('sidebar')
                }
              } catch (error) {
                console.error('Failed to open project:', error)
              }
            }}
            className="w-full"
            leftIcon={<FolderOpen className="w-4 h-4" />}
          >
            Open Project
          </Button>
          
          <Button 
            variant="secondary"
            onClick={handleNewTab}
            className="w-full"
            leftIcon={<Plus className="w-4 h-4" />}
          >
            New File
          </Button>
          
          <div className="grid grid-cols-2 gap-2">
            <Button 
              variant="outline"
              size="sm"
              onClick={() => togglePanel('chat')}
              leftIcon={<MessageSquare className="w-4 h-4" />}
            >
              AI Chat
            </Button>
            
            <Button 
              variant="outline"
              size="sm"
              onClick={() => togglePanel('terminal')}
              leftIcon={<Terminal className="w-4 h-4" />}
            >
              Terminal
            </Button>
          </div>
        </motion.div>

        {/* Recent Files */}
        <motion.div variants={slideVariants.up} className="w-full">
          <Card variant="ghost">
            <CardContent className="p-4">
              <h3 className="text-sm font-medium mb-3">Recent Files</h3>
              <div className="space-y-2">
                {['App.tsx', 'MainLayout.tsx', 'package.json'].map((file, index) => (
                  <button
                    key={file}
                    className="flex items-center space-x-2 w-full p-2 rounded hover:bg-accent hover:text-accent-foreground transition-colors text-left"
                    onClick={() => {
                      // Open recent file
                      console.log('Opening recent file:', file)
                    }}
                  >
                    <FileText className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm">{file}</span>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </motion.div>
  )

  return (
    <div className="flex flex-col h-full bg-editor text-editor-foreground">
      {/* Tab Bar */}
      <AnimatePresence>
        {tabs.length > 0 && (
          <motion.div
            initial={{ y: -32, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -32, opacity: 0 }}
            className="flex items-center bg-card border-b border-border"
          >
            {/* Tabs */}
            <div className="flex flex-1 overflow-x-auto">
              {tabs.map((tab) => (
                <motion.div
                  key={tab.id}
                  layout
                  className={cn(
                    'flex items-center space-x-2 px-3 py-2 border-r border-border',
                    'cursor-pointer select-none group min-w-0 max-w-48',
                    'hover:bg-accent hover:text-accent-foreground transition-colors',
                    tab.id === activeTabId && 'bg-editor text-editor-foreground'
                  )}
                  onClick={() => handleTabClick(tab.id)}
                >
                  {/* Language Icon */}
                  {getLanguageIcon(tab.language)}
                  
                  {/* File Name */}
                  <span className="text-sm truncate flex-1">
                    {tab.name}
                    {tab.isDirty && <span className="text-warning ml-1">•</span>}
                  </span>
                  
                  {/* Close Button */}
                  <button
                    onClick={(e) => handleTabClose(tab.id, e)}
                    className="opacity-0 group-hover:opacity-100 p-0.5 rounded hover:bg-accent-foreground/10 transition-opacity"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </motion.div>
              ))}
            </div>

            {/* Tab Actions */}
            <div className="flex items-center px-2">
              <Button
                variant="ghost"
                size="icon-sm"
                onClick={handleNewTab}
                title="New Tab"
              >
                <Plus className="w-3 h-3" />
              </Button>
              <Button
                variant="ghost"
                size="icon-sm"
                title="More Actions"
              >
                <MoreHorizontal className="w-3 h-3" />
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Editor Content */}
      <div className="flex-1 overflow-hidden">
        {tabs.length === 0 || !activeTab ? (
          <WelcomeScreen />
        ) : (
          <motion.div
            key={activeTabId}
            variants={fadeVariants}
            initial="hidden"
            animate="visible"
            className="h-full p-4"
          >
            {/* Placeholder for Monaco Editor */}
            <div className="h-full bg-editor-line-highlight rounded border border-border p-4">
              <div className="text-sm text-muted-foreground mb-4">
                Editing: {activeTab.path || activeTab.name}
              </div>
              <div className="font-mono text-sm">
                <div className="text-muted-foreground">1</div>
                <div className="text-muted-foreground">2</div>
                <div className="text-muted-foreground">3</div>
                <div className="text-muted-foreground">4</div>
                <div className="text-muted-foreground">5</div>
              </div>
              <div className="mt-4 text-center text-muted-foreground">
                Monaco Editor will be integrated here
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}
