import { GoogleGenerativeAI, GenerativeModel, Part, HarmCategory, HarmBlockThreshold } from '@google/generative-ai'
import { AIModel, AIResponse, ProjectContext, FileContent } from '../../../types'
import { BaseProvider } from './BaseProvider'
import { AIProviderConfig } from '../interfaces'

/**
 * Gemini AI Provider implementation
 * Supports Gemini 2.5 Flash and Pro models
 */
export class GeminiProvider extends BaseProvider {
  readonly name = 'gemini'
  readonly version = '1.0.0'
  readonly supportedModels: AIModel[] = [
    {
      id: 'gemini-2.5-flash',
      name: 'Gemini 2.5 Flash',
      provider: 'gemini',
      version: '2.5',
      maxTokens: 1048576, // 1M tokens
      costPerToken: 0.000001, // $0.001 per 1K tokens
      description: 'Fast and efficient model for quick responses'
    },
    {
      id: 'gemini-2.5-pro',
      name: 'Gemini 2.5 Pro',
      provider: 'gemini',
      version: '2.5',
      maxTokens: 2097152, // 2M tokens
      costPerToken: 0.000002, // $0.002 per 1K tokens
      description: 'Advanced model for complex reasoning and analysis'
    },
    {
      id: 'gemini-1.5-flash',
      name: 'Gemini 1.5 Flash',
      provider: 'gemini',
      version: '1.5',
      maxTokens: 1048576,
      costPerToken: 0.0000005,
      description: 'Previous generation fast model'
    },
    {
      id: 'gemini-1.5-pro',
      name: 'Gemini 1.5 Pro',
      provider: 'gemini',
      version: '1.5',
      maxTokens: 2097152,
      costPerToken: 0.000001,
      description: 'Previous generation advanced model'
    }
  ]

  private genAI: GoogleGenerativeAI | null = null
  private model: GenerativeModel | null = null

  constructor(config: AIProviderConfig) {
    super(config)
    if (this.apiKey) {
      this.initializeClient()
    }
  }

  private initializeClient(): void {
    if (!this.apiKey) {
      throw this.createError('MISSING_API_KEY', 'API key is required', false)
    }

    this.genAI = new GoogleGenerativeAI(this.apiKey)

    if (this.currentModel) {
      this.model = this.genAI.getGenerativeModel({
        model: this.currentModel.id,
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        },
        safetySettings: [
          {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
        ],
      })
    }
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const testAI = new GoogleGenerativeAI(apiKey)
      const testModel = testAI.getGenerativeModel({ model: 'gemini-1.5-flash' })

      // Try a simple request to validate the key
      await testModel.generateContent('Hello')
      return true
    } catch (error) {
      console.warn('Gemini API key validation failed:', error)
      return false
    }
  }

  async setApiKey(apiKey: string): Promise<void> {
    await super.setApiKey(apiKey)
    this.initializeClient()
  }

  async setModel(modelId: string): Promise<void> {
    await super.setModel(modelId)
    if (this.genAI) {
      this.initializeClient()
    }
  }

  async getAvailableModels(): Promise<AIModel[]> {
    // For Gemini, we return the predefined supported models
    // In a real implementation, you might want to fetch this from the API
    return [...this.supportedModels]
  }

  async sendMessage(message: string, context?: ProjectContext): Promise<AIResponse> {
    this.validateRequiredConfig()

    if (!this.model) {
      throw this.createError('MODEL_NOT_INITIALIZED', 'Model not initialized', false)
    }

    const startTime = Date.now()

    try {
      const prompt = this.buildPrompt(message, context)
      const result = await this.withRetry(async () => {
        return await this.model!.generateContent(prompt)
      })

      const response = result.response
      const text = response.text()

      // Calculate token usage (estimation for now)
      const tokensUsed = this.estimateTokens(message + text)

      return {
        content: text,
        tokensUsed,
        model: this.currentModel!.id,
        processingTime: Date.now() - startTime,
        confidence: this.extractConfidence(response),
        suggestions: this.extractSuggestions(text)
      }
    } catch (error) {
      throw this.handleGeminiError(error as Error)
    }
  }

  async streamMessage(
    message: string,
    onChunk: (chunk: string) => void,
    context?: ProjectContext
  ): Promise<AIResponse> {
    this.validateRequiredConfig()

    if (!this.model) {
      throw this.createError('MODEL_NOT_INITIALIZED', 'Model not initialized', false)
    }

    const startTime = Date.now()
    let fullResponse = ''

    try {
      const prompt = this.buildPrompt(message, context)
      const result = await this.withRetry(async () => {
        return await this.model!.generateContentStream(prompt)
      })

      for await (const chunk of result.stream) {
        const chunkText = chunk.text()
        if (chunkText) {
          fullResponse += chunkText
          onChunk(chunkText)
        }
      }

      // Get final response for metadata
      const finalResponse = await result.response
      const tokensUsed = this.estimateTokens(message + fullResponse)

      return {
        content: fullResponse,
        tokensUsed,
        model: this.currentModel!.id,
        processingTime: Date.now() - startTime,
        confidence: this.extractConfidence(finalResponse),
        suggestions: this.extractSuggestions(fullResponse)
      }
    } catch (error) {
      throw this.handleGeminiError(error as Error)
    }
  }

  async countTokens(text: string): Promise<number> {
    this.validateRequiredConfig()

    if (!this.model) {
      throw this.createError('MODEL_NOT_INITIALIZED', 'Model not initialized', false)
    }

    try {
      const result = await this.model.countTokens(text)
      return result.totalTokens
    } catch (error) {
      // Fallback to estimation if API call fails
      console.warn('Token counting failed, using estimation:', error)
      return this.estimateTokens(text)
    }
  }

  // Utility methods
  private buildPrompt(message: string, context?: ProjectContext): string | Part[] {
    if (!context) {
      return message
    }

    let prompt = ''

    // Add project context if available
    if (context.projectType) {
      prompt += `Project Type: ${context.projectType}\n\n`
    }

    // Add current file context
    if (context.currentFile) {
      prompt += `Current File: ${context.currentFile}\n\n`
    }

    // Add selected code context
    if (context.selectedCode) {
      prompt += `Selected Code:\n\`\`\`\n${context.selectedCode}\n\`\`\`\n\n`
    }

    // Add relevant files context (limit to avoid token overflow)
    if (context.files && context.files.length > 0) {
      prompt += 'Relevant Files:\n'
      const relevantFiles = context.files.slice(0, 5) // Limit to 5 files

      for (const file of relevantFiles) {
        prompt += `\n--- ${file.path} ---\n`
        // Truncate large files
        const content = file.content.length > 2000
          ? file.content.substring(0, 2000) + '\n... (truncated)'
          : file.content
        prompt += content + '\n'
      }
      prompt += '\n'
    }

    // Add dependencies context
    if (context.dependencies && context.dependencies.length > 0) {
      prompt += `Dependencies: ${context.dependencies.map(d => d.name).join(', ')}\n\n`
    }

    // Add frameworks context
    if (context.frameworks && context.frameworks.length > 0) {
      prompt += `Frameworks: ${context.frameworks.map(f => f.name).join(', ')}\n\n`
    }

    prompt += `User Message: ${message}`

    return prompt
  }

  private extractConfidence(response: any): number {
    // Gemini doesn't provide confidence scores directly
    // We can estimate based on response characteristics
    try {
      const candidates = response.candidates
      if (candidates && candidates.length > 0) {
        const candidate = candidates[0]
        if (candidate.finishReason === 'STOP') {
          return 0.9 // High confidence for complete responses
        } else if (candidate.finishReason === 'MAX_TOKENS') {
          return 0.7 // Medium confidence for truncated responses
        }
      }
      return 0.8 // Default confidence
    } catch (error) {
      return 0.5 // Low confidence if we can't determine
    }
  }

  private extractSuggestions(text: string): string[] {
    // Extract potential suggestions from the response
    const suggestions: string[] = []

    // Look for common suggestion patterns
    const suggestionPatterns = [
      /(?:consider|try|you could|might want to|suggestion:|recommend:)\s*([^.!?]+)/gi,
      /(?:alternative|option|approach):\s*([^.!?]+)/gi
    ]

    for (const pattern of suggestionPatterns) {
      const matches = text.matchAll(pattern)
      for (const match of matches) {
        if (match[1] && match[1].trim().length > 10) {
          suggestions.push(match[1].trim())
        }
      }
    }

    return suggestions.slice(0, 3) // Limit to 3 suggestions
  }

  private handleGeminiError(error: Error): never {
    const errorMessage = error.message.toLowerCase()

    if (errorMessage.includes('api key')) {
      throw this.createError(
        'INVALID_API_KEY',
        'Invalid or missing API key',
        false,
        { originalError: error.message }
      )
    }

    if (errorMessage.includes('quota') || errorMessage.includes('rate limit')) {
      throw this.createError(
        'RATE_LIMIT_EXCEEDED',
        'Rate limit or quota exceeded',
        true,
        { originalError: error.message }
      )
    }

    if (errorMessage.includes('model not found')) {
      throw this.createError(
        'MODEL_NOT_FOUND',
        'Specified model not found',
        false,
        { originalError: error.message }
      )
    }

    if (errorMessage.includes('safety') || errorMessage.includes('blocked')) {
      throw this.createError(
        'CONTENT_BLOCKED',
        'Content was blocked by safety filters',
        false,
        { originalError: error.message }
      )
    }

    if (errorMessage.includes('timeout') || errorMessage.includes('network')) {
      throw this.createError(
        'NETWORK_ERROR',
        'Network timeout or connection error',
        true,
        { originalError: error.message }
      )
    }

    // Generic error
    throw this.createError(
      'UNKNOWN_ERROR',
      `Gemini API error: ${error.message}`,
      true,
      { originalError: error.message }
    )
  }

  // Override token estimation for better accuracy with Gemini
  estimateTokens(text: string): number {
    // Gemini uses a different tokenization approach
    // More accurate estimation based on Gemini's characteristics
    const words = text.split(/\s+/).length
    const characters = text.length

    // Gemini typically uses ~3.5 characters per token for English
    // But also consider word boundaries
    const charBasedEstimate = Math.ceil(characters / 3.5)
    const wordBasedEstimate = Math.ceil(words * 1.3)

    // Use the higher estimate for safety
    return Math.max(charBasedEstimate, wordBasedEstimate)
  }

  // Get model-specific configuration
  getModelConfig(modelId: string): Record<string, any> {
    const model = this.supportedModels.find(m => m.id === modelId)
    if (!model) {
      return {}
    }

    return {
      maxTokens: model.maxTokens,
      costPerToken: model.costPerToken,
      version: model.version,
      description: model.description
    }
  }
}