import { ipcMain } from 'electron'
import { AIServiceManager } from '../../services/ai/AIServiceManager'
import { GeminiProvider } from '../../services/ai/providers/GeminiProvider'
import { ModelManager } from '../../services/ai/ModelManager'
import { ChatService } from '../../services/ai/ChatService'
import { TokenManager } from '../../services/ai/TokenManager'
import { SettingsManager } from '../../services/settings/SettingsManager'
import { KeyManager } from '../../services/security/KeyManager'
import { AIModel, AIResponse, ProjectContext, TokenUsage } from '../../types'

/**
 * AI Service Handlers for IPC communication
 */
export class AIHandlers {
  private aiServiceManager: AIServiceManager
  private modelManager: ModelManager
  private chatService: ChatService
  private tokenManager: TokenManager
  private settingsManager: SettingsManager
  private keyManager: KeyManager
  private isInitialized = false

  constructor() {
    // Initialize services
    this.settingsManager = new SettingsManager()
    this.tokenManager = new TokenManager(this.settingsManager)
    this.modelManager = new ModelManager(this.settingsManager)
    this.chatService = new ChatService()
    this.keyManager = KeyManager.getInstance()
    this.aiServiceManager = new AIServiceManager(this.tokenManager)

    this.initializeServices()
    this.registerHandlers()
  }

  private async initializeServices(): Promise<void> {
    try {
      // Register Gemini provider
      const geminiConfig = {
        name: 'gemini',
        timeout: 30000,
        maxRetries: 3
      }

      const geminiProvider = new GeminiProvider(geminiConfig)
      this.aiServiceManager.registerProvider(geminiProvider)
      this.modelManager.registerProvider(geminiProvider)

      // Load default model if available
      await this.modelManager.loadDefaultModel()

      this.isInitialized = true
    } catch (error) {
      console.error('Failed to initialize AI services:', error)
    }
  }

  private registerHandlers(): void {
    // AI Service Management
    ipcMain.handle('ai:getProviders', this.handleGetProviders.bind(this))
    ipcMain.handle('ai:setActiveProvider', this.handleSetActiveProvider.bind(this))
    ipcMain.handle('ai:getActiveProvider', this.handleGetActiveProvider.bind(this))
    ipcMain.handle('ai:isConfigured', this.handleIsConfigured.bind(this))
    ipcMain.handle('ai:validateConfiguration', this.handleValidateConfiguration.bind(this))

    // Model Management
    ipcMain.handle('ai:getAvailableModels', this.handleGetAvailableModels.bind(this))
    ipcMain.handle('ai:getCurrentModel', this.handleGetCurrentModel.bind(this))
    ipcMain.handle('ai:setCurrentModel', this.handleSetCurrentModel.bind(this))
    ipcMain.handle('ai:getRecommendedModels', this.handleGetRecommendedModels.bind(this))
    ipcMain.handle('ai:getModelsByProvider', this.handleGetModelsByProvider.bind(this))

    // Chat Operations
    ipcMain.handle('ai:sendMessage', this.handleSendMessage.bind(this))
    ipcMain.handle('ai:streamMessage', this.handleStreamMessage.bind(this))
    ipcMain.handle('ai:countTokens', this.handleCountTokens.bind(this))

    // Chat Session Management
    ipcMain.handle('chat:createSession', this.handleCreateSession.bind(this))
    ipcMain.handle('chat:getSession', this.handleGetSession.bind(this))
    ipcMain.handle('chat:getAllSessions', this.handleGetAllSessions.bind(this))
    ipcMain.handle('chat:deleteSession', this.handleDeleteSession.bind(this))
    ipcMain.handle('chat:addMessage', this.handleAddMessage.bind(this))
    ipcMain.handle('chat:getMessages', this.handleGetMessages.bind(this))
    ipcMain.handle('chat:clearMessages', this.handleClearMessages.bind(this))
    ipcMain.handle('chat:updateContext', this.handleUpdateContext.bind(this))
    ipcMain.handle('chat:getContext', this.handleGetContext.bind(this))

    // Token Management
    ipcMain.handle('tokens:getUsage', this.handleGetTokenUsage.bind(this))
    ipcMain.handle('tokens:resetUsage', this.handleResetTokenUsage.bind(this))
    ipcMain.handle('tokens:setLimit', this.handleSetTokenLimit.bind(this))
    ipcMain.handle('tokens:getLimit', this.handleGetTokenLimit.bind(this))
    ipcMain.handle('tokens:isLimitExceeded', this.handleIsLimitExceeded.bind(this))
    ipcMain.handle('tokens:getRemainingTokens', this.handleGetRemainingTokens.bind(this))
    ipcMain.handle('tokens:getAnalytics', this.handleGetTokenAnalytics.bind(this))

    // API Key Management
    ipcMain.handle('keys:setApiKey', this.handleSetApiKey.bind(this))
    ipcMain.handle('keys:getApiKey', this.handleGetApiKey.bind(this))
    ipcMain.handle('keys:deleteApiKey', this.handleDeleteApiKey.bind(this))
    ipcMain.handle('keys:validateApiKey', this.handleValidateApiKey.bind(this))
    ipcMain.handle('keys:hasApiKey', this.handleHasApiKey.bind(this))
    ipcMain.handle('keys:getConfiguredProviders', this.handleGetConfiguredProviders.bind(this))
  }

  // AI Service Management Handlers
  private async handleGetProviders(): Promise<string[]> {
    return this.aiServiceManager.getAvailableProviders()
  }

  private async handleSetActiveProvider(_event: any, providerName: string): Promise<void> {
    await this.aiServiceManager.setActiveProvider(providerName)
  }

  private async handleGetActiveProvider(): Promise<string | null> {
    const provider = this.aiServiceManager.getActiveProvider()
    return provider?.name || null
  }

  private async handleIsConfigured(): Promise<boolean> {
    return this.aiServiceManager.isConfigured()
  }

  private async handleValidateConfiguration(): Promise<boolean> {
    return await this.aiServiceManager.validateConfiguration()
  }

  // Model Management Handlers
  private async handleGetAvailableModels(): Promise<AIModel[]> {
    return await this.modelManager.getAllAvailableModels()
  }

  private async handleGetCurrentModel(): Promise<AIModel | null> {
    return this.modelManager.getCurrentModel()
  }

  private async handleSetCurrentModel(_event: any, modelId: string): Promise<void> {
    await this.modelManager.setCurrentModel(modelId)
  }

  private async handleGetRecommendedModels(_event: any, context?: any): Promise<AIModel[]> {
    return await this.modelManager.getRecommendedModels(context)
  }

  private async handleGetModelsByProvider(_event: any, providerName: string): Promise<AIModel[]> {
    return await this.modelManager.findModelsByProvider(providerName)
  }

  // Chat Operation Handlers
  private async handleSendMessage(_event: any, message: string, context?: ProjectContext): Promise<AIResponse> {
    return await this.aiServiceManager.sendMessage(message, context)
  }

  private async handleStreamMessage(_event: any, message: string, context?: ProjectContext): Promise<AIResponse> {
    // For streaming, we'll need to set up a different mechanism
    // This is a simplified version that returns the full response
    return await this.aiServiceManager.sendMessage(message, context)
  }

  private async handleCountTokens(_event: any, text: string): Promise<number> {
    return await this.aiServiceManager.countTokens(text)
  }

  // Chat Session Management Handlers
  private async handleCreateSession(_event: any, projectId?: string): Promise<string> {
    return await this.chatService.createSession(projectId)
  }

  private async handleGetSession(_event: any, sessionId: string): Promise<any> {
    return await this.chatService.getSession(sessionId)
  }

  private async handleGetAllSessions(): Promise<any[]> {
    return await this.chatService.getAllSessions()
  }

  private async handleDeleteSession(_event: any, sessionId: string): Promise<void> {
    await this.chatService.deleteSession(sessionId)
  }

  private async handleAddMessage(_event: any, sessionId: string, message: any): Promise<void> {
    await this.chatService.addMessage(sessionId, message)
  }

  private async handleGetMessages(_event: any, sessionId: string): Promise<any[]> {
    return await this.chatService.getMessages(sessionId)
  }

  private async handleClearMessages(_event: any, sessionId: string): Promise<void> {
    await this.chatService.clearMessages(sessionId)
  }

  private async handleUpdateContext(_event: any, sessionId: string, context: ProjectContext): Promise<void> {
    await this.chatService.updateSessionContext(sessionId, context)
  }

  private async handleGetContext(_event: any, sessionId: string): Promise<ProjectContext | null> {
    return await this.chatService.getSessionContext(sessionId)
  }

  // Token Management Handlers
  private async handleGetTokenUsage(_event: any, period?: 'daily' | 'monthly' | 'total'): Promise<TokenUsage> {
    return await this.tokenManager.getUsage(period)
  }

  private async handleResetTokenUsage(_event: any, period?: 'daily' | 'monthly' | 'total'): Promise<void> {
    await this.tokenManager.resetUsage(period)
  }

  private async handleSetTokenLimit(_event: any, limit: number, period: 'daily' | 'monthly'): Promise<void> {
    await this.tokenManager.setTokenLimit(limit, period)
  }

  private async handleGetTokenLimit(_event: any, period: 'daily' | 'monthly'): Promise<number> {
    return await this.tokenManager.getTokenLimit(period)
  }

  private async handleIsLimitExceeded(_event: any, period: 'daily' | 'monthly'): Promise<boolean> {
    return await this.tokenManager.isLimitExceeded(period)
  }

  private async handleGetRemainingTokens(_event: any, period: 'daily' | 'monthly'): Promise<number> {
    return await this.tokenManager.getRemainingTokens(period)
  }

  private async handleGetTokenAnalytics(): Promise<any> {
    return await this.tokenManager.getUsageAnalytics()
  }

  // API Key Management Handlers
  private async handleSetApiKey(_event: any, provider: string, apiKey: string): Promise<void> {
    // Validate the key first
    const isValid = await this.keyManager.validateApiKey(provider, apiKey)
    if (!isValid) {
      throw new Error(`Invalid API key for provider: ${provider}`)
    }

    // Store the key
    await this.settingsManager.setApiKey(provider, apiKey)

    // Update the provider with the new key
    const providerInstance = this.aiServiceManager.getProvider(provider)
    if (providerInstance) {
      await providerInstance.setApiKey(apiKey)
    }
  }

  private async handleGetApiKey(_event: any, provider: string): Promise<string | null> {
    return await this.settingsManager.getApiKey(provider)
  }

  private async handleDeleteApiKey(_event: any, provider: string): Promise<void> {
    await this.settingsManager.deleteApiKey(provider)
  }

  private async handleValidateApiKey(_event: any, provider: string, apiKey: string): Promise<boolean> {
    return await this.keyManager.validateApiKey(provider, apiKey)
  }

  private async handleHasApiKey(_event: any, provider: string): Promise<boolean> {
    return await this.settingsManager.hasApiKey(provider)
  }

  private async handleGetConfiguredProviders(): Promise<string[]> {
    return await this.settingsManager.getConfiguredProviders()
  }

  // Cleanup method
  cleanup(): void {
    // Remove all IPC handlers
    ipcMain.removeAllListeners('ai:getProviders')
    ipcMain.removeAllListeners('ai:setActiveProvider')
    ipcMain.removeAllListeners('ai:getActiveProvider')
    ipcMain.removeAllListeners('ai:isConfigured')
    ipcMain.removeAllListeners('ai:validateConfiguration')

    ipcMain.removeAllListeners('ai:getAvailableModels')
    ipcMain.removeAllListeners('ai:getCurrentModel')
    ipcMain.removeAllListeners('ai:setCurrentModel')
    ipcMain.removeAllListeners('ai:getRecommendedModels')
    ipcMain.removeAllListeners('ai:getModelsByProvider')

    ipcMain.removeAllListeners('ai:sendMessage')
    ipcMain.removeAllListeners('ai:streamMessage')
    ipcMain.removeAllListeners('ai:countTokens')

    ipcMain.removeAllListeners('chat:createSession')
    ipcMain.removeAllListeners('chat:getSession')
    ipcMain.removeAllListeners('chat:getAllSessions')
    ipcMain.removeAllListeners('chat:deleteSession')
    ipcMain.removeAllListeners('chat:addMessage')
    ipcMain.removeAllListeners('chat:getMessages')
    ipcMain.removeAllListeners('chat:clearMessages')
    ipcMain.removeAllListeners('chat:updateContext')
    ipcMain.removeAllListeners('chat:getContext')

    ipcMain.removeAllListeners('tokens:getUsage')
    ipcMain.removeAllListeners('tokens:resetUsage')
    ipcMain.removeAllListeners('tokens:setLimit')
    ipcMain.removeAllListeners('tokens:getLimit')
    ipcMain.removeAllListeners('tokens:isLimitExceeded')
    ipcMain.removeAllListeners('tokens:getRemainingTokens')
    ipcMain.removeAllListeners('tokens:getAnalytics')

    ipcMain.removeAllListeners('keys:setApiKey')
    ipcMain.removeAllListeners('keys:getApiKey')
    ipcMain.removeAllListeners('keys:deleteApiKey')
    ipcMain.removeAllListeners('keys:validateApiKey')
    ipcMain.removeAllListeners('keys:hasApiKey')
    ipcMain.removeAllListeners('keys:getConfiguredProviders')

    // Cleanup services
    this.aiServiceManager.cleanup()
    this.modelManager.cleanup()
    this.chatService.cleanup()
    this.tokenManager.cleanup()
    this.settingsManager.cleanup()
  }
}