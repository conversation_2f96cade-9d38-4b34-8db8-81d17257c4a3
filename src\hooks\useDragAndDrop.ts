/**
 * Drag and Drop Hook
 * Comprehensive drag and drop functionality for file/folder operations
 */

import { useState, useCallback, useRef, useEffect } from 'react'
import { FileNode } from '../types'

export interface DragItem {
  node: FileNode
  index: number
  sourceParent: string | null
}

export interface DropTarget {
  node: FileNode | null
  position: 'before' | 'after' | 'inside' | null
  isValid: boolean
}

export interface DragState {
  isDragging: boolean
  dragItem: DragItem | null
  dropTarget: DropTarget | null
  dragPreview: {
    x: number
    y: number
    visible: boolean
  }
}

export interface UseDragAndDropOptions {
  onMove?: (sourceNode: FileNode, targetNode: FileNode, position: 'before' | 'after' | 'inside') => Promise<void>
  onReorder?: (sourceNode: FileNode, targetIndex: number, targetParent: string | null) => Promise<void>
  canDrop?: (sourceNode: FileNode, targetNode: FileNode, position: 'before' | 'after' | 'inside') => boolean
  disabled?: boolean
}

export const useDragAndDrop = (options: UseDragAndDropOptions = {}) => {
  const {
    onMove,
    onReorder,
    canDrop,
    disabled = false
  } = options

  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    dragItem: null,
    dropTarget: null,
    dragPreview: { x: 0, y: 0, visible: false }
  })

  const dragImageRef = useRef<HTMLElement | null>(null)
  const dropIndicatorRef = useRef<HTMLElement | null>(null)

  // Validate drop operation
  const validateDrop = useCallback((
    sourceNode: FileNode,
    targetNode: FileNode,
    position: 'before' | 'after' | 'inside'
  ): boolean => {
    if (disabled) return false

    // Can't drop on itself
    if (sourceNode.path === targetNode.path) return false

    // Can't drop a parent into its child
    if (targetNode.path.startsWith(sourceNode.path + '/')) return false

    // Can only drop inside folders
    if (position === 'inside' && targetNode.type !== 'folder') return false

    // Custom validation
    if (canDrop) {
      return canDrop(sourceNode, targetNode, position)
    }

    return true
  }, [disabled, canDrop])

  // Start drag operation
  const handleDragStart = useCallback((
    event: React.DragEvent,
    node: FileNode,
    index: number,
    sourceParent: string | null
  ) => {
    if (disabled) {
      event.preventDefault()
      return
    }

    const dragItem: DragItem = { node, index, sourceParent }

    // Set drag data
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('application/json', JSON.stringify(dragItem))

    // Create custom drag image
    const dragImage = createDragImage(node)
    if (dragImage) {
      dragImageRef.current = dragImage
      event.dataTransfer.setDragImage(dragImage, 20, 10)
    }

    setDragState(prev => ({
      ...prev,
      isDragging: true,
      dragItem,
      dragPreview: { x: event.clientX, y: event.clientY, visible: true }
    }))
  }, [disabled])

  // Handle drag over
  const handleDragOver = useCallback((
    event: React.DragEvent,
    targetNode: FileNode,
    targetIndex: number
  ) => {
    if (disabled || !dragState.isDragging) return

    event.preventDefault()

    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    const y = event.clientY - rect.top
    const height = rect.height

    // Determine drop position
    let position: 'before' | 'after' | 'inside'
    if (targetNode.type === 'folder') {
      if (y < height * 0.25) {
        position = 'before'
      } else if (y > height * 0.75) {
        position = 'after'
      } else {
        position = 'inside'
      }
    } else {
      position = y < height * 0.5 ? 'before' : 'after'
    }

    const isValid = dragState.dragItem 
      ? validateDrop(dragState.dragItem.node, targetNode, position)
      : false

    // Update drop target
    setDragState(prev => ({
      ...prev,
      dropTarget: {
        node: targetNode,
        position,
        isValid
      }
    }))

    // Set drop effect
    event.dataTransfer.dropEffect = isValid ? 'move' : 'none'
  }, [disabled, dragState.isDragging, dragState.dragItem, validateDrop])

  // Handle drop
  const handleDrop = useCallback(async (
    event: React.DragEvent,
    targetNode: FileNode,
    targetIndex: number
  ) => {
    if (disabled) return

    event.preventDefault()

    const { dragItem, dropTarget } = dragState

    if (!dragItem || !dropTarget || !dropTarget.isValid) {
      setDragState(prev => ({ ...prev, isDragging: false, dragItem: null, dropTarget: null }))
      return
    }

    try {
      if (dropTarget.position === 'inside') {
        // Move into folder
        await onMove?.(dragItem.node, targetNode, 'inside')
      } else {
        // Reorder or move
        if (dragItem.sourceParent === getParentPath(targetNode.path)) {
          // Reordering within same parent
          const newIndex = dropTarget.position === 'before' ? targetIndex : targetIndex + 1
          await onReorder?.(dragItem.node, newIndex, dragItem.sourceParent)
        } else {
          // Moving to different parent
          await onMove?.(dragItem.node, targetNode, dropTarget.position)
        }
      }
    } catch (error) {
      console.error('Drop operation failed:', error)
    } finally {
      setDragState({
        isDragging: false,
        dragItem: null,
        dropTarget: null,
        dragPreview: { x: 0, y: 0, visible: false }
      })
    }
  }, [disabled, dragState, onMove, onReorder])

  // Handle drag end
  const handleDragEnd = useCallback(() => {
    setDragState({
      isDragging: false,
      dragItem: null,
      dropTarget: null,
      dragPreview: { x: 0, y: 0, visible: false }
    })

    // Cleanup drag image
    if (dragImageRef.current) {
      document.body.removeChild(dragImageRef.current)
      dragImageRef.current = null
    }
  }, [])

  // Handle drag leave
  const handleDragLeave = useCallback((event: React.DragEvent) => {
    // Only clear drop target if leaving the entire tree
    const relatedTarget = event.relatedTarget as HTMLElement
    if (!relatedTarget || !event.currentTarget.contains(relatedTarget)) {
      setDragState(prev => ({ ...prev, dropTarget: null }))
    }
  }, [])

  // Update drag preview position
  const updateDragPreview = useCallback((x: number, y: number) => {
    setDragState(prev => ({
      ...prev,
      dragPreview: { ...prev.dragPreview, x, y }
    }))
  }, [])

  // Mouse move handler for drag preview
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      if (dragState.isDragging) {
        updateDragPreview(event.clientX, event.clientY)
      }
    }

    if (dragState.isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      return () => document.removeEventListener('mousemove', handleMouseMove)
    }
  }, [dragState.isDragging, updateDragPreview])

  return {
    dragState,
    handleDragStart,
    handleDragOver,
    handleDrop,
    handleDragEnd,
    handleDragLeave,
    
    // Utility functions
    isDragging: dragState.isDragging,
    dragItem: dragState.dragItem,
    dropTarget: dragState.dropTarget,
    
    // Check if node is being dragged
    isNodeDragging: (nodePath: string) => 
      dragState.dragItem?.node.path === nodePath,
    
    // Check if node is drop target
    isNodeDropTarget: (nodePath: string) => 
      dragState.dropTarget?.node?.path === nodePath,
    
    // Get drop indicator position
    getDropIndicator: (nodePath: string) => {
      if (dragState.dropTarget?.node?.path === nodePath) {
        return {
          position: dragState.dropTarget.position,
          isValid: dragState.dropTarget.isValid
        }
      }
      return null
    }
  }
}

// Helper functions
function createDragImage(node: FileNode): HTMLElement | null {
  const dragImage = document.createElement('div')
  dragImage.className = 'drag-preview'
  dragImage.style.cssText = `
    position: absolute;
    top: -1000px;
    left: -1000px;
    background: var(--popover);
    border: 1px solid var(--border);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    color: var(--foreground);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    pointer-events: none;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    max-width: 200px;
  `

  // Add icon and name
  const icon = document.createElement('div')
  icon.textContent = node.type === 'folder' ? '📁' : '📄'
  
  const name = document.createElement('span')
  name.textContent = node.name
  name.style.cssText = 'white-space: nowrap; overflow: hidden; text-overflow: ellipsis;'

  dragImage.appendChild(icon)
  dragImage.appendChild(name)
  
  document.body.appendChild(dragImage)
  return dragImage
}

function getParentPath(path: string): string | null {
  const lastSlash = path.lastIndexOf('/')
  return lastSlash > 0 ? path.substring(0, lastSlash) : null
}

// Drop indicator component props
export interface DropIndicatorProps {
  position: 'before' | 'after' | 'inside' | null
  isValid: boolean
  className?: string
}

// Drag preview component props
export interface DragPreviewProps {
  dragState: DragState
  className?: string
}
