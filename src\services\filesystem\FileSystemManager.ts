import * as fs from 'fs/promises'
import * as path from 'path'
import * as fsSync from 'fs'
import { watch, FSWatcher } from 'chokidar'
import {
  ProjectStructure,
  FileNode,
  FileContent,
  FileEvent,
  ProjectType
} from '../../types'
import {
  IFileSystemManager,
  FileTypeInfo,
  FileStats,
  SearchOptions,
  ProjectAnalysis,
  PackageManager,
  BuildSystem,
  Framework,
  Dependency
} from '../ai/interfaces'

/**
 * File System Manager for handling all file system operations
 * Provides comprehensive file and project management capabilities
 */
export class FileSystemManager implements IFileSystemManager {
  private currentProject: ProjectStructure | null = null
  private watcher: FSWatcher | null = null
  private watchCallbacks: Set<(event: FileEvent) => void> = new Set()
  private readonly maxFileSize = 10 * 1024 * 1024 // 10MB
  private readonly textExtensions = new Set([
    '.js', '.ts', '.jsx', '.tsx', '.vue', '.py', '.java', '.cs', '.cpp', '.c',
    '.go', '.rs', '.php', '.rb', '.swift', '.kt', '.dart', '.html', '.css',
    '.scss', '.sass', '.less', '.json', '.xml', '.yaml', '.yml', '.toml',
    '.md', '.txt', '.sql', '.sh', '.ps1', '.dockerfile', '.prisma', '.env'
  ])

  constructor() {
    this.initializeFileTypeDetection()
  }

  // Project operations
  async openProject(projectPath: string): Promise<ProjectStructure> {
    try {
      // Validate path exists
      const exists = await this.exists(projectPath)
      if (!exists) {
        throw new Error(`Project path does not exist: ${projectPath}`)
      }

      // Check if it's a directory
      const stats = await this.getStats(projectPath)
      if (!stats.isDirectory) {
        throw new Error(`Project path is not a directory: ${projectPath}`)
      }

      // Close existing project if any
      if (this.currentProject) {
        await this.closeProject()
      }

      // Parse directory structure
      const files = await this.parseDirectory(projectPath)
      const projectName = path.basename(projectPath)
      const projectType = await this.detectProjectType(projectPath)

      this.currentProject = {
        root: projectPath,
        files,
        name: projectName,
        type: projectType
      }

      // Start watching files
      this.startWatching(projectPath)

      return this.currentProject
    } catch (error) {
      throw new Error(`Failed to open project: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  async closeProject(): Promise<void> {
    if (this.watcher) {
      await this.watcher.close()
      this.watcher = null
    }
    this.watchCallbacks.clear()
    this.currentProject = null
  }

  getProjectStructure(): ProjectStructure | null {
    return this.currentProject
  }

  async refreshProject(): Promise<void> {
    if (!this.currentProject) {
      throw new Error('No project is currently open')
    }

    const projectPath = this.currentProject.root
    const files = await this.parseDirectory(projectPath)

    this.currentProject = {
      ...this.currentProject,
      files
    }
  }

  // File operations
  async readFile(filePath: string): Promise<FileContent> {
    try {
      // Check if file exists
      const exists = await this.exists(filePath)
      if (!exists) {
        throw new Error(`File does not exist: ${filePath}`)
      }

      // Check file size
      const stats = await this.getStats(filePath)
      if (stats.size > this.maxFileSize) {
        throw new Error(`File too large: ${filePath} (${stats.size} bytes)`)
      }

      // Check if it's a binary file
      const isBinary = await this.isBinaryFile(filePath)
      if (isBinary) {
        throw new Error(`Cannot read binary file: ${filePath}`)
      }

      // Read file content
      const content = await fs.readFile(filePath, 'utf-8')
      const extension = path.extname(filePath)
      const language = this.getLanguageFromExtension(extension)

      return {
        path: filePath,
        content,
        language,
        encoding: 'utf-8',
        lastModified: stats.modified
      }
    } catch (error) {
      throw new Error(`Failed to read file: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  async writeFile(filePath: string, content: string): Promise<void> {
    try {
      // Ensure directory exists
      const directory = path.dirname(filePath)
      await fs.mkdir(directory, { recursive: true })

      // Write file
      await fs.writeFile(filePath, content, 'utf-8')
    } catch (error) {
      throw new Error(`Failed to write file: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  async createFile(filePath: string, content: string = ''): Promise<void> {
    try {
      // Check if file already exists
      const exists = await this.exists(filePath)
      if (exists) {
        throw new Error(`File already exists: ${filePath}`)
      }

      await this.writeFile(filePath, content)
    } catch (error) {
      throw new Error(`Failed to create file: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  async deleteFile(filePath: string): Promise<void> {
    try {
      // Check if file exists
      const exists = await this.exists(filePath)
      if (!exists) {
        throw new Error(`File does not exist: ${filePath}`)
      }

      // Check if it's actually a file
      const stats = await this.getStats(filePath)
      if (!stats.isFile) {
        throw new Error(`Path is not a file: ${filePath}`)
      }

      await fs.unlink(filePath)
    } catch (error) {
      throw new Error(`Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  async copyFile(sourcePath: string, destinationPath: string): Promise<void> {
    try {
      // Check if source exists
      const sourceExists = await this.exists(sourcePath)
      if (!sourceExists) {
        throw new Error(`Source file does not exist: ${sourcePath}`)
      }

      // Check if source is a file
      const sourceStats = await this.getStats(sourcePath)
      if (!sourceStats.isFile) {
        throw new Error(`Source is not a file: ${sourcePath}`)
      }

      // Check if destination already exists
      const destExists = await this.exists(destinationPath)
      if (destExists) {
        throw new Error(`Destination already exists: ${destinationPath}`)
      }

      // Ensure destination directory exists
      const destDir = path.dirname(destinationPath)
      await fs.mkdir(destDir, { recursive: true })

      // Copy file
      await fs.copyFile(sourcePath, destinationPath)
    } catch (error) {
      throw new Error(`Failed to copy file: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  async moveFile(sourcePath: string, destinationPath: string): Promise<void> {
    try {
      // Copy file first
      await this.copyFile(sourcePath, destinationPath)

      // Delete source file
      await this.deleteFile(sourcePath)
    } catch (error) {
      throw new Error(`Failed to move file: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // Directory operations
  async createDirectory(dirPath: string): Promise<void> {
    try {
      // Check if directory already exists
      const exists = await this.exists(dirPath)
      if (exists) {
        const stats = await this.getStats(dirPath)
        if (stats.isDirectory) {
          throw new Error(`Directory already exists: ${dirPath}`)
        } else {
          throw new Error(`Path exists but is not a directory: ${dirPath}`)
        }
      }

      await fs.mkdir(dirPath, { recursive: true })
    } catch (error) {
      throw new Error(`Failed to create directory: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  async deleteDirectory(dirPath: string, recursive: boolean = false): Promise<void> {
    try {
      // Check if directory exists
      const exists = await this.exists(dirPath)
      if (!exists) {
        throw new Error(`Directory does not exist: ${dirPath}`)
      }

      // Check if it's actually a directory
      const stats = await this.getStats(dirPath)
      if (!stats.isDirectory) {
        throw new Error(`Path is not a directory: ${dirPath}`)
      }

      if (recursive) {
        await fs.rm(dirPath, { recursive: true, force: true })
      } else {
        // Check if directory is empty
        const contents = await fs.readdir(dirPath)
        if (contents.length > 0) {
          throw new Error(`Directory is not empty: ${dirPath}. Use recursive option to delete non-empty directories.`)
        }
        await fs.rmdir(dirPath)
      }
    } catch (error) {
      throw new Error(`Failed to delete directory: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  async copyDirectory(sourcePath: string, destinationPath: string): Promise<void> {
    try {
      // Check if source exists and is a directory
      const sourceExists = await this.exists(sourcePath)
      if (!sourceExists) {
        throw new Error(`Source directory does not exist: ${sourcePath}`)
      }

      const sourceStats = await this.getStats(sourcePath)
      if (!sourceStats.isDirectory) {
        throw new Error(`Source is not a directory: ${sourcePath}`)
      }

      // Check if destination already exists
      const destExists = await this.exists(destinationPath)
      if (destExists) {
        throw new Error(`Destination already exists: ${destinationPath}`)
      }

      // Create destination directory
      await fs.mkdir(destinationPath, { recursive: true })

      // Copy all contents recursively
      await this.copyDirectoryRecursive(sourcePath, destinationPath)
    } catch (error) {
      throw new Error(`Failed to copy directory: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  async moveDirectory(sourcePath: string, destinationPath: string): Promise<void> {
    try {
      // Copy directory first
      await this.copyDirectory(sourcePath, destinationPath)

      // Delete source directory
      await this.deleteDirectory(sourcePath, true)
    } catch (error) {
      throw new Error(`Failed to move directory: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // Helper method for recursive directory copying
  private async copyDirectoryRecursive(sourcePath: string, destinationPath: string): Promise<void> {
    const entries = await fs.readdir(sourcePath, { withFileTypes: true })

    for (const entry of entries) {
      const srcPath = path.join(sourcePath, entry.name)
      const destPath = path.join(destinationPath, entry.name)

      if (entry.isDirectory()) {
        await fs.mkdir(destPath, { recursive: true })
        await this.copyDirectoryRecursive(srcPath, destPath)
      } else if (entry.isFile()) {
        await fs.copyFile(srcPath, destPath)
      }
    }
  }

  // Utility methods
  async exists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath)
      return true
    } catch {
      return false
    }
  }

  async getStats(filePath: string): Promise<FileStats> {
    try {
      const stats = await fs.stat(filePath)

      return {
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        accessed: stats.atime,
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory(),
        permissions: stats.mode.toString(8)
      }
    } catch (error) {
      throw new Error(`Failed to get file stats: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  async searchFiles(pattern: string, options: SearchOptions = {}): Promise<string[]> {
    if (!this.currentProject) {
      throw new Error('No project is currently open')
    }

    const {
      recursive = true,
      includeHidden = false,
      excludePatterns = ['node_modules', '.git', 'dist', 'build'],
      maxResults = 1000,
      caseSensitive = false
    } = options

    const results: string[] = []
    const searchPattern = caseSensitive ? pattern : pattern.toLowerCase()

    await this.searchInDirectory(
      this.currentProject.root,
      searchPattern,
      results,
      recursive,
      includeHidden,
      excludePatterns,
      maxResults,
      caseSensitive
    )

    return results
  }

  private async searchInDirectory(
    dirPath: string,
    pattern: string,
    results: string[],
    recursive: boolean,
    includeHidden: boolean,
    excludePatterns: string[],
    maxResults: number,
    caseSensitive: boolean
  ): Promise<void> {
    if (results.length >= maxResults) return

    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true })

      for (const entry of entries) {
        if (results.length >= maxResults) break

        const entryPath = path.join(dirPath, entry.name)
        const entryName = caseSensitive ? entry.name : entry.name.toLowerCase()

        // Skip hidden files if not included
        if (!includeHidden && entry.name.startsWith('.')) continue

        // Skip excluded patterns
        if (excludePatterns.some(exclude => entry.name.includes(exclude))) continue

        // Check if name matches pattern
        if (entryName.includes(pattern)) {
          results.push(entryPath)
        }

        // Recurse into directories if recursive is enabled
        if (recursive && entry.isDirectory()) {
          await this.searchInDirectory(
            entryPath,
            pattern,
            results,
            recursive,
            includeHidden,
            excludePatterns,
            maxResults,
            caseSensitive
          )
        }
      }
    } catch (error) {
      // Skip directories we can't read
      console.warn(`Cannot read directory ${dirPath}:`, error)
    }
  }

  // Directory parsing methods
  private async parseDirectory(dirPath: string, maxDepth: number = 10, currentDepth: number = 0): Promise<FileNode[]> {
    if (currentDepth >= maxDepth) {
      return []
    }

    const nodes: FileNode[] = []

    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true })

      // Sort entries: directories first, then files, both alphabetically
      entries.sort((a, b) => {
        if (a.isDirectory() && !b.isDirectory()) return -1
        if (!a.isDirectory() && b.isDirectory()) return 1
        return a.name.localeCompare(b.name)
      })

      for (const entry of entries) {
        // Skip hidden files and common ignore patterns
        if (this.shouldIgnoreEntry(entry.name)) continue

        const entryPath = path.join(dirPath, entry.name)

        try {
          const stats = await fs.stat(entryPath)

          if (entry.isDirectory()) {
            const children = await this.parseDirectory(entryPath, maxDepth, currentDepth + 1)

            nodes.push({
              name: entry.name,
              path: entryPath,
              type: 'folder',
              children,
              size: stats.size,
              lastModified: stats.mtime
            })
          } else if (entry.isFile()) {
            const extension = path.extname(entry.name)

            nodes.push({
              name: entry.name,
              path: entryPath,
              type: 'file',
              extension,
              size: stats.size,
              lastModified: stats.mtime
            })
          }
        } catch (error) {
          // Skip files/directories we can't access
          console.warn(`Cannot access ${entryPath}:`, error)
        }
      }
    } catch (error) {
      console.warn(`Cannot read directory ${dirPath}:`, error)
    }

    return nodes
  }

  private shouldIgnoreEntry(name: string): boolean {
    const ignorePatterns = [
      // Version control
      '.git', '.svn', '.hg',
      // Dependencies
      'node_modules', '__pycache__', '.venv', 'venv', 'env',
      // Build outputs
      'dist', 'build', 'out', 'target', 'bin', 'obj',
      // IDE files
      '.vscode', '.idea', '*.swp', '*.swo',
      // OS files
      '.DS_Store', 'Thumbs.db', 'desktop.ini',
      // Logs
      '*.log', 'logs',
      // Temporary files
      'tmp', 'temp', '.tmp', '.temp'
    ]

    return ignorePatterns.some(pattern => {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'))
        return regex.test(name)
      }
      return name === pattern || name.startsWith(pattern)
    })
  }

  // Project analysis methods
  async analyzeProject(projectPath: string): Promise<ProjectAnalysis> {
    const projectType = await this.detectProjectType(projectPath)
    const structure = await this.openProject(projectPath)
    const dependencies = await this.getProjectDependencies(projectPath)
    const frameworks = await this.detectFrameworks(projectPath)
    const packageManager = await this.detectPackageManager(projectPath)
    const buildSystem = await this.detectBuildSystem(projectPath)

    const configFiles = await this.findConfigFiles(projectPath)
    const entryPoints = await this.findEntryPoints(projectPath, projectType)
    const testFiles = await this.findTestFiles(projectPath)
    const documentationFiles = await this.findDocumentationFiles(projectPath)

    return {
      type: projectType,
      structure,
      dependencies,
      frameworks,
      packageManager,
      buildSystem,
      configFiles,
      entryPoints,
      testFiles,
      documentationFiles
    }
  }

  async detectProjectType(projectPath: string): Promise<ProjectType> {
    try {
      const files = await fs.readdir(projectPath)

      // Check for specific project indicators
      if (files.includes('package.json')) {
        const packageJson = await this.readPackageJson(projectPath)

        // React project
        if (packageJson.dependencies?.react || packageJson.devDependencies?.react) {
          return 'react'
        }

        // Vue project
        if (packageJson.dependencies?.vue || packageJson.devDependencies?.vue) {
          return 'vue'
        }

        // Angular project
        if (packageJson.dependencies?.['@angular/core'] || files.includes('angular.json')) {
          return 'angular'
        }

        // Node.js project
        return 'node'
      }

      // Python project
      if (files.some(f => ['requirements.txt', 'setup.py', 'pyproject.toml', 'Pipfile'].includes(f))) {
        return 'python'
      }

      // Java project
      if (files.some(f => ['pom.xml', 'build.gradle', 'build.gradle.kts'].includes(f))) {
        return 'java'
      }

      // C# project
      if (files.some(f => f.endsWith('.csproj') || f.endsWith('.sln'))) {
        return 'csharp'
      }

      // Go project
      if (files.includes('go.mod') || files.includes('go.sum')) {
        return 'go'
      }

      // Rust project
      if (files.includes('Cargo.toml')) {
        return 'rust'
      }

      // PHP project
      if (files.includes('composer.json')) {
        return 'php'
      }

      // Ruby project
      if (files.includes('Gemfile') || files.includes('Rakefile')) {
        return 'ruby'
      }

      return 'unknown'
    } catch (error) {
      console.warn('Error detecting project type:', error)
      return 'unknown'
    }
  }

  async getProjectDependencies(projectPath: string): Promise<Dependency[]> {
    const dependencies: Dependency[] = []

    try {
      // Node.js dependencies
      const packageJsonPath = path.join(projectPath, 'package.json')
      if (await this.exists(packageJsonPath)) {
        const packageJson = await this.readPackageJson(projectPath)

        // Production dependencies
        if (packageJson.dependencies) {
          for (const [name, version] of Object.entries(packageJson.dependencies)) {
            dependencies.push({
              name,
              version: version as string,
              type: 'production',
              source: 'npm'
            })
          }
        }

        // Development dependencies
        if (packageJson.devDependencies) {
          for (const [name, version] of Object.entries(packageJson.devDependencies)) {
            dependencies.push({
              name,
              version: version as string,
              type: 'development',
              source: 'npm'
            })
          }
        }
      }

      // Python dependencies
      const requirementsPath = path.join(projectPath, 'requirements.txt')
      if (await this.exists(requirementsPath)) {
        const content = await fs.readFile(requirementsPath, 'utf-8')
        const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'))

        for (const line of lines) {
          const match = line.match(/^([^=<>!]+)[=<>!]*(.*)$/)
          if (match) {
            dependencies.push({
              name: match[1].trim(),
              version: match[2].trim() || 'latest',
              type: 'production',
              source: 'pip'
            })
          }
        }
      }

      // Add more dependency parsers for other languages as needed

    } catch (error) {
      console.warn('Error reading dependencies:', error)
    }

    return dependencies
  }

  private async readPackageJson(projectPath: string): Promise<any> {
    try {
      const packageJsonPath = path.join(projectPath, 'package.json')
      const content = await fs.readFile(packageJsonPath, 'utf-8')
      return JSON.parse(content)
    } catch (error) {
      return {}
    }
  }

  private async detectFrameworks(projectPath: string): Promise<Framework[]> {
    const frameworks: Framework[] = []

    try {
      const packageJson = await this.readPackageJson(projectPath)
      const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }

      // Frontend frameworks
      if (dependencies.react) {
        frameworks.push({
          name: 'React',
          version: dependencies.react,
          type: 'frontend',
          configFiles: ['package.json']
        })
      }

      if (dependencies.vue) {
        frameworks.push({
          name: 'Vue.js',
          version: dependencies.vue,
          type: 'frontend',
          configFiles: ['package.json', 'vue.config.js']
        })
      }

      if (dependencies['@angular/core']) {
        frameworks.push({
          name: 'Angular',
          version: dependencies['@angular/core'],
          type: 'frontend',
          configFiles: ['package.json', 'angular.json']
        })
      }

      // Backend frameworks
      if (dependencies.express) {
        frameworks.push({
          name: 'Express.js',
          version: dependencies.express,
          type: 'backend',
          configFiles: ['package.json']
        })
      }

      if (dependencies.next) {
        frameworks.push({
          name: 'Next.js',
          version: dependencies.next,
          type: 'fullstack',
          configFiles: ['package.json', 'next.config.js']
        })
      }

      // Testing frameworks
      if (dependencies.jest) {
        frameworks.push({
          name: 'Jest',
          version: dependencies.jest,
          type: 'testing',
          configFiles: ['package.json', 'jest.config.js']
        })
      }

      // Build tools
      if (dependencies.webpack) {
        frameworks.push({
          name: 'Webpack',
          version: dependencies.webpack,
          type: 'build',
          configFiles: ['webpack.config.js']
        })
      }

      if (dependencies.vite) {
        frameworks.push({
          name: 'Vite',
          version: dependencies.vite,
          type: 'build',
          configFiles: ['vite.config.js', 'vite.config.ts']
        })
      }

    } catch (error) {
      console.warn('Error detecting frameworks:', error)
    }

    return frameworks
  }

  private async detectPackageManager(projectPath: string): Promise<PackageManager | undefined> {
    try {
      const files = await fs.readdir(projectPath)

      if (files.includes('pnpm-lock.yaml')) {
        return {
          name: 'pnpm',
          configFile: 'package.json',
          lockFile: 'pnpm-lock.yaml',
          installCommand: 'pnpm install',
          runCommand: 'pnpm run'
        }
      }

      if (files.includes('yarn.lock')) {
        return {
          name: 'yarn',
          configFile: 'package.json',
          lockFile: 'yarn.lock',
          installCommand: 'yarn install',
          runCommand: 'yarn run'
        }
      }

      if (files.includes('package-lock.json')) {
        return {
          name: 'npm',
          configFile: 'package.json',
          lockFile: 'package-lock.json',
          installCommand: 'npm install',
          runCommand: 'npm run'
        }
      }

      if (files.includes('requirements.txt')) {
        return {
          name: 'pip',
          configFile: 'requirements.txt',
          installCommand: 'pip install -r requirements.txt',
          runCommand: 'python'
        }
      }

      if (files.includes('Cargo.toml')) {
        return {
          name: 'cargo',
          configFile: 'Cargo.toml',
          lockFile: 'Cargo.lock',
          installCommand: 'cargo build',
          runCommand: 'cargo run'
        }
      }

    } catch (error) {
      console.warn('Error detecting package manager:', error)
    }

    return undefined
  }

  // File watching methods
  watchFiles(callback: (event: FileEvent) => void): () => void {
    this.watchCallbacks.add(callback)

    // Return unwatch function
    return () => {
      this.watchCallbacks.delete(callback)
    }
  }

  unwatchFiles(): void {
    this.watchCallbacks.clear()
    if (this.watcher) {
      this.watcher.close()
      this.watcher = null
    }
  }

  private startWatching(projectPath: string): void {
    if (this.watcher) {
      this.watcher.close()
    }

    // Configure chokidar options
    const watchOptions = {
      ignored: [
        // Version control
        /(^|[\/\\])\../, // Hidden files
        /node_modules/,
        /__pycache__/,
        /\.git/,
        /\.svn/,
        /\.hg/,
        // Build outputs
        /dist/,
        /build/,
        /out/,
        /target/,
        /bin/,
        /obj/,
        // Logs and temp files
        /\.log$/,
        /logs/,
        /tmp/,
        /temp/,
        // IDE files
        /\.vscode/,
        /\.idea/,
        /\.swp$/,
        /\.swo$/,
        // OS files
        /\.DS_Store$/,
        /Thumbs\.db$/,
        /desktop\.ini$/
      ],
      persistent: true,
      ignoreInitial: true,
      followSymlinks: false,
      depth: 10,
      awaitWriteFinish: {
        stabilityThreshold: 100,
        pollInterval: 50
      }
    }

    this.watcher = watch(projectPath, watchOptions)

    // Set up event listeners
    this.watcher
      .on('add', (filePath: string, stats?: any) => {
        this.emitFileEvent({
          type: 'add',
          path: filePath,
          stats
        })
      })
      .on('change', (filePath: string, stats?: any) => {
        this.emitFileEvent({
          type: 'change',
          path: filePath,
          stats
        })
      })
      .on('unlink', (filePath: string) => {
        this.emitFileEvent({
          type: 'unlink',
          path: filePath
        })
      })
      .on('addDir', (dirPath: string, stats?: any) => {
        this.emitFileEvent({
          type: 'addDir',
          path: dirPath,
          stats
        })
      })
      .on('unlinkDir', (dirPath: string) => {
        this.emitFileEvent({
          type: 'unlinkDir',
          path: dirPath
        })
      })
      .on('error', (error: Error) => {
        console.error('File watcher error:', error)
      })
      .on('ready', () => {
        console.log('File watcher ready')
      })
  }

  private emitFileEvent(event: FileEvent): void {
    // Emit to all registered callbacks
    for (const callback of this.watchCallbacks) {
      try {
        callback(event)
      } catch (error) {
        console.error('Error in file watch callback:', error)
      }
    }

    // Auto-refresh project structure on significant changes
    if (event.type === 'addDir' || event.type === 'unlinkDir') {
      this.debounceRefresh()
    }
  }

  private refreshTimeout: NodeJS.Timeout | null = null
  private debounceRefresh(): void {
    if (this.refreshTimeout) {
      clearTimeout(this.refreshTimeout)
    }

    this.refreshTimeout = setTimeout(async () => {
      try {
        await this.refreshProject()
      } catch (error) {
        console.error('Error refreshing project:', error)
      }
    }, 500) // 500ms debounce
  }

  // File type detection methods
  async detectFileType(filePath: string): Promise<FileTypeInfo> {
    const extension = path.extname(filePath).toLowerCase()
    const basename = path.basename(filePath).toLowerCase()

    // Get basic info
    const language = this.getLanguageFromExtension(extension)
    const isBinary = await this.isBinaryFile(filePath)
    const mimeType = this.getMimeType(extension, basename)
    const category = this.getFileCategory(extension, basename, language)

    return {
      extension,
      mimeType,
      language,
      category,
      isBinary,
      isText: !isBinary,
      encoding: isBinary ? undefined : 'utf-8'
    }
  }

  getLanguageFromExtension(extension: string): string {
    const languageMap: Record<string, string> = {
      // JavaScript/TypeScript
      '.js': 'javascript',
      '.jsx': 'javascriptreact',
      '.ts': 'typescript',
      '.tsx': 'typescriptreact',
      '.mjs': 'javascript',
      '.cjs': 'javascript',

      // Web technologies
      '.html': 'html',
      '.htm': 'html',
      '.css': 'css',
      '.scss': 'scss',
      '.sass': 'sass',
      '.less': 'less',
      '.vue': 'vue',
      '.svelte': 'svelte',

      // Python
      '.py': 'python',
      '.pyw': 'python',
      '.pyi': 'python',
      '.pyx': 'python',

      // Java
      '.java': 'java',
      '.class': 'java',
      '.jar': 'java',

      // C/C++
      '.c': 'c',
      '.h': 'c',
      '.cpp': 'cpp',
      '.cxx': 'cpp',
      '.cc': 'cpp',
      '.hpp': 'cpp',
      '.hxx': 'cpp',

      // C#
      '.cs': 'csharp',
      '.csx': 'csharp',

      // Go
      '.go': 'go',

      // Rust
      '.rs': 'rust',

      // PHP
      '.php': 'php',
      '.phtml': 'php',
      '.php3': 'php',
      '.php4': 'php',
      '.php5': 'php',

      // Ruby
      '.rb': 'ruby',
      '.rbw': 'ruby',
      '.rake': 'ruby',
      '.gemspec': 'ruby',

      // Swift
      '.swift': 'swift',

      // Kotlin
      '.kt': 'kotlin',
      '.kts': 'kotlin',

      // Dart
      '.dart': 'dart',

      // Shell scripts
      '.sh': 'shellscript',
      '.bash': 'shellscript',
      '.zsh': 'shellscript',
      '.fish': 'shellscript',
      '.ps1': 'powershell',
      '.psm1': 'powershell',
      '.psd1': 'powershell',

      // Data formats
      '.json': 'json',
      '.jsonc': 'jsonc',
      '.xml': 'xml',
      '.yaml': 'yaml',
      '.yml': 'yaml',
      '.toml': 'toml',
      '.ini': 'ini',
      '.cfg': 'ini',
      '.conf': 'ini',

      // Documentation
      '.md': 'markdown',
      '.markdown': 'markdown',
      '.mdown': 'markdown',
      '.mkd': 'markdown',
      '.rst': 'restructuredtext',
      '.txt': 'plaintext',

      // SQL
      '.sql': 'sql',
      '.mysql': 'sql',
      '.pgsql': 'sql',

      // Docker
      '.dockerfile': 'dockerfile',

      // Prisma
      '.prisma': 'prisma',

      // Environment files
      '.env': 'dotenv',
      '.env.local': 'dotenv',
      '.env.development': 'dotenv',
      '.env.production': 'dotenv',

      // Git
      '.gitignore': 'ignore',
      '.gitattributes': 'gitattributes',

      // Other config files
      '.editorconfig': 'editorconfig',
      '.eslintrc': 'json',
      '.prettierrc': 'json',
      '.babelrc': 'json'
    }

    return languageMap[extension] || 'plaintext'
  }

  async isBinaryFile(filePath: string): Promise<boolean> {
    const extension = path.extname(filePath).toLowerCase()

    // Known binary extensions
    const binaryExtensions = new Set([
      // Executables
      '.exe', '.dll', '.so', '.dylib', '.bin',
      // Archives
      '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz',
      // Images
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.svg', '.ico', '.webp',
      // Videos
      '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv',
      // Audio
      '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma',
      // Documents
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
      // Fonts
      '.ttf', '.otf', '.woff', '.woff2', '.eot',
      // Other
      '.db', '.sqlite', '.sqlite3'
    ])

    if (binaryExtensions.has(extension)) {
      return true
    }

    // For unknown extensions, check if it's in our text extensions set
    if (this.textExtensions.has(extension)) {
      return false
    }

    // For files without extension or unknown extensions, sample the content
    try {
      const stats = await this.getStats(filePath)
      if (stats.size === 0) return false
      if (stats.size > this.maxFileSize) return true

      // Read first 1KB to check for binary content
      const buffer = Buffer.alloc(Math.min(1024, stats.size))
      const fd = await fs.open(filePath, 'r')
      await fd.read(buffer, 0, buffer.length, 0)
      await fd.close()

      // Check for null bytes (common in binary files)
      return buffer.includes(0)
    } catch (error) {
      // If we can't read the file, assume it's binary
      return true
    }
  }

  private getMimeType(extension: string, basename: string): string {
    const mimeMap: Record<string, string> = {
      // Text files
      '.txt': 'text/plain',
      '.md': 'text/markdown',
      '.html': 'text/html',
      '.css': 'text/css',
      '.js': 'text/javascript',
      '.ts': 'text/typescript',
      '.json': 'application/json',
      '.xml': 'application/xml',
      '.yaml': 'application/x-yaml',
      '.yml': 'application/x-yaml',

      // Images
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.bmp': 'image/bmp',
      '.webp': 'image/webp',

      // Archives
      '.zip': 'application/zip',
      '.tar': 'application/x-tar',
      '.gz': 'application/gzip',

      // Documents
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',

      // Audio/Video
      '.mp3': 'audio/mpeg',
      '.mp4': 'video/mp4',
      '.wav': 'audio/wav',

      // Fonts
      '.ttf': 'font/ttf',
      '.woff': 'font/woff',
      '.woff2': 'font/woff2'
    }

    return mimeMap[extension] || 'application/octet-stream'
  }

  private getFileCategory(extension: string, basename: string, language: string): FileTypeInfo['category'] {
    // Configuration files
    const configFiles = [
      'package.json', 'tsconfig.json', 'webpack.config.js', 'vite.config.js',
      '.eslintrc', '.prettierrc', '.babelrc', '.editorconfig', 'docker-compose.yml',
      'Dockerfile', '.gitignore', '.env'
    ]

    if (configFiles.some(config => basename.includes(config.toLowerCase()))) {
      return 'config'
    }

    // Code files
    const codeLanguages = [
      'javascript', 'typescript', 'python', 'java', 'csharp', 'cpp', 'c',
      'go', 'rust', 'php', 'ruby', 'swift', 'kotlin', 'dart'
    ]

    if (codeLanguages.includes(language)) {
      return 'code'
    }

    // Data files
    const dataExtensions = ['.json', '.xml', '.yaml', '.yml', '.csv', '.sql']
    if (dataExtensions.includes(extension)) {
      return 'data'
    }

    // Image files
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.bmp', '.webp']
    if (imageExtensions.includes(extension)) {
      return 'image'
    }

    // Document files
    const docExtensions = ['.md', '.txt', '.pdf', '.doc', '.docx']
    if (docExtensions.includes(extension)) {
      return 'document'
    }

    // Binary files
    const binaryExtensions = ['.exe', '.dll', '.so', '.zip', '.tar', '.gz']
    if (binaryExtensions.includes(extension)) {
      return 'binary'
    }

    return 'unknown'
  }

  private initializeFileTypeDetection(): void {
    // This method can be used to initialize any file type detection resources
    // For now, it's just a placeholder for future enhancements
  }

  // Additional helper methods for project analysis
  private async detectBuildSystem(projectPath: string): Promise<BuildSystem | undefined> {
    try {
      const files = await fs.readdir(projectPath)

      if (files.includes('webpack.config.js') || files.includes('webpack.config.ts')) {
        return {
          name: 'webpack',
          configFile: 'webpack.config.js',
          buildCommand: 'npm run build',
          devCommand: 'npm run dev',
          outputDir: 'dist'
        }
      }

      if (files.includes('vite.config.js') || files.includes('vite.config.ts')) {
        return {
          name: 'vite',
          configFile: 'vite.config.js',
          buildCommand: 'npm run build',
          devCommand: 'npm run dev',
          outputDir: 'dist'
        }
      }

      if (files.includes('rollup.config.js')) {
        return {
          name: 'rollup',
          configFile: 'rollup.config.js',
          buildCommand: 'npm run build',
          outputDir: 'dist'
        }
      }

      if (files.includes('tsconfig.json')) {
        return {
          name: 'tsc',
          configFile: 'tsconfig.json',
          buildCommand: 'tsc',
          outputDir: 'dist'
        }
      }

    } catch (error) {
      console.warn('Error detecting build system:', error)
    }

    return undefined
  }

  private async findConfigFiles(projectPath: string): Promise<string[]> {
    const configFiles: string[] = []
    const commonConfigFiles = [
      'package.json', 'tsconfig.json', 'webpack.config.js', 'vite.config.js',
      'rollup.config.js', 'babel.config.js', '.babelrc', '.eslintrc.js',
      '.eslintrc.json', '.prettierrc', '.editorconfig', 'jest.config.js',
      'docker-compose.yml', 'Dockerfile', '.gitignore', '.env',
      'requirements.txt', 'setup.py', 'Cargo.toml', 'go.mod', 'pom.xml',
      'build.gradle', 'composer.json', 'Gemfile'
    ]

    for (const configFile of commonConfigFiles) {
      const configPath = path.join(projectPath, configFile)
      if (await this.exists(configPath)) {
        configFiles.push(configPath)
      }
    }

    return configFiles
  }

  private async findEntryPoints(projectPath: string, projectType: ProjectType): Promise<string[]> {
    const entryPoints: string[] = []
    const commonEntryPoints = [
      'index.js', 'index.ts', 'main.js', 'main.ts', 'app.js', 'app.ts',
      'server.js', 'server.ts', 'index.html', 'main.py', '__main__.py',
      'main.go', 'main.rs', 'index.php', 'app.php'
    ]

    // Check package.json main field for Node.js projects
    if (projectType === 'node' || projectType === 'react' || projectType === 'vue') {
      try {
        const packageJson = await this.readPackageJson(projectPath)
        if (packageJson.main) {
          const mainPath = path.join(projectPath, packageJson.main)
          if (await this.exists(mainPath)) {
            entryPoints.push(mainPath)
          }
        }
      } catch (error) {
        // Ignore errors
      }
    }

    // Check common entry point files
    for (const entryPoint of commonEntryPoints) {
      const entryPath = path.join(projectPath, entryPoint)
      if (await this.exists(entryPath)) {
        entryPoints.push(entryPath)
      }
    }

    // Check src directory
    const srcPath = path.join(projectPath, 'src')
    if (await this.exists(srcPath)) {
      for (const entryPoint of commonEntryPoints) {
        const entryPath = path.join(srcPath, entryPoint)
        if (await this.exists(entryPath)) {
          entryPoints.push(entryPath)
        }
      }
    }

    return entryPoints
  }

  private async findTestFiles(projectPath: string): Promise<string[]> {
    const testFiles: string[] = []
    const testPatterns = [
      '**/*.test.js', '**/*.test.ts', '**/*.spec.js', '**/*.spec.ts',
      '**/test/**/*.js', '**/test/**/*.ts', '**/tests/**/*.js', '**/tests/**/*.ts',
      '**/__tests__/**/*.js', '**/__tests__/**/*.ts'
    ]

    try {
      const results = await this.searchFiles('test', {
        recursive: true,
        includeHidden: false,
        excludePatterns: ['node_modules', '.git'],
        maxResults: 100
      })

      // Filter for actual test files
      for (const file of results) {
        const basename = path.basename(file).toLowerCase()
        if (basename.includes('test') || basename.includes('spec')) {
          testFiles.push(file)
        }
      }
    } catch (error) {
      console.warn('Error finding test files:', error)
    }

    return testFiles
  }

  private async findDocumentationFiles(projectPath: string): Promise<string[]> {
    const docFiles: string[] = []
    const commonDocFiles = [
      'README.md', 'README.txt', 'CHANGELOG.md', 'CONTRIBUTING.md',
      'LICENSE', 'LICENSE.md', 'docs/README.md', 'documentation/README.md'
    ]

    for (const docFile of commonDocFiles) {
      const docPath = path.join(projectPath, docFile)
      if (await this.exists(docPath)) {
        docFiles.push(docPath)
      }
    }

    // Search for markdown files in docs directories
    try {
      const results = await this.searchFiles('.md', {
        recursive: true,
        includeHidden: false,
        excludePatterns: ['node_modules', '.git'],
        maxResults: 50
      })

      for (const file of results) {
        const relativePath = path.relative(projectPath, file)
        if (relativePath.includes('doc') || relativePath.includes('readme')) {
          docFiles.push(file)
        }
      }
    } catch (error) {
      console.warn('Error finding documentation files:', error)
    }

    return docFiles
  }

  // Cleanup method
  cleanup(): void {
    this.unwatchFiles()
    this.currentProject = null
    if (this.refreshTimeout) {
      clearTimeout(this.refreshTimeout)
      this.refreshTimeout = null
    }
  }
}