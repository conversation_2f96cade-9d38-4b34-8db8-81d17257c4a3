/**
 * Drop Indicator Component
 * Visual feedback for drag and drop operations
 */

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '../../utils'

export interface DropIndicatorProps {
  position: 'before' | 'after' | 'inside' | null
  isValid: boolean
  className?: string
  children?: React.ReactNode
}

export const DropIndicator: React.FC<DropIndicatorProps> = ({
  position,
  isValid,
  className,
  children
}) => {
  if (!position) return <>{children}</>

  const indicatorClass = cn(
    'absolute left-0 right-0 h-0.5 z-10',
    isValid ? 'bg-primary' : 'bg-destructive',
    className
  )

  const overlayClass = cn(
    'absolute inset-0 z-10 rounded',
    isValid 
      ? 'bg-primary/10 border-2 border-primary border-dashed' 
      : 'bg-destructive/10 border-2 border-destructive border-dashed'
  )

  return (
    <div className="relative">
      {children}
      
      <AnimatePresence>
        {position === 'before' && (
          <motion.div
            initial={{ scaleX: 0, opacity: 0 }}
            animate={{ scaleX: 1, opacity: 1 }}
            exit={{ scaleX: 0, opacity: 0 }}
            transition={{ duration: 0.15 }}
            className={cn(indicatorClass, '-top-px')}
          />
        )}
        
        {position === 'after' && (
          <motion.div
            initial={{ scaleX: 0, opacity: 0 }}
            animate={{ scaleX: 1, opacity: 1 }}
            exit={{ scaleX: 0, opacity: 0 }}
            transition={{ duration: 0.15 }}
            className={cn(indicatorClass, '-bottom-px')}
          />
        )}
        
        {position === 'inside' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.15 }}
            className={overlayClass}
          />
        )}
      </AnimatePresence>
    </div>
  )
}

export interface DragPreviewProps {
  node: {
    name: string
    type: 'file' | 'folder'
  }
  position: { x: number; y: number }
  visible: boolean
  className?: string
}

export const DragPreview: React.FC<DragPreviewProps> = ({
  node,
  position,
  visible,
  className
}) => {
  if (!visible) return null

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className={cn(
        'fixed z-50 pointer-events-none',
        'bg-popover border border-border rounded-md shadow-lg',
        'px-3 py-2 text-sm',
        'flex items-center gap-2',
        'max-w-xs',
        className
      )}
      style={{
        left: position.x + 10,
        top: position.y - 10
      }}
    >
      <span className="text-lg">
        {node.type === 'folder' ? '📁' : '📄'}
      </span>
      <span className="truncate font-medium">
        {node.name}
      </span>
    </motion.div>
  )
}

export interface DropZoneProps {
  onDrop: (event: React.DragEvent) => void
  onDragOver: (event: React.DragEvent) => void
  onDragLeave: (event: React.DragEvent) => void
  isActive: boolean
  isValid: boolean
  className?: string
  children: React.ReactNode
}

export const DropZone: React.FC<DropZoneProps> = ({
  onDrop,
  onDragOver,
  onDragLeave,
  isActive,
  isValid,
  className,
  children
}) => {
  return (
    <div
      onDrop={onDrop}
      onDragOver={onDragOver}
      onDragLeave={onDragLeave}
      className={cn(
        'relative transition-colors duration-150',
        isActive && isValid && 'bg-primary/5',
        isActive && !isValid && 'bg-destructive/5',
        className
      )}
    >
      {children}
      
      {isActive && (
        <div
          className={cn(
            'absolute inset-0 border-2 border-dashed rounded pointer-events-none',
            isValid ? 'border-primary' : 'border-destructive'
          )}
        />
      )}
    </div>
  )
}

export interface DragHandleProps {
  onDragStart: (event: React.DragEvent) => void
  onDragEnd: (event: React.DragEvent) => void
  disabled?: boolean
  className?: string
  children?: React.ReactNode
}

export const DragHandle: React.FC<DragHandleProps> = ({
  onDragStart,
  onDragEnd,
  disabled = false,
  className,
  children
}) => {
  return (
    <div
      draggable={!disabled}
      onDragStart={disabled ? undefined : onDragStart}
      onDragEnd={disabled ? undefined : onDragEnd}
      className={cn(
        'cursor-grab active:cursor-grabbing',
        disabled && 'cursor-not-allowed opacity-50',
        className
      )}
      role="button"
      tabIndex={disabled ? -1 : 0}
      aria-label="Drag to move"
    >
      {children || (
        <div className="flex flex-col gap-0.5 p-1">
          <div className="w-1 h-1 bg-muted-foreground rounded-full" />
          <div className="w-1 h-1 bg-muted-foreground rounded-full" />
          <div className="w-1 h-1 bg-muted-foreground rounded-full" />
          <div className="w-1 h-1 bg-muted-foreground rounded-full" />
          <div className="w-1 h-1 bg-muted-foreground rounded-full" />
          <div className="w-1 h-1 bg-muted-foreground rounded-full" />
        </div>
      )}
    </div>
  )
}

// Utility component for drag and drop feedback
export interface DragDropFeedbackProps {
  isDragging: boolean
  dragItem: {
    node: { name: string; type: 'file' | 'folder' }
    count?: number
  } | null
  dropTarget: {
    node: { name: string; type: 'file' | 'folder' } | null
    position: 'before' | 'after' | 'inside' | null
    isValid: boolean
  } | null
  className?: string
}

export const DragDropFeedback: React.FC<DragDropFeedbackProps> = ({
  isDragging,
  dragItem,
  dropTarget,
  className
}) => {
  if (!isDragging || !dragItem) return null

  const getDropMessage = () => {
    if (!dropTarget?.node || !dropTarget.isValid) {
      return 'Drop not allowed here'
    }

    const { node, position } = dropTarget
    const itemCount = dragItem.count || 1
    const itemText = itemCount === 1 ? dragItem.node.name : `${itemCount} items`

    switch (position) {
      case 'inside':
        return `Move ${itemText} into ${node.name}`
      case 'before':
        return `Move ${itemText} before ${node.name}`
      case 'after':
        return `Move ${itemText} after ${node.name}`
      default:
        return 'Drop to move'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      className={cn(
        'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50',
        'bg-popover border border-border rounded-lg shadow-lg',
        'px-4 py-2 text-sm',
        'pointer-events-none',
        className
      )}
    >
      <div className="flex items-center gap-2">
        <span className="text-lg">
          {dragItem.node.type === 'folder' ? '📁' : '📄'}
        </span>
        <span className={cn(
          'font-medium',
          dropTarget?.isValid ? 'text-foreground' : 'text-destructive'
        )}>
          {getDropMessage()}
        </span>
      </div>
    </motion.div>
  )
}

export default DropIndicator
