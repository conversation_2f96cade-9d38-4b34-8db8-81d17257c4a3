/**
 * Status Bar Component
 * Bottom status bar with project info, AI status, and system information
 */

import React from 'react'
import { motion } from 'framer-motion'
import { 
  Zap, 
  Wifi, 
  WifiOff, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  FileText,
  GitBranch,
  Users
} from 'lucide-react'
import { useLayoutStore } from '../../stores/layoutStore'
import { useAIStore } from '../../stores/aiStore'
import { Badge } from '../ui'
import { cn } from '../../utils'

export function StatusBar() {
  const { togglePanel } = useLayoutStore()
  const { 
    isConnected, 
    currentModel, 
    tokenUsage, 
    isProcessing 
  } = useAIStore()

  // Mock data - these would come from actual stores
  const projectInfo = {
    name: 'AI Code Editor',
    branch: 'main',
    files: 42,
    language: 'TypeScript',
    encoding: 'UTF-8',
    lineEnding: 'LF',
  }

  const handleAIStatusClick = () => {
    togglePanel('chat')
  }

  const handleProjectClick = () => {
    togglePanel('sidebar')
  }

  const handleTerminalClick = () => {
    togglePanel('terminal')
  }

  return (
    <motion.div
      className="flex items-center justify-between h-6 bg-card border-t border-border px-2 text-xs"
      initial={{ opacity: 0, y: 6 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
    >
      {/* Left Section - Project Info */}
      <div className="flex items-center space-x-4">
        {/* Project Name */}
        <button
          onClick={handleProjectClick}
          className="flex items-center space-x-1 hover:bg-accent hover:text-accent-foreground px-1 py-0.5 rounded transition-colors"
          title="Open project explorer"
        >
          <FileText className="w-3 h-3" />
          <span>{projectInfo.name}</span>
        </button>

        {/* Git Branch */}
        <div className="flex items-center space-x-1 text-muted-foreground">
          <GitBranch className="w-3 h-3" />
          <span>{projectInfo.branch}</span>
        </div>

        {/* File Count */}
        <div className="flex items-center space-x-1 text-muted-foreground">
          <span>{projectInfo.files} files</span>
        </div>
      </div>

      {/* Center Section - AI Status */}
      <div className="flex items-center space-x-3">
        {/* AI Connection Status */}
        <button
          onClick={handleAIStatusClick}
          className={cn(
            'flex items-center space-x-2 px-2 py-0.5 rounded transition-colors',
            'hover:bg-accent hover:text-accent-foreground',
            isConnected ? 'text-success' : 'text-error'
          )}
          title={`AI Status: ${isConnected ? 'Connected' : 'Disconnected'}`}
        >
          {isConnected ? (
            <Wifi className="w-3 h-3" />
          ) : (
            <WifiOff className="w-3 h-3" />
          )}
          <span>{currentModel || 'No Model'}</span>
          {isProcessing && (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            >
              <Zap className="w-3 h-3" />
            </motion.div>
          )}
        </button>

        {/* Token Usage */}
        {isConnected && tokenUsage && (
          <div className="flex items-center space-x-1 text-muted-foreground">
            <Clock className="w-3 h-3" />
            <span>{tokenUsage.used}/{tokenUsage.limit} tokens</span>
          </div>
        )}

        {/* Status Indicator */}
        <div className="flex items-center space-x-1">
          {isProcessing ? (
            <Badge variant="warning" className="text-xs">
              Processing...
            </Badge>
          ) : isConnected ? (
            <Badge variant="success" className="text-xs">
              Ready
            </Badge>
          ) : (
            <Badge variant="error" className="text-xs">
              Disconnected
            </Badge>
          )}
        </div>
      </div>

      {/* Right Section - System Info */}
      <div className="flex items-center space-x-4 text-muted-foreground">
        {/* Language */}
        <span>{projectInfo.language}</span>

        {/* Encoding */}
        <span>{projectInfo.encoding}</span>

        {/* Line Ending */}
        <span>{projectInfo.lineEnding}</span>

        {/* Terminal Toggle */}
        <button
          onClick={handleTerminalClick}
          className="hover:bg-accent hover:text-accent-foreground px-1 py-0.5 rounded transition-colors"
          title="Toggle terminal (Ctrl+`)"
        >
          Terminal
        </button>

        {/* Collaboration Status */}
        <div className="flex items-center space-x-1">
          <Users className="w-3 h-3" />
          <span>Solo</span>
        </div>

        {/* Time */}
        <div className="flex items-center space-x-1">
          <Clock className="w-3 h-3" />
          <span>{new Date().toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}</span>
        </div>
      </div>
    </motion.div>
  )
}
