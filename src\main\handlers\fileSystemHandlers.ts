import { ipc<PERSON><PERSON>, BrowserWindow } from 'electron'
import { FileSystemManager } from '../../services/filesystem/FileSystemManager'
import { ProjectStructure, FileContent, FileEvent } from '../../types'
import { FileTypeInfo, FileStats, SearchOptions, ProjectAnalysis } from '../../services/ai/interfaces'

/**
 * File System Handlers for IPC communication
 */
export class FileSystemHandlers {
  private fileSystemManager: FileSystemManager
  private mainWindow: BrowserWindow | null = null

  constructor() {
    this.fileSystemManager = new FileSystemManager()
    this.registerHandlers()
    this.setupFileWatching()
  }

  setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window
  }

  private registerHandlers(): void {
    // Project operations
    ipcMain.handle('fs:openProject', this.handleOpenProject.bind(this))
    ipcMain.handle('fs:closeProject', this.handleCloseProject.bind(this))
    ipcMain.handle('fs:getProjectStructure', this.handleGetProjectStructure.bind(this))
    ipcMain.handle('fs:refreshProject', this.handleRefreshProject.bind(this))
    ipcMain.handle('fs:analyzeProject', this.handleAnalyzeProject.bind(this))
    ipcMain.handle('fs:detectProjectType', this.handleDetectProjectType.bind(this))

    // File operations
    ipcMain.handle('fs:readFile', this.handleReadFile.bind(this))
    ipcMain.handle('fs:writeFile', this.handleWriteFile.bind(this))
    ipcMain.handle('fs:createFile', this.handleCreateFile.bind(this))
    ipcMain.handle('fs:deleteFile', this.handleDeleteFile.bind(this))
    ipcMain.handle('fs:copyFile', this.handleCopyFile.bind(this))
    ipcMain.handle('fs:moveFile', this.handleMoveFile.bind(this))

    // Directory operations
    ipcMain.handle('fs:createDirectory', this.handleCreateDirectory.bind(this))
    ipcMain.handle('fs:deleteDirectory', this.handleDeleteDirectory.bind(this))
    ipcMain.handle('fs:copyDirectory', this.handleCopyDirectory.bind(this))
    ipcMain.handle('fs:moveDirectory', this.handleMoveDirectory.bind(this))

    // File type detection
    ipcMain.handle('fs:detectFileType', this.handleDetectFileType.bind(this))
    ipcMain.handle('fs:getLanguageFromExtension', this.handleGetLanguageFromExtension.bind(this))
    ipcMain.handle('fs:isBinaryFile', this.handleIsBinaryFile.bind(this))

    // Utility operations
    ipcMain.handle('fs:exists', this.handleExists.bind(this))
    ipcMain.handle('fs:getStats', this.handleGetStats.bind(this))
    ipcMain.handle('fs:searchFiles', this.handleSearchFiles.bind(this))
    ipcMain.handle('fs:getProjectDependencies', this.handleGetProjectDependencies.bind(this))

    // File watching
    ipcMain.handle('fs:startWatching', this.handleStartWatching.bind(this))
    ipcMain.handle('fs:stopWatching', this.handleStopWatching.bind(this))
  }

  // Project operation handlers
  private async handleOpenProject(_event: any, projectPath: string): Promise<ProjectStructure> {
    try {
      return await this.fileSystemManager.openProject(projectPath)
    } catch (error) {
      throw new Error(`Failed to open project: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleCloseProject(): Promise<void> {
    await this.fileSystemManager.closeProject()
  }

  private async handleGetProjectStructure(): Promise<ProjectStructure | null> {
    return this.fileSystemManager.getProjectStructure()
  }

  private async handleRefreshProject(): Promise<void> {
    await this.fileSystemManager.refreshProject()
  }

  private async handleAnalyzeProject(_event: any, projectPath: string): Promise<ProjectAnalysis> {
    return await this.fileSystemManager.analyzeProject(projectPath)
  }

  private async handleDetectProjectType(_event: any, projectPath: string): Promise<string> {
    return await this.fileSystemManager.detectProjectType(projectPath)
  }

  // File operation handlers
  private async handleReadFile(_event: any, filePath: string): Promise<FileContent> {
    return await this.fileSystemManager.readFile(filePath)
  }

  private async handleWriteFile(_event: any, filePath: string, content: string): Promise<void> {
    await this.fileSystemManager.writeFile(filePath, content)
  }

  private async handleCreateFile(_event: any, filePath: string, content?: string): Promise<void> {
    await this.fileSystemManager.createFile(filePath, content)
  }

  private async handleDeleteFile(_event: any, filePath: string): Promise<void> {
    await this.fileSystemManager.deleteFile(filePath)
  }

  private async handleCopyFile(_event: any, sourcePath: string, destinationPath: string): Promise<void> {
    await this.fileSystemManager.copyFile(sourcePath, destinationPath)
  }

  private async handleMoveFile(_event: any, sourcePath: string, destinationPath: string): Promise<void> {
    await this.fileSystemManager.moveFile(sourcePath, destinationPath)
  }

  // Directory operation handlers
  private async handleCreateDirectory(_event: any, dirPath: string): Promise<void> {
    await this.fileSystemManager.createDirectory(dirPath)
  }

  private async handleDeleteDirectory(_event: any, dirPath: string, recursive?: boolean): Promise<void> {
    await this.fileSystemManager.deleteDirectory(dirPath, recursive)
  }

  private async handleCopyDirectory(_event: any, sourcePath: string, destinationPath: string): Promise<void> {
    await this.fileSystemManager.copyDirectory(sourcePath, destinationPath)
  }

  private async handleMoveDirectory(_event: any, sourcePath: string, destinationPath: string): Promise<void> {
    await this.fileSystemManager.moveDirectory(sourcePath, destinationPath)
  }

  // File type detection handlers
  private async handleDetectFileType(_event: any, filePath: string): Promise<FileTypeInfo> {
    return await this.fileSystemManager.detectFileType(filePath)
  }

  private async handleGetLanguageFromExtension(_event: any, extension: string): Promise<string> {
    return this.fileSystemManager.getLanguageFromExtension(extension)
  }

  private async handleIsBinaryFile(_event: any, filePath: string): Promise<boolean> {
    return await this.fileSystemManager.isBinaryFile(filePath)
  }

  // Utility operation handlers
  private async handleExists(_event: any, filePath: string): Promise<boolean> {
    return await this.fileSystemManager.exists(filePath)
  }

  private async handleGetStats(_event: any, filePath: string): Promise<FileStats> {
    return await this.fileSystemManager.getStats(filePath)
  }

  private async handleSearchFiles(_event: any, pattern: string, options?: SearchOptions): Promise<string[]> {
    return await this.fileSystemManager.searchFiles(pattern, options)
  }

  private async handleGetProjectDependencies(_event: any, projectPath: string): Promise<any[]> {
    return await this.fileSystemManager.getProjectDependencies(projectPath)
  }

  // File watching handlers
  private async handleStartWatching(): Promise<void> {
    // File watching is automatically started when a project is opened
    // This handler can be used for additional watch setup if needed
  }

  private async handleStopWatching(): Promise<void> {
    this.fileSystemManager.unwatchFiles()
  }

  // File watching setup
  private setupFileWatching(): void {
    // Set up file watching to emit events to renderer process
    this.fileSystemManager.watchFiles((event: FileEvent) => {
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send('fs:fileChanged', event)
      }
    })
  }

  // Cleanup method
  cleanup(): void {
    // Remove all IPC handlers
    ipcMain.removeAllListeners('fs:openProject')
    ipcMain.removeAllListeners('fs:closeProject')
    ipcMain.removeAllListeners('fs:getProjectStructure')
    ipcMain.removeAllListeners('fs:refreshProject')
    ipcMain.removeAllListeners('fs:analyzeProject')
    ipcMain.removeAllListeners('fs:detectProjectType')

    ipcMain.removeAllListeners('fs:readFile')
    ipcMain.removeAllListeners('fs:writeFile')
    ipcMain.removeAllListeners('fs:createFile')
    ipcMain.removeAllListeners('fs:deleteFile')
    ipcMain.removeAllListeners('fs:copyFile')
    ipcMain.removeAllListeners('fs:moveFile')

    ipcMain.removeAllListeners('fs:createDirectory')
    ipcMain.removeAllListeners('fs:deleteDirectory')
    ipcMain.removeAllListeners('fs:copyDirectory')
    ipcMain.removeAllListeners('fs:moveDirectory')

    ipcMain.removeAllListeners('fs:detectFileType')
    ipcMain.removeAllListeners('fs:getLanguageFromExtension')
    ipcMain.removeAllListeners('fs:isBinaryFile')

    ipcMain.removeAllListeners('fs:exists')
    ipcMain.removeAllListeners('fs:getStats')
    ipcMain.removeAllListeners('fs:searchFiles')
    ipcMain.removeAllListeners('fs:getProjectDependencies')

    ipcMain.removeAllListeners('fs:startWatching')
    ipcMain.removeAllListeners('fs:stopWatching')

    // Cleanup file system manager
    this.fileSystemManager.cleanup()
    this.mainWindow = null
  }

  // Get file system manager instance for sharing with other handlers
  getFileSystemManager(): FileSystemManager {
    return this.fileSystemManager
  }
}