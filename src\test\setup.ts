import '@testing-library/jest-dom'

// Mock electron API
const mockElectronAPI = {
  getVersion: jest.fn().mockResolvedValue('1.0.0'),
  getPlatform: jest.fn().mockResolvedValue('win32'),
  openProject: jest.fn(),
  readFile: jest.fn(),
  writeFile: jest.fn(),
  createFile: jest.fn(),
  createFolder: jest.fn(),
  deleteFile: jest.fn(),
  watchFiles: jest.fn(),
  sendAIMessage: jest.fn(),
  setAIModel: jest.fn(),
  getAIModels: jest.fn(),
  validateAPIKey: jest.fn(),
  createTerminal: jest.fn(),
  executeCommand: jest.fn(),
  killTerminal: jest.fn(),
  onTerminalOutput: jest.fn(),
  getSetting: jest.fn(),
  setSetting: jest.fn(),
  getAllSettings: jest.fn(),
  minimizeWindow: jest.fn(),
  maximizeWindow: jest.fn(),
  closeWindow: jest.fn(),
  isMaximized: jest.fn(),
  showOpenDialog: jest.fn(),
  showSaveDialog: jest.fn(),
  showMessageBox: jest.fn(),
  searchWeb: jest.fn(),
  scrapeWebsite: jest.fn(),
  createFigmaDesign: jest.fn(),
  analyzeFigmaDesign: jest.fn(),
}

// Mock window.electronAPI
Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
})

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Suppress console warnings in tests
const originalError = console.error
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return
    }
    originalError.call(console, ...args)
  }
})

afterAll(() => {
  console.error = originalError
})
