/**
 * Terminal Component
 * Integrated terminal panel
 */

import React, { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Terminal as TerminalIcon, X, Plus, Settings, Trash2 } from 'lucide-react'
import { Button } from '../ui'
import { cn } from '../../utils'
import { fadeVariants } from '../../design/animations'

interface TerminalSession {
  id: string
  name: string
  isActive: boolean
  output: string[]
  currentDirectory: string
}

const mockSessions: TerminalSession[] = [
  {
    id: '1',
    name: 'Terminal 1',
    isActive: true,
    output: [
      'AI Code Editor Terminal v1.0.0',
      'Type "help" for available commands',
      '',
      '~/Desktop/ideai $ npm run dev',
      'Starting development server...',
      'Server running on http://localhost:3000',
      '',
      '~/Desktop/ideai $ '
    ],
    currentDirectory: '~/Desktop/ideai'
  }
]

export function Terminal() {
  const [sessions, setSessions] = useState<TerminalSession[]>(mockSessions)
  const [activeSessionId, setActiveSessionId] = useState('1')
  const [input, setInput] = useState('')
  const terminalRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const activeSession = sessions.find(s => s.id === activeSessionId)

  useEffect(() => {
    // Auto-scroll to bottom when new output is added
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight
    }
  }, [activeSession?.output])

  useEffect(() => {
    // Focus input when terminal becomes active
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [activeSessionId])

  const handleCommand = (command: string) => {
    if (!activeSession) return

    const newOutput = [...activeSession.output]
    
    // Add command to output
    newOutput.push(`${activeSession.currentDirectory} $ ${command}`)

    // Process command
    switch (command.toLowerCase().trim()) {
      case 'help':
        newOutput.push(
          'Available commands:',
          '  help     - Show this help message',
          '  clear    - Clear terminal output',
          '  ls       - List directory contents',
          '  pwd      - Show current directory',
          '  echo     - Echo text',
          '  npm      - Node package manager',
          '  git      - Git version control',
          ''
        )
        break
      
      case 'clear':
        newOutput.length = 0
        break
      
      case 'ls':
        newOutput.push(
          'src/',
          'public/',
          'node_modules/',
          'package.json',
          'tsconfig.json',
          'README.md',
          ''
        )
        break
      
      case 'pwd':
        newOutput.push(activeSession.currentDirectory, '')
        break
      
      default:
        if (command.startsWith('echo ')) {
          newOutput.push(command.substring(5), '')
        } else if (command.trim()) {
          newOutput.push(`Command not found: ${command}`, '')
        }
        break
    }

    // Add new prompt
    newOutput.push(`${activeSession.currentDirectory} $ `)

    // Update session
    setSessions(sessions.map(session => 
      session.id === activeSessionId 
        ? { ...session, output: newOutput }
        : session
    ))

    setInput('')
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleCommand(input)
    }
  }

  const createNewSession = () => {
    const newSession: TerminalSession = {
      id: Date.now().toString(),
      name: `Terminal ${sessions.length + 1}`,
      isActive: false,
      output: [
        'AI Code Editor Terminal v1.0.0',
        'Type "help" for available commands',
        '',
        '~/Desktop/ideai $ '
      ],
      currentDirectory: '~/Desktop/ideai'
    }

    setSessions([...sessions, newSession])
    setActiveSessionId(newSession.id)
  }

  const closeSession = (sessionId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    const newSessions = sessions.filter(s => s.id !== sessionId)
    setSessions(newSessions)

    if (sessionId === activeSessionId && newSessions.length > 0) {
      setActiveSessionId(newSessions[0].id)
    }
  }

  const clearTerminal = () => {
    if (!activeSession) return

    setSessions(sessions.map(session => 
      session.id === activeSessionId 
        ? { ...session, output: [`${session.currentDirectory} $ `] }
        : session
    ))
  }

  return (
    <motion.div
      variants={fadeVariants}
      initial="hidden"
      animate="visible"
      className="flex flex-col h-full bg-terminal text-terminal-foreground"
    >
      {/* Terminal Header */}
      <div className="flex items-center justify-between bg-card border-b border-border px-3 py-2">
        {/* Session Tabs */}
        <div className="flex items-center space-x-1">
          {sessions.map((session) => (
            <button
              key={session.id}
              onClick={() => setActiveSessionId(session.id)}
              className={cn(
                'flex items-center space-x-2 px-2 py-1 rounded text-xs',
                'hover:bg-accent hover:text-accent-foreground transition-colors',
                'group',
                session.id === activeSessionId && 'bg-accent text-accent-foreground'
              )}
            >
              <TerminalIcon className="w-3 h-3" />
              <span>{session.name}</span>
              {sessions.length > 1 && (
                <button
                  onClick={(e) => closeSession(session.id, e)}
                  className="opacity-0 group-hover:opacity-100 p-0.5 rounded hover:bg-accent-foreground/10"
                >
                  <X className="w-2 h-2" />
                </button>
              )}
            </button>
          ))}
        </div>

        {/* Terminal Actions */}
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="icon-sm"
            onClick={createNewSession}
            title="New Terminal"
          >
            <Plus className="w-3 h-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon-sm"
            onClick={clearTerminal}
            title="Clear Terminal"
          >
            <Trash2 className="w-3 h-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon-sm"
            title="Terminal Settings"
          >
            <Settings className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {/* Terminal Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Output Area */}
        <div
          ref={terminalRef}
          className="flex-1 overflow-auto p-3 font-mono text-sm leading-relaxed"
        >
          {activeSession?.output.map((line, index) => (
            <div
              key={index}
              className={cn(
                'whitespace-pre-wrap',
                line.includes('$') && 'text-terminal-foreground font-semibold',
                line.includes('Error') && 'text-error',
                line.includes('Warning') && 'text-warning',
                line.includes('Success') && 'text-success'
              )}
            >
              {line}
            </div>
          ))}
        </div>

        {/* Input Area */}
        <div className="border-t border-border p-3">
          <div className="flex items-center space-x-2 font-mono text-sm">
            <span className="text-terminal-foreground font-semibold">
              {activeSession?.currentDirectory} $
            </span>
            <input
              ref={inputRef}
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              className="flex-1 bg-transparent border-none outline-none text-terminal-foreground"
              placeholder="Type a command..."
              autoComplete="off"
              spellCheck={false}
            />
          </div>
        </div>
      </div>
    </motion.div>
  )
}
