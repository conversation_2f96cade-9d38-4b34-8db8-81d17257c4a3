import { AIModel, AIResponse, ProjectContext, TokenUsage } from '../../types'
import { IAIServiceManager, IAIProvider, ITokenManager, AIServiceError } from './interfaces'

/**
 * Central AI Service Manager that coordinates multiple AI providers
 * Implements the facade pattern to provide a unified interface
 */
export class AIServiceManager implements IAIServiceManager {
  private providers: Map<string, IAIProvider> = new Map()
  private activeProvider: IAIProvider | null = null
  private tokenManager: ITokenManager | null = null

  constructor(tokenManager?: ITokenManager) {
    this.tokenManager = tokenManager || null
  }

  // Provider management
  registerProvider(provider: IAIProvider): void {
    this.providers.set(provider.name, provider)

    // Set as active if it's the first provider or if no active provider is set
    if (!this.activeProvider || this.providers.size === 1) {
      this.activeProvider = provider
    }
  }

  getProvider(name: string): IAIProvider | null {
    return this.providers.get(name) || null
  }

  getActiveProvider(): IAIProvider | null {
    return this.activeProvider
  }

  async setActiveProvider(name: string): Promise<void> {
    const provider = this.providers.get(name)
    if (!provider) {
      throw this.createError(
        'PROVIDER_NOT_FOUND',
        `Provider ${name} not found`,
        false
      )
    }

    if (!provider.isConfigured()) {
      throw this.createError(
        'PROVIDER_NOT_CONFIGURED',
        `Provider ${name} is not properly configured`,
        false
      )
    }

    this.activeProvider = provider
  }

  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys())
  }

  // Model management across providers
  async getAllAvailableModels(): Promise<AIModel[]> {
    const allModels: AIModel[] = []

    for (const provider of this.providers.values()) {
      try {
        const models = await provider.getAvailableModels()
        allModels.push(...models)
      } catch (error) {
        console.warn(`Failed to get models from provider ${provider.name}:`, error)
      }
    }

    return allModels
  }

  async setGlobalModel(modelId: string): Promise<void> {
    if (!this.activeProvider) {
      throw this.createError(
        'NO_ACTIVE_PROVIDER',
        'No active provider set',
        false
      )
    }

    await this.activeProvider.setModel(modelId)
  }

  getCurrentModel(): AIModel | null {
    return this.activeProvider?.getCurrentModel() || null
  }

  // Chat operations (delegates to active provider)
  async sendMessage(message: string, context?: ProjectContext): Promise<AIResponse> {
    this.validateActiveProvider()

    const startTime = Date.now()

    try {
      const response = await this.activeProvider!.sendMessage(message, context)

      // Record token usage if token manager is available
      if (this.tokenManager && response.tokensUsed) {
        await this.tokenManager.recordUsage(
          response.tokensUsed,
          response.model,
          await this.tokenManager.calculateCost(response.tokensUsed, response.model)
        )
      }

      return {
        ...response,
        processingTime: Date.now() - startTime
      }
    } catch (error) {
      throw this.wrapProviderError(error as Error)
    }
  }

  async streamMessage(
    message: string,
    onChunk: (chunk: string) => void,
    context?: ProjectContext
  ): Promise<AIResponse> {
    this.validateActiveProvider()

    const startTime = Date.now()

    try {
      const response = await this.activeProvider!.streamMessage(message, onChunk, context)

      // Record token usage if token manager is available
      if (this.tokenManager && response.tokensUsed) {
        await this.tokenManager.recordUsage(
          response.tokensUsed,
          response.model,
          await this.tokenManager.calculateCost(response.tokensUsed, response.model)
        )
      }

      return {
        ...response,
        processingTime: Date.now() - startTime
      }
    } catch (error) {
      throw this.wrapProviderError(error as Error)
    }
  }

  // Token management
  async countTokens(text: string): Promise<number> {
    this.validateActiveProvider()

    try {
      return await this.activeProvider!.countTokens(text)
    } catch (error) {
      // Fallback to estimation if counting fails
      return this.activeProvider!.estimateTokens(text)
    }
  }

  async getTokenUsage(): Promise<TokenUsage> {
    if (!this.tokenManager) {
      throw this.createError(
        'TOKEN_MANAGER_NOT_AVAILABLE',
        'Token manager is not configured',
        false
      )
    }

    return await this.tokenManager.getUsage()
  }

  async resetTokenUsage(): Promise<void> {
    if (!this.tokenManager) {
      throw this.createError(
        'TOKEN_MANAGER_NOT_AVAILABLE',
        'Token manager is not configured',
        false
      )
    }

    await this.tokenManager.resetUsage()
  }

  // Configuration
  isConfigured(): boolean {
    return !!(this.activeProvider && this.activeProvider.isConfigured())
  }

  async validateConfiguration(): Promise<boolean> {
    if (!this.activeProvider) {
      return false
    }

    try {
      return this.activeProvider.isConfigured()
    } catch (error) {
      return false
    }
  }

  // Utility methods
  private validateActiveProvider(): void {
    if (!this.activeProvider) {
      throw this.createError(
        'NO_ACTIVE_PROVIDER',
        'No active provider set',
        false
      )
    }

    if (!this.activeProvider.isConfigured()) {
      throw this.createError(
        'PROVIDER_NOT_CONFIGURED',
        'Active provider is not properly configured',
        false
      )
    }
  }

  private createError(
    code: string,
    message: string,
    retryable: boolean,
    details?: Record<string, any>
  ): AIServiceError {
    const error = new Error(message) as AIServiceError
    error.code = code
    error.provider = this.activeProvider?.name
    error.model = this.activeProvider?.getCurrentModel()?.id
    error.retryable = retryable
    error.details = details
    return error
  }

  private wrapProviderError(error: Error): AIServiceError {
    if ('code' in error && 'retryable' in error) {
      return error as AIServiceError
    }

    return this.createError(
      'PROVIDER_ERROR',
      `Provider error: ${error.message}`,
      true,
      { originalError: error.message }
    )
  }

  // Token manager setter for dependency injection
  setTokenManager(tokenManager: ITokenManager): void {
    this.tokenManager = tokenManager
  }

  // Get provider statistics
  getProviderStats(): Record<string, any> {
    const stats: Record<string, any> = {}

    for (const [name, provider] of this.providers) {
      stats[name] = {
        configured: provider.isConfigured(),
        currentModel: provider.getCurrentModel()?.id || null,
        supportedModels: provider.supportedModels.length,
        isActive: provider === this.activeProvider
      }
    }

    return stats
  }

  // Cleanup method
  async cleanup(): Promise<void> {
    this.providers.clear()
    this.activeProvider = null
    this.tokenManager = null
  }
}