import { AIModel, AIResponse, ProjectContext } from '../../../types'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, AIProviderConfig, AIServiceError } from '../interfaces'

/**
 * Abstract base class for all AI providers
 * Provides common functionality and enforces interface implementation
 */
export abstract class BaseProvider implements IAIProvider {
  protected config: AIProviderConfig
  protected currentModel: AIModel | null = null
  protected apiKey: string | null = null

  constructor(config: AIProviderConfig) {
    this.config = { ...config }
    this.apiKey = config.apiKey || null
  }

  // Abstract methods that must be implemented by subclasses
  abstract readonly name: string
  abstract readonly version: string
  abstract readonly supportedModels: AIModel[]

  abstract validateApiKey(apiKey: string): Promise<boolean>
  abstract getAvailableModels(): Promise<AIModel[]>
  abstract sendMessage(message: string, context?: ProjectContext): Promise<AIResponse>
  abstract streamMessage(
    message: string,
    onChunk: (chunk: string) => void,
    context?: ProjectContext
  ): Promise<AIResponse>
  abstract countTokens(text: string): Promise<number>

  // Common implementation methods
  async setApiKey(apiKey: string): Promise<void> {
    const isValid = await this.validateApiKey(apiKey)
    if (!isValid) {
      throw this.createError('INVALID_API_KEY', 'Provided API key is invalid', false)
    }
    this.apiKey = apiKey
    this.config.apiKey = apiKey
  }

  async setModel(modelId: string): Promise<void> {
    const availableModels = await this.getAvailableModels()
    const model = availableModels.find(m => m.id === modelId)

    if (!model) {
      throw this.createError(
        'MODEL_NOT_FOUND',
        `Model ${modelId} not found in available models`,
        false
      )
    }

    this.currentModel = model
    this.config.defaultModel = modelId
  }

  getCurrentModel(): AIModel | null {
    return this.currentModel
  }

  estimateTokens(text: string): number {
    // Simple estimation: ~4 characters per token for most models
    // This can be overridden by specific providers for more accuracy
    return Math.ceil(text.length / 4)
  }

  isConfigured(): boolean {
    return !!(this.apiKey && this.currentModel)
  }

  getConfiguration(): Record<string, any> {
    return {
      name: this.name,
      version: this.version,
      currentModel: this.currentModel?.id || null,
      hasApiKey: !!this.apiKey,
      baseUrl: this.config.baseUrl,
      timeout: this.config.timeout,
      maxRetries: this.config.maxRetries
    }
  }

  async updateConfiguration(config: Record<string, any>): Promise<void> {
    if (config.apiKey) {
      await this.setApiKey(config.apiKey)
    }

    if (config.model) {
      await this.setModel(config.model)
    }

    if (config.baseUrl) {
      this.config.baseUrl = config.baseUrl
    }

    if (config.timeout) {
      this.config.timeout = config.timeout
    }

    if (config.maxRetries) {
      this.config.maxRetries = config.maxRetries
    }
  }

  // Utility methods for subclasses
  protected createError(
    code: string,
    message: string,
    retryable: boolean,
    details?: Record<string, any>
  ): AIServiceError {
    const error = new Error(message) as AIServiceError
    error.code = code
    error.provider = this.name
    error.model = this.currentModel?.id
    error.retryable = retryable
    error.details = details
    return error
  }

  protected async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = this.config.maxRetries || 3
  ): Promise<T> {
    let lastError: Error

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error as Error

        // Don't retry if it's not a retryable error
        if (error instanceof Error && 'retryable' in error && !error.retryable) {
          throw error
        }

        // Don't retry on the last attempt
        if (attempt === maxRetries) {
          throw error
        }

        // Exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt), 10000)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    throw lastError!
  }

  protected validateRequiredConfig(): void {
    if (!this.apiKey) {
      throw this.createError('MISSING_API_KEY', 'API key is required', false)
    }

    if (!this.currentModel) {
      throw this.createError('MISSING_MODEL', 'Model must be selected', false)
    }
  }
}