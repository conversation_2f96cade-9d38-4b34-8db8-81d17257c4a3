/**
 * Main Layout Component
 * VS Code-like layout with resizable panels and responsive behavior
 */

import React, { useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { useLayoutStore } from '../../stores/layoutStore'
import { ResizablePanel } from './ResizablePanel'
import { TitleBar } from './TitleBar'
import { StatusBar } from './StatusBar'
import { Sidebar } from './Sidebar'
import { EditorArea } from './EditorArea'
import { Terminal } from './Terminal'
import { ChatPanel } from './ChatPanel'
import { SettingsPanel } from './SettingsPanel'
import { cn } from '../../utils'

interface MainLayoutProps {
  children?: React.ReactNode
}

export function MainLayout({ children }: MainLayoutProps) {
  const {
    panels,
    isFullscreen,
    showStatusBar,
    showTitleBar,
    compactMode,
    resizePanel,
    collapsePanel,
    expandPanel,
    adaptToScreenSize,
  } = useLayoutStore()

  // Handle window resize for responsive behavior
  const handleResize = useCallback(() => {
    adaptToScreenSize(window.innerWidth, window.innerHeight)
  }, [adaptToScreenSize])

  useEffect(() => {
    // Initial adaptation
    handleResize()

    // Listen for window resize
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [handleResize])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + B: Toggle sidebar
      if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
        e.preventDefault()
        const sidebar = panels.sidebar
        if (sidebar.isCollapsed) {
          expandPanel('sidebar')
        } else {
          collapsePanel('sidebar')
        }
      }
      // Ctrl/Cmd + `: Toggle terminal
      else if ((e.ctrlKey || e.metaKey) && e.key === '`') {
        e.preventDefault()
        const terminal = panels.terminal
        if (terminal.isCollapsed) {
          expandPanel('terminal')
        } else {
          collapsePanel('terminal')
        }
      }
      // Ctrl/Cmd + Shift + C: Toggle chat
      else if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'C') {
        e.preventDefault()
        const chat = panels.chat
        if (chat.isCollapsed) {
          expandPanel('chat')
        } else {
          collapsePanel('chat')
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [panels, collapsePanel, expandPanel])

  // Get panel component by type
  const getPanelContent = (panelId: string) => {
    const panel = panels[panelId]
    if (!panel) return null

    switch (panel.type) {
      case 'sidebar':
        return <Sidebar />
      case 'terminal':
        return <Terminal />
      case 'chat':
        return <ChatPanel />
      case 'settings':
        return <SettingsPanel />
      default:
        return null
    }
  }

  // Sort panels by position and order
  const leftPanels = Object.values(panels)
    .filter(p => p.position === 'left' && p.isVisible)
    .sort((a, b) => a.order - b.order)

  const rightPanels = Object.values(panels)
    .filter(p => p.position === 'right' && p.isVisible)
    .sort((a, b) => a.order - b.order)

  const topPanels = Object.values(panels)
    .filter(p => p.position === 'top' && p.isVisible)
    .sort((a, b) => a.order - b.order)

  const bottomPanels = Object.values(panels)
    .filter(p => p.position === 'bottom' && p.isVisible)
    .sort((a, b) => a.order - b.order)

  return (
    <div
      className={cn(
        'flex flex-col h-screen w-screen bg-background text-foreground overflow-hidden',
        isFullscreen && 'fixed inset-0 z-50',
        compactMode && 'compact-layout'
      )}
    >
      {/* Title Bar */}
      {showTitleBar && !isFullscreen && (
        <motion.div
          initial={{ y: -32 }}
          animate={{ y: 0 }}
          transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        >
          <TitleBar />
        </motion.div>
      )}

      {/* Top Panels */}
      {topPanels.map(panel => (
        <ResizablePanel
          key={panel.id}
          position={panel.position}
          size={panel.size}
          minSize={panel.minSize}
          maxSize={panel.maxSize}
          isVisible={panel.isVisible}
          isCollapsed={panel.isCollapsed}
          isResizable={panel.isResizable}
          onResize={(size) => resizePanel(panel.id, size)}
          onToggleCollapse={() => 
            panel.isCollapsed ? expandPanel(panel.id) : collapsePanel(panel.id)
          }
        >
          {getPanelContent(panel.id)}
        </ResizablePanel>
      ))}

      {/* Main Content Area */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left Panels */}
        {leftPanels.map(panel => (
          <ResizablePanel
            key={panel.id}
            position={panel.position}
            size={panel.size}
            minSize={panel.minSize}
            maxSize={panel.maxSize}
            isVisible={panel.isVisible}
            isCollapsed={panel.isCollapsed}
            isResizable={panel.isResizable}
            onResize={(size) => resizePanel(panel.id, size)}
            onToggleCollapse={() => 
              panel.isCollapsed ? expandPanel(panel.id) : collapsePanel(panel.id)
            }
          >
            {getPanelContent(panel.id)}
          </ResizablePanel>
        ))}

        {/* Center Editor Area */}
        <motion.div
          className="flex-1 flex flex-col overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1 }}
        >
          <EditorArea />
        </motion.div>

        {/* Right Panels */}
        {rightPanels.map(panel => (
          <ResizablePanel
            key={panel.id}
            position={panel.position}
            size={panel.size}
            minSize={panel.minSize}
            maxSize={panel.maxSize}
            isVisible={panel.isVisible}
            isCollapsed={panel.isCollapsed}
            isResizable={panel.isResizable}
            onResize={(size) => resizePanel(panel.id, size)}
            onToggleCollapse={() => 
              panel.isCollapsed ? expandPanel(panel.id) : collapsePanel(panel.id)
            }
          >
            {getPanelContent(panel.id)}
          </ResizablePanel>
        ))}
      </div>

      {/* Bottom Panels */}
      {bottomPanels.map(panel => (
        <ResizablePanel
          key={panel.id}
          position={panel.position}
          size={panel.size}
          minSize={panel.minSize}
          maxSize={panel.maxSize}
          isVisible={panel.isVisible}
          isCollapsed={panel.isCollapsed}
          isResizable={panel.isResizable}
          onResize={(size) => resizePanel(panel.id, size)}
          onToggleCollapse={() => 
            panel.isCollapsed ? expandPanel(panel.id) : collapsePanel(panel.id)
          }
        >
          {getPanelContent(panel.id)}
        </ResizablePanel>
      ))}

      {/* Status Bar */}
      {showStatusBar && !isFullscreen && (
        <motion.div
          initial={{ y: 32 }}
          animate={{ y: 0 }}
          transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        >
          <StatusBar />
        </motion.div>
      )}

      {/* Custom children (for modals, overlays, etc.) */}
      {children}
    </div>
  )
}
