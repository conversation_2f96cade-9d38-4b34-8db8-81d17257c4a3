# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
release/
out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Electron specific
app/dist/
app/main.prod.js
app/main.prod.js.map
app/renderer.prod.js
app/renderer.prod.js.map
src/main/main.js
src/main/main.js.map
src/preload/preload.js
src/preload/preload.js.map

# Package managers
package-lock.json
yarn.lock
pnpm-lock.yaml

# TypeScript
*.tsbuildinfo

# Temporary folders
tmp/
temp/

# Test results
test-results/
playwright-report/
test-results.xml

# Storybook build outputs
storybook-static

# Local development
.local
.cache/

# Figma plugin
manifest.json

# API keys and secrets (backup)
secrets/
keys/
*.key
*.pem
*.p12

# Database
*.db
*.sqlite
*.sqlite3

# Backup files
*.backup
*.bak
*.tmp

# Archive files
*.zip
*.tar.gz
*.rar
*.7z
