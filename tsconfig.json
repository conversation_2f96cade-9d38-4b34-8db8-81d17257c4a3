{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/services/*": ["src/services/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/hooks/*": ["src/hooks/*"], "@/stores/*": ["src/stores/*"], "@/assets/*": ["src/assets/*"]}, "types": ["node", "jest"]}, "include": ["src/**/*", "src/**/*.tsx", "src/**/*.ts"], "exclude": ["node_modules", "dist", "release"]}