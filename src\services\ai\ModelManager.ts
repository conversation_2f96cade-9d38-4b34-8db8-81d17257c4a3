import { AIModel } from '../../types'
import { IAIProvider, ISettingsManager } from './interfaces'

/**
 * Model Manager for handling AI model selection and configuration
 */
export class ModelManager {
  private providers: Map<string, IAIProvider> = new Map()
  private settingsManager: ISettingsManager | null = null
  private currentModel: AIModel | null = null
  private modelCache: Map<string, AIModel[]> = new Map()
  private cacheExpiry: Map<string, number> = new Map()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  constructor(settingsManager?: ISettingsManager) {
    this.settingsManager = settingsManager || null
  }

  // Provider registration
  registerProvider(provider: IAIProvider): void {
    this.providers.set(provider.name, provider)
    // Clear cache for this provider
    this.modelCache.delete(provider.name)
    this.cacheExpiry.delete(provider.name)
  }

  unregisterProvider(providerName: string): void {
    this.providers.delete(providerName)
    this.modelCache.delete(providerName)
    this.cacheExpiry.delete(providerName)
  }

  getRegisteredProviders(): string[] {
    return Array.from(this.providers.keys())
  }

  // Model discovery and caching
  async getAllAvailableModels(): Promise<AIModel[]> {
    const allModels: AIModel[] = []

    for (const [providerName, provider] of this.providers) {
      try {
        const models = await this.getModelsFromProvider(providerName)
        allModels.push(...models)
      } catch (error) {
        console.warn(`Failed to get models from provider ${providerName}:`, error)
      }
    }

    return this.sortModelsByRecommendation(allModels)
  }

  async getModelsFromProvider(providerName: string): Promise<AIModel[]> {
    const provider = this.providers.get(providerName)
    if (!provider) {
      throw new Error(`Provider ${providerName} not found`)
    }

    // Check cache first
    const cached = this.modelCache.get(providerName)
    const cacheTime = this.cacheExpiry.get(providerName)

    if (cached && cacheTime && Date.now() < cacheTime) {
      return cached
    }

    // Fetch fresh data
    try {
      const models = await provider.getAvailableModels()

      // Cache the results
      this.modelCache.set(providerName, models)
      this.cacheExpiry.set(providerName, Date.now() + this.CACHE_DURATION)

      return models
    } catch (error) {
      // If fetch fails but we have cached data, return it
      if (cached) {
        console.warn(`Using cached models for ${providerName} due to fetch error:`, error)
        return cached
      }
      throw error
    }
  }

  // Model selection and management
  async setCurrentModel(modelId: string): Promise<void> {
    const model = await this.findModelById(modelId)
    if (!model) {
      throw new Error(`Model ${modelId} not found`)
    }

    const provider = this.providers.get(model.provider)
    if (!provider) {
      throw new Error(`Provider ${model.provider} not found`)
    }

    if (!provider.isConfigured()) {
      throw new Error(`Provider ${model.provider} is not configured`)
    }

    // Set model on the provider
    await provider.setModel(modelId)
    this.currentModel = model

    // Save to settings if available
    if (this.settingsManager) {
      await this.settingsManager.setDefaultModel(modelId)
    }
  }

  getCurrentModel(): AIModel | null {
    return this.currentModel
  }

  async loadDefaultModel(): Promise<void> {
    if (!this.settingsManager) {
      return
    }

    try {
      const defaultModelId = await this.settingsManager.getDefaultModel()
      if (defaultModelId) {
        await this.setCurrentModel(defaultModelId)
      } else {
        // Set first available model as default
        await this.setFirstAvailableModel()
      }
    } catch (error) {
      console.warn('Failed to load default model:', error)
      await this.setFirstAvailableModel()
    }
  }

  private async setFirstAvailableModel(): Promise<void> {
    const models = await this.getAllAvailableModels()
    const availableModel = models.find(model => {
      const provider = this.providers.get(model.provider)
      return provider && provider.isConfigured()
    })

    if (availableModel) {
      await this.setCurrentModel(availableModel.id)
    }
  }

  // Model search and filtering
  async findModelById(modelId: string): Promise<AIModel | null> {
    const allModels = await this.getAllAvailableModels()
    return allModels.find(model => model.id === modelId) || null
  }

  async findModelsByProvider(providerName: string): Promise<AIModel[]> {
    return await this.getModelsFromProvider(providerName)
  }

  async findModelsByCapability(capability: string): Promise<AIModel[]> {
    const allModels = await this.getAllAvailableModels()

    // Filter based on capability (this could be extended with more sophisticated matching)
    return allModels.filter(model => {
      const description = model.description?.toLowerCase() || ''
      const name = model.name.toLowerCase()
      const cap = capability.toLowerCase()

      return description.includes(cap) || name.includes(cap)
    })
  }

  async getRecommendedModels(context?: {
    taskType?: 'chat' | 'code' | 'analysis' | 'creative'
    performance?: 'fast' | 'balanced' | 'quality'
    budget?: 'low' | 'medium' | 'high'
  }): Promise<AIModel[]> {
    const allModels = await this.getAllAvailableModels()

    if (!context) {
      return this.sortModelsByRecommendation(allModels)
    }

    let filtered = allModels

    // Filter by performance preference
    if (context.performance === 'fast') {
      filtered = filtered.filter(model =>
        model.name.toLowerCase().includes('flash') ||
        model.name.toLowerCase().includes('fast')
      )
    } else if (context.performance === 'quality') {
      filtered = filtered.filter(model =>
        model.name.toLowerCase().includes('pro') ||
        model.name.toLowerCase().includes('advanced')
      )
    }

    // Filter by budget
    if (context.budget === 'low') {
      filtered = filtered.filter(model => model.costPerToken <= 0.000001)
    } else if (context.budget === 'high') {
      filtered = filtered.filter(model => model.costPerToken >= 0.000002)
    }

    return this.sortModelsByRecommendation(filtered)
  }

  // Model comparison and statistics
  getModelComparison(modelIds: string[]): Promise<ModelComparison[]> {
    return Promise.all(
      modelIds.map(async (id) => {
        const model = await this.findModelById(id)
        if (!model) {
          throw new Error(`Model ${id} not found`)
        }

        return {
          model,
          provider: this.providers.get(model.provider),
          isConfigured: this.providers.get(model.provider)?.isConfigured() || false,
          estimatedCostPer1K: model.costPerToken * 1000,
          maxContextSize: model.maxTokens
        }
      })
    )
  }

  getModelStatistics(): ModelStatistics {
    const allProviders = Array.from(this.providers.values())
    const configuredProviders = allProviders.filter(p => p.isConfigured())

    return {
      totalProviders: allProviders.length,
      configuredProviders: configuredProviders.length,
      totalModels: Array.from(this.modelCache.values()).flat().length,
      currentModel: this.currentModel,
      cacheStatus: {
        cached: this.modelCache.size,
        expired: Array.from(this.cacheExpiry.entries()).filter(
          ([, expiry]) => Date.now() > expiry
        ).length
      }
    }
  }

  // Cache management
  clearCache(providerName?: string): void {
    if (providerName) {
      this.modelCache.delete(providerName)
      this.cacheExpiry.delete(providerName)
    } else {
      this.modelCache.clear()
      this.cacheExpiry.clear()
    }
  }

  async refreshModels(providerName?: string): Promise<void> {
    if (providerName) {
      this.clearCache(providerName)
      await this.getModelsFromProvider(providerName)
    } else {
      this.clearCache()
      await this.getAllAvailableModels()
    }
  }

  // Utility methods
  private sortModelsByRecommendation(models: AIModel[]): AIModel[] {
    return models.sort((a, b) => {
      // Prioritize by version (newer first)
      const versionCompare = b.version.localeCompare(a.version)
      if (versionCompare !== 0) return versionCompare

      // Then by performance (Pro > Flash)
      const aIsPro = a.name.toLowerCase().includes('pro')
      const bIsPro = b.name.toLowerCase().includes('pro')
      if (aIsPro && !bIsPro) return -1
      if (!aIsPro && bIsPro) return 1

      // Finally by cost (lower cost first for same tier)
      return a.costPerToken - b.costPerToken
    })
  }

  // Settings manager setter
  setSettingsManager(settingsManager: ISettingsManager): void {
    this.settingsManager = settingsManager
  }

  // Cleanup
  cleanup(): void {
    this.providers.clear()
    this.modelCache.clear()
    this.cacheExpiry.clear()
    this.currentModel = null
    this.settingsManager = null
  }
}

// Supporting interfaces
interface ModelComparison {
  model: AIModel
  provider: IAIProvider | undefined
  isConfigured: boolean
  estimatedCostPer1K: number
  maxContextSize: number
}

interface ModelStatistics {
  totalProviders: number
  configuredProviders: number
  totalModels: number
  currentModel: AIModel | null
  cacheStatus: {
    cached: number
    expired: number
  }
}