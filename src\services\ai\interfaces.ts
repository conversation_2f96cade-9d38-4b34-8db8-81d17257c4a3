import { AIModel, AIMessage, AIResponse, ProjectContext, TokenUsage, ProjectStructure, FileContent, FileEvent, ProjectType } from '../../types'

/**
 * Base interface for all AI providers
 */
export interface IAIProvider {
  readonly name: string
  readonly version: string
  readonly supportedModels: AIModel[]

  // Authentication
  validateApiKey(apiKey: string): Promise<boolean>
  setApiKey(apiKey: string): Promise<void>

  // Model management
  getAvailableModels(): Promise<AIModel[]>
  setModel(modelId: string): Promise<void>
  getCurrentModel(): AIModel | null

  // Chat operations
  sendMessage(message: string, context?: ProjectContext): Promise<AIResponse>
  streamMessage(
    message: string,
    onChunk: (chunk: string) => void,
    context?: ProjectContext
  ): Promise<AIResponse>

  // Token operations
  countTokens(text: string): Promise<number>
  estimateTokens(text: string): number

  // Provider specific operations
  isConfigured(): boolean
  getConfiguration(): Record<string, any>
  updateConfiguration(config: Record<string, any>): Promise<void>
}

/**
 * AI Service Manager interface for managing multiple providers
 */
export interface IAIServiceManager {
  // Provider management
  registerProvider(provider: IAIProvider): void
  getProvider(name: string): IAIProvider | null
  getActiveProvider(): IAIProvider | null
  setActiveProvider(name: string): Promise<void>
  getAvailableProviders(): string[]

  // Model management across providers
  getAllAvailableModels(): Promise<AIModel[]>
  setGlobalModel(modelId: string): Promise<void>
  getCurrentModel(): AIModel | null

  // Chat operations (delegates to active provider)
  sendMessage(message: string, context?: ProjectContext): Promise<AIResponse>
  streamMessage(
    message: string,
    onChunk: (chunk: string) => void,
    context?: ProjectContext
  ): Promise<AIResponse>

  // Token management
  countTokens(text: string): Promise<number>
  getTokenUsage(): Promise<TokenUsage>
  resetTokenUsage(): Promise<void>

  // Configuration
  isConfigured(): boolean
  validateConfiguration(): Promise<boolean>
}

/**
 * Chat service interface for message handling
 */
export interface IChatService {
  // Session management
  createSession(projectId?: string): Promise<string>
  getSession(sessionId: string): Promise<ChatSession | null>
  deleteSession(sessionId: string): Promise<void>
  getAllSessions(): Promise<ChatSession[]>

  // Message operations
  addMessage(sessionId: string, message: AIMessage): Promise<void>
  getMessages(sessionId: string): Promise<AIMessage[]>
  clearMessages(sessionId: string): Promise<void>

  // Context management
  updateSessionContext(sessionId: string, context: ProjectContext): Promise<void>
  getSessionContext(sessionId: string): Promise<ProjectContext | null>
}

/**
 * Token manager interface for usage tracking
 */
export interface ITokenManager {
  // Usage tracking
  recordUsage(tokens: number, model: string, cost?: number): Promise<void>
  getUsage(period?: 'daily' | 'monthly' | 'total'): Promise<TokenUsage>
  resetUsage(period?: 'daily' | 'monthly' | 'total'): Promise<void>

  // Limits and warnings
  setTokenLimit(limit: number, period: 'daily' | 'monthly'): Promise<void>
  getTokenLimit(period: 'daily' | 'monthly'): Promise<number>
  isLimitExceeded(period: 'daily' | 'monthly'): Promise<boolean>
  getRemainingTokens(period: 'daily' | 'monthly'): Promise<number>

  // Cost calculation
  calculateCost(tokens: number, model: string): Promise<number>
  getTotalCost(period?: 'daily' | 'monthly' | 'total'): Promise<number>
}

/**
 * Settings manager interface for configuration
 */
export interface ISettingsManager {
  // General settings
  get<T>(key: string): Promise<T | null>
  set<T>(key: string, value: T): Promise<void>
  delete(key: string): Promise<void>
  getAll(): Promise<Record<string, any>>
  clear(): Promise<void>

  // AI specific settings
  getApiKey(provider: string): Promise<string | null>
  setApiKey(provider: string, key: string): Promise<void>
  deleteApiKey(provider: string): Promise<void>

  // Model settings
  getDefaultModel(): Promise<string | null>
  setDefaultModel(modelId: string): Promise<void>

  // Token settings
  getTokenLimit(): Promise<number>
  setTokenLimit(limit: number): Promise<void>

  // Theme and UI settings
  getTheme(): Promise<'light' | 'dark' | 'auto'>
  setTheme(theme: 'light' | 'dark' | 'auto'): Promise<void>

  // Editor settings
  getEditorSettings(): Promise<EditorSettings>
  setEditorSettings(settings: Partial<EditorSettings>): Promise<void>
}

/**
 * Chat session interface
 */
export interface ChatSession {
  id: string
  projectId?: string
  title?: string
  messages: AIMessage[]
  context: ProjectContext
  createdAt: Date
  updatedAt: Date
  metadata?: Record<string, any>
}

/**
 * Editor settings interface
 */
export interface EditorSettings {
  fontSize: number
  fontFamily: string
  tabSize: number
  wordWrap: boolean
  lineNumbers: boolean
  minimap: boolean
  autoSave: boolean
  autoSaveDelay: number
  theme: string
}

/**
 * AI provider configuration interface
 */
export interface AIProviderConfig {
  name: string
  apiKey?: string
  baseUrl?: string
  timeout?: number
  maxRetries?: number
  defaultModel?: string
  customHeaders?: Record<string, string>
  rateLimits?: {
    requestsPerMinute?: number
    tokensPerMinute?: number
  }
}

/**
 * Error types for AI services
 */
export interface AIServiceError extends Error {
  code: string
  provider?: string
  model?: string
  retryable: boolean
  details?: Record<string, any>
}

/**
 * Request context for AI operations
 */
export interface AIRequestContext {
  sessionId?: string
  userId?: string
  projectId?: string
  timestamp: Date
  metadata?: Record<string, any>
}

/**
 * Response metadata for AI operations
 */
export interface AIResponseMetadata {
  model: string
  provider: string
  tokensUsed: number
  processingTime: number
  requestId?: string
  cost?: number
  confidence?: number
}

/**
 * File System Manager interface for file operations
 */
export interface IFileSystemManager {
  // Project operations
  openProject(path: string): Promise<ProjectStructure>
  closeProject(): Promise<void>
  getProjectStructure(): ProjectStructure | null
  refreshProject(): Promise<void>

  // File operations
  readFile(path: string): Promise<FileContent>
  writeFile(path: string, content: string): Promise<void>
  createFile(path: string, content?: string): Promise<void>
  deleteFile(path: string): Promise<void>
  copyFile(sourcePath: string, destinationPath: string): Promise<void>
  moveFile(sourcePath: string, destinationPath: string): Promise<void>

  // Directory operations
  createDirectory(path: string): Promise<void>
  deleteDirectory(path: string, recursive?: boolean): Promise<void>
  copyDirectory(sourcePath: string, destinationPath: string): Promise<void>
  moveDirectory(sourcePath: string, destinationPath: string): Promise<void>

  // File watching
  watchFiles(callback: (event: FileEvent) => void): () => void
  unwatchFiles(): void

  // File type detection
  detectFileType(path: string): Promise<FileTypeInfo>
  getLanguageFromExtension(extension: string): string
  isBinaryFile(path: string): Promise<boolean>

  // Utility methods
  exists(path: string): Promise<boolean>
  getStats(path: string): Promise<FileStats>
  searchFiles(pattern: string, options?: SearchOptions): Promise<string[]>

  // Project analysis
  analyzeProject(path: string): Promise<ProjectAnalysis>
  detectProjectType(path: string): Promise<ProjectType>
  getProjectDependencies(path: string): Promise<Dependency[]>
}

/**
 * File type information interface
 */
export interface FileTypeInfo {
  extension: string
  mimeType: string
  language: string
  category: 'code' | 'config' | 'data' | 'image' | 'document' | 'binary' | 'unknown'
  isBinary: boolean
  isText: boolean
  encoding?: string
}

/**
 * File statistics interface
 */
export interface FileStats {
  size: number
  created: Date
  modified: Date
  accessed: Date
  isFile: boolean
  isDirectory: boolean
  permissions: string
}

/**
 * Search options interface
 */
export interface SearchOptions {
  recursive?: boolean
  includeHidden?: boolean
  excludePatterns?: string[]
  maxResults?: number
  caseSensitive?: boolean
}

/**
 * Project analysis interface
 */
export interface ProjectAnalysis {
  type: ProjectType
  structure: ProjectStructure
  dependencies: Dependency[]
  frameworks: Framework[]
  packageManager?: PackageManager
  buildSystem?: BuildSystem
  configFiles: string[]
  entryPoints: string[]
  testFiles: string[]
  documentationFiles: string[]
}

/**
 * Package manager interface
 */
export interface PackageManager {
  name: 'npm' | 'yarn' | 'pnpm' | 'pip' | 'composer' | 'cargo' | 'go' | 'maven' | 'gradle'
  version?: string
  configFile: string
  lockFile?: string
  installCommand: string
  runCommand: string
}

/**
 * Build system interface
 */
export interface BuildSystem {
  name: 'webpack' | 'vite' | 'rollup' | 'parcel' | 'esbuild' | 'tsc' | 'babel' | 'gradle' | 'maven'
  configFile: string
  buildCommand: string
  devCommand?: string
  outputDir: string
}

/**
 * Framework interface
 */
export interface Framework {
  name: string
  version?: string
  type: 'frontend' | 'backend' | 'fullstack' | 'mobile' | 'desktop' | 'testing' | 'build'
  configFiles: string[]
}

/**
 * Dependency interface
 */
export interface Dependency {
  name: string
  version: string
  type: 'production' | 'development' | 'peer' | 'optional'
  source: 'npm' | 'pip' | 'composer' | 'cargo' | 'go' | 'maven' | 'gradle' | 'other'
}