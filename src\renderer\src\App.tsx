import React, { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { Loader2, Code2, Spark<PERSON> } from 'lucide-react'
import { ThemeProvider } from '../../components/ui'
import { MainLayout } from '../../components/layout'

// Type declaration for electron API
declare global {
  interface Window {
    electronAPI: {
      getVersion: () => Promise<string>
      getPlatform: () => Promise<string>
    }
  }
}

function App() {
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Simulate loading time for smooth UX
        await new Promise(resolve => setTimeout(resolve, 1000))
      } catch (error) {
        console.error('Failed to initialize app:', error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeApp()
  }, [])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center w-full h-screen bg-background">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex flex-col items-center space-y-4"
        >
          <div className="relative">
            <Code2 className="w-12 h-12 text-primary" />
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="absolute -top-1 -right-1"
            >
              <Sparkles className="w-6 h-6 text-accent" />
            </motion.div>
          </div>
          <div className="flex items-center space-x-2">
            <Loader2 className="w-4 h-4 animate-spin text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              AI Code Editor yükleniyor...
            </span>
          </div>
        </motion.div>
      </div>
    )
  }

  return (
    <ThemeProvider defaultTheme="auto">
      <MainLayout />
    </ThemeProvider>
  )
}

export default App
