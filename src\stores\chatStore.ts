import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { AIMessage, ProjectContext } from '../types'
import { ChatSession } from '../services/ai/interfaces'

/**
 * Chat Store State Interface
 */
interface ChatState {
  // Session management
  sessions: ChatSession[]
  currentSessionId: string | null
  isLoadingSessions: boolean
  sessionError: string | null

  // Current session state
  currentMessages: AIMessage[]
  isLoadingMessages: boolean
  messageError: string | null

  // Chat input state
  inputMessage: string
  isTyping: boolean
  isSending: boolean

  // Context state
  currentContext: ProjectContext | null

  // UI state
  isChatPanelOpen: boolean
  selectedMessageId: string | null

  // Actions - Session management
  setSessions: (sessions: ChatSession[]) => void
  addSession: (session: ChatSession) => void
  updateSession: (sessionId: string, updates: Partial<ChatSession>) => void
  removeSession: (sessionId: string) => void
  setCurrentSessionId: (sessionId: string | null) => void
  setLoadingSessions: (loading: boolean) => void
  setSessionError: (error: string | null) => void

  // Actions - Message management
  setCurrentMessages: (messages: AIMessage[]) => void
  addMessage: (message: AIMessage) => void
  updateMessage: (messageId: string, updates: Partial<AIMessage>) => void
  removeMessage: (messageId: string) => void
  setLoadingMessages: (loading: boolean) => void
  setMessageError: (error: string | null) => void

  // Actions - Input management
  setInputMessage: (message: string) => void
  setTyping: (typing: boolean) => void
  setSending: (sending: boolean) => void
  clearInput: () => void

  // Actions - Context management
  setCurrentContext: (context: ProjectContext | null) => void
  updateContext: (updates: Partial<ProjectContext>) => void

  // Actions - UI management
  setChatPanelOpen: (open: boolean) => void
  setSelectedMessageId: (messageId: string | null) => void

  // Computed getters
  getCurrentSession: () => ChatSession | null
  getSessionById: (sessionId: string) => ChatSession | null
  getRecentSessions: (limit?: number) => ChatSession[]
  getSessionsByProject: (projectId: string) => ChatSession[]

  // Utility actions
  resetState: () => void
  resetErrors: () => void
}

/**
 * Initial state
 */
const initialState = {
  sessions: [],
  currentSessionId: null,
  isLoadingSessions: false,
  sessionError: null,

  currentMessages: [],
  isLoadingMessages: false,
  messageError: null,

  inputMessage: '',
  isTyping: false,
  isSending: false,

  currentContext: null,

  isChatPanelOpen: true,
  selectedMessageId: null,
}

/**
 * Chat Store using Zustand
 */
export const useChatStore = create<ChatState>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    // Session management actions
    setSessions: (sessions) => set({ sessions }),

    addSession: (session) => set((state) => ({
      sessions: [session, ...state.sessions]
    })),

    updateSession: (sessionId, updates) => set((state) => ({
      sessions: state.sessions.map(session =>
        session.id === sessionId ? { ...session, ...updates } : session
      )
    })),

    removeSession: (sessionId) => set((state) => ({
      sessions: state.sessions.filter(session => session.id !== sessionId),
      currentSessionId: state.currentSessionId === sessionId ? null : state.currentSessionId,
      currentMessages: state.currentSessionId === sessionId ? [] : state.currentMessages
    })),

    setCurrentSessionId: (sessionId) => set({ currentSessionId: sessionId }),
    setLoadingSessions: (loading) => set({ isLoadingSessions: loading }),
    setSessionError: (error) => set({ sessionError: error }),

    // Message management actions
    setCurrentMessages: (messages) => set({ currentMessages: messages }),

    addMessage: (message) => set((state) => ({
      currentMessages: [...state.currentMessages, message]
    })),

    updateMessage: (messageId, updates) => set((state) => ({
      currentMessages: state.currentMessages.map(msg =>
        msg.id === messageId ? { ...msg, ...updates } : msg
      )
    })),

    removeMessage: (messageId) => set((state) => ({
      currentMessages: state.currentMessages.filter(msg => msg.id !== messageId)
    })),

    setLoadingMessages: (loading) => set({ isLoadingMessages: loading }),
    setMessageError: (error) => set({ messageError: error }),

    // Input management actions
    setInputMessage: (message) => set({ inputMessage: message }),
    setTyping: (typing) => set({ isTyping: typing }),
    setSending: (sending) => set({ isSending: sending }),
    clearInput: () => set({ inputMessage: '' }),

    // Context management actions
    setCurrentContext: (context) => set({ currentContext: context }),
    updateContext: (updates) => set((state) => ({
      currentContext: state.currentContext
        ? { ...state.currentContext, ...updates }
        : updates as ProjectContext
    })),

    // UI management actions
    setChatPanelOpen: (open) => set({ isChatPanelOpen: open }),
    setSelectedMessageId: (messageId) => set({ selectedMessageId: messageId }),

    // Computed getters
    getCurrentSession: () => {
      const { sessions, currentSessionId } = get()
      return sessions.find(session => session.id === currentSessionId) || null
    },

    getSessionById: (sessionId) => {
      const { sessions } = get()
      return sessions.find(session => session.id === sessionId) || null
    },

    getRecentSessions: (limit = 10) => {
      const { sessions } = get()
      return sessions
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
        .slice(0, limit)
    },

    getSessionsByProject: (projectId) => {
      const { sessions } = get()
      return sessions.filter(session => session.projectId === projectId)
    },

    // Utility actions
    resetState: () => set(initialState),
    resetErrors: () => set({
      sessionError: null,
      messageError: null
    })
  }))
)

// Store selectors for better performance
export const selectSessions = (state: ChatState) => state.sessions
export const selectCurrentSessionId = (state: ChatState) => state.currentSessionId
export const selectCurrentMessages = (state: ChatState) => state.currentMessages
export const selectInputMessage = (state: ChatState) => state.inputMessage
export const selectIsSending = (state: ChatState) => state.isSending
export const selectIsTyping = (state: ChatState) => state.isTyping
export const selectCurrentContext = (state: ChatState) => state.currentContext
export const selectIsChatPanelOpen = (state: ChatState) => state.isChatPanelOpen

// Helper hooks for common operations
export const useSessions = () => useChatStore(selectSessions)
export const useCurrentSessionId = () => useChatStore(selectCurrentSessionId)
export const useCurrentMessages = () => useChatStore(selectCurrentMessages)
export const useInputMessage = () => useChatStore(selectInputMessage)
export const useIsSending = () => useChatStore(selectIsSending)
export const useIsTyping = () => useChatStore(selectIsTyping)
export const useCurrentContext = () => useChatStore(selectCurrentContext)
export const useIsChatPanelOpen = () => useChatStore(selectIsChatPanelOpen)

// Computed selectors
export const useCurrentSession = () => useChatStore(state => state.getCurrentSession())
export const useRecentSessions = (limit?: number) =>
  useChatStore(state => state.getRecentSessions(limit))
export const useSessionsByProject = (projectId: string) =>
  useChatStore(state => state.getSessionsByProject(projectId))

// Error selectors
export const useChatErrors = () => useChatStore(state => ({
  sessionError: state.sessionError,
  messageError: state.messageError
}))

// Loading state selectors
export const useChatLoadingStates = () => useChatStore(state => ({
  isLoadingSessions: state.isLoadingSessions,
  isLoadingMessages: state.isLoadingMessages,
  isSending: state.isSending
}))

// Store actions for external use
export const chatStoreActions = {
  setSessions: (sessions: ChatSession[]) => useChatStore.getState().setSessions(sessions),
  addSession: (session: ChatSession) => useChatStore.getState().addSession(session),
  setCurrentSessionId: (sessionId: string | null) => useChatStore.getState().setCurrentSessionId(sessionId),
  addMessage: (message: AIMessage) => useChatStore.getState().addMessage(message),
  setInputMessage: (message: string) => useChatStore.getState().setInputMessage(message),
  setSending: (sending: boolean) => useChatStore.getState().setSending(sending),
  setCurrentContext: (context: ProjectContext | null) => useChatStore.getState().setCurrentContext(context),
  setChatPanelOpen: (open: boolean) => useChatStore.getState().setChatPanelOpen(open),
  resetErrors: () => useChatStore.getState().resetErrors(),
  resetState: () => useChatStore.getState().resetState()
}