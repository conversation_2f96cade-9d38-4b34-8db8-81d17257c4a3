/**
 * Tree Animations Hook
 * Provides smooth animations for tree view interactions
 */

import { useCallback, useRef, useMemo } from 'react'
import { useSpring, useTransition, config } from '@react-spring/web'

interface TreeAnimationOptions {
  expandDuration?: number
  collapseDuration?: number
  staggerDelay?: number
  enableStagger?: boolean
}

export const useTreeAnimations = (options: TreeAnimationOptions = {}) => {
  const {
    expandDuration = 300,
    collapseDuration = 200,
    staggerDelay = 50,
    enableStagger = true
  } = options

  const expandingNodes = useRef<Set<string>>(new Set())

  // Memoize animation configurations to prevent recreation
  const animationConfigs = useMemo(() => ({
    expandDuration,
    collapseDuration,
    staggerDelay,
    enableStagger
  }), [expandDuration, collapseDuration, staggerDelay, enableStagger])

  // Folder expand/collapse animation
  const getFolderAnimation = useCallback((isExpanded: boolean, isAnimating: boolean = false) => {
    const animationConfig = {
      duration: isExpanded ? animationConfigs.expandDuration : animationConfigs.collapseDuration,
      easing: isExpanded ? 'ease-out' : 'ease-in'
    }

    return useSpring({
      height: isExpanded ? 'auto' : 0,
      opacity: isExpanded ? 1 : 0,
      transform: isExpanded ? 'translateY(0px)' : 'translateY(-10px)',
      config: animationConfig,
      immediate: !isAnimating
    })
  }, [animationConfigs])

  // Chevron rotation animation
  const getChevronAnimation = useCallback((isExpanded: boolean) => {
    return useSpring({
      transform: `rotate(${isExpanded ? 90 : 0}deg)`,
      config: config.gentle
    })
  }, [])

  // File/folder item entrance animation
  const getItemEntranceAnimation = useCallback((index: number, delay: number = 0) => {
    const calculatedDelay = animationConfigs.enableStagger ? index * animationConfigs.staggerDelay + delay : delay

    return useSpring({
      from: {
        opacity: 0,
        transform: 'translateX(-20px) scale(0.95)'
      },
      to: {
        opacity: 1,
        transform: 'translateX(0px) scale(1)'
      },
      delay: calculatedDelay,
      config: config.gentle
    })
  }, [animationConfigs])

  // Hover animation
  const getHoverAnimation = useCallback((isHovered: boolean) => {
    return useSpring({
      backgroundColor: isHovered ? 'rgba(var(--accent), 0.1)' : 'transparent',
      transform: isHovered ? 'translateX(2px)' : 'translateX(0px)',
      config: config.wobbly
    })
  }, [])

  // Selection animation
  const getSelectionAnimation = useCallback((isSelected: boolean) => {
    return useSpring({
      backgroundColor: isSelected ? 'rgba(var(--accent), 0.2)' : 'transparent',
      borderLeft: isSelected ? '3px solid var(--primary)' : '3px solid transparent',
      config: config.gentle
    })
  }, [])

  // Loading animation for async operations
  const getLoadingAnimation = useCallback((isLoading: boolean) => {
    return useSpring({
      opacity: isLoading ? 0.6 : 1,
      transform: isLoading ? 'scale(0.98)' : 'scale(1)',
      config: config.gentle
    })
  }, [])

  // Drag and drop animations
  const getDragAnimation = useCallback((isDragging: boolean, isDragOver: boolean) => {
    return useSpring({
      transform: isDragging 
        ? 'scale(1.05) rotate(2deg)' 
        : isDragOver 
          ? 'scale(1.02)' 
          : 'scale(1) rotate(0deg)',
      opacity: isDragging ? 0.8 : 1,
      backgroundColor: isDragOver ? 'rgba(var(--primary), 0.1)' : 'transparent',
      config: config.wobbly
    })
  }, [])

  // Transition for adding/removing items
  const getListTransition = useCallback((items: any[]) => {
    const trailDelay = animationConfigs.enableStagger ? animationConfigs.staggerDelay : 0

    return useTransition(items, {
      from: { opacity: 0, height: 0, transform: 'translateX(-20px)' },
      enter: { opacity: 1, height: 28, transform: 'translateX(0px)' },
      leave: { opacity: 0, height: 0, transform: 'translateX(-20px)' },
      config: config.gentle,
      trail: trailDelay
    })
  }, [animationConfigs])

  // Search highlight animation
  const getSearchHighlightAnimation = useCallback((isHighlighted: boolean) => {
    return useSpring({
      backgroundColor: isHighlighted ? 'rgba(var(--warning), 0.3)' : 'transparent',
      config: config.gentle
    })
  }, [])

  // Error state animation
  const getErrorAnimation = useCallback((hasError: boolean) => {
    return useSpring({
      backgroundColor: hasError ? 'rgba(var(--destructive), 0.1)' : 'transparent',
      borderColor: hasError ? 'var(--destructive)' : 'transparent',
      config: config.gentle
    })
  }, [])

  // Utility functions
  const markNodeAsExpanding = useCallback((nodePath: string) => {
    expandingNodes.current.add(nodePath)
  }, [])

  const unmarkNodeAsExpanding = useCallback((nodePath: string) => {
    expandingNodes.current.delete(nodePath)
  }, [])

  const isNodeExpanding = useCallback((nodePath: string) => {
    return expandingNodes.current.has(nodePath)
  }, [])

  // Preset animation combinations
  const getTreeItemAnimations = useCallback((
    index: number,
    isExpanded: boolean,
    isSelected: boolean,
    isHovered: boolean,
    isLoading: boolean = false,
    hasError: boolean = false
  ) => {
    return {
      entrance: getItemEntranceAnimation(index),
      folder: getFolderAnimation(isExpanded),
      chevron: getChevronAnimation(isExpanded),
      hover: getHoverAnimation(isHovered),
      selection: getSelectionAnimation(isSelected),
      loading: getLoadingAnimation(isLoading),
      error: getErrorAnimation(hasError)
    }
  }, [
    getItemEntranceAnimation,
    getFolderAnimation,
    getChevronAnimation,
    getHoverAnimation,
    getSelectionAnimation,
    getLoadingAnimation,
    getErrorAnimation
  ])

  return {
    // Individual animations
    getFolderAnimation,
    getChevronAnimation,
    getItemEntranceAnimation,
    getHoverAnimation,
    getSelectionAnimation,
    getLoadingAnimation,
    getDragAnimation,
    getListTransition,
    getSearchHighlightAnimation,
    getErrorAnimation,
    
    // Utility functions
    markNodeAsExpanding,
    unmarkNodeAsExpanding,
    isNodeExpanding,
    
    // Preset combinations
    getTreeItemAnimations
  }
}
