/**
 * Keyboard Shortcuts Hook
 * Handles global keyboard shortcuts for file operations
 */

import { useEffect, useCallback, useRef, useState } from 'react'
import { FileNode } from '../types'

export interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  shiftKey?: boolean
  altKey?: boolean
  metaKey?: boolean
  action: () => void
  description: string
  category: string
  disabled?: boolean
}

export interface UseKeyboardShortcutsOptions {
  selectedNodes: FileNode[]
  focusedNode: FileNode | null
  onCopy?: (nodes: FileNode[]) => void
  onCut?: (nodes: FileNode[]) => void
  onPaste?: (targetPath: string) => void
  onDelete?: (nodes: FileNode[]) => void
  onRename?: (node: FileNode) => void
  onDuplicate?: (node: FileNode) => void
  onNewFile?: (parentPath: string) => void
  onNewFolder?: (parentPath: string) => void
  onRefresh?: () => void
  onSelectAll?: () => void
  onFind?: () => void
  onToggleSearch?: () => void
  onNavigateUp?: () => void
  onNavigateDown?: () => void
  onExpandAll?: () => void
  onCollapseAll?: () => void
  disabled?: boolean
}

export const useKeyboardShortcuts = (options: UseKeyboardShortcutsOptions) => {
  const {
    selectedNodes,
    focusedNode,
    onCopy,
    onCut,
    onPaste,
    onDelete,
    onRename,
    onDuplicate,
    onNewFile,
    onNewFolder,
    onRefresh,
    onSelectAll,
    onFind,
    onToggleSearch,
    onNavigateUp,
    onNavigateDown,
    onExpandAll,
    onCollapseAll,
    disabled = false
  } = options

  const shortcutsRef = useRef<KeyboardShortcut[]>([])

  // Define all keyboard shortcuts
  const shortcuts: KeyboardShortcut[] = [
    // File Operations
    {
      key: 'c',
      ctrlKey: true,
      action: () => onCopy?.(selectedNodes),
      description: 'Copy selected items',
      category: 'File Operations',
      disabled: !onCopy || selectedNodes.length === 0
    },
    {
      key: 'x',
      ctrlKey: true,
      action: () => onCut?.(selectedNodes),
      description: 'Cut selected items',
      category: 'File Operations',
      disabled: !onCut || selectedNodes.length === 0
    },
    {
      key: 'v',
      ctrlKey: true,
      action: () => {
        if (focusedNode?.type === 'folder') {
          onPaste?.(focusedNode.path)
        } else if (focusedNode) {
          // Paste to parent directory
          const parentPath = focusedNode.path.substring(0, focusedNode.path.lastIndexOf('/'))
          onPaste?.(parentPath)
        }
      },
      description: 'Paste items',
      category: 'File Operations',
      disabled: !onPaste
    },
    {
      key: 'Delete',
      action: () => onDelete?.(selectedNodes),
      description: 'Delete selected items',
      category: 'File Operations',
      disabled: !onDelete || selectedNodes.length === 0
    },
    {
      key: 'F2',
      action: () => {
        if (selectedNodes.length === 1) {
          onRename?.(selectedNodes[0])
        } else if (focusedNode) {
          onRename?.(focusedNode)
        }
      },
      description: 'Rename selected item',
      category: 'File Operations',
      disabled: !onRename || (selectedNodes.length === 0 && !focusedNode)
    },
    {
      key: 'd',
      ctrlKey: true,
      action: () => {
        if (selectedNodes.length === 1) {
          onDuplicate?.(selectedNodes[0])
        } else if (focusedNode) {
          onDuplicate?.(focusedNode)
        }
      },
      description: 'Duplicate selected item',
      category: 'File Operations',
      disabled: !onDuplicate || (selectedNodes.length === 0 && !focusedNode)
    },

    // Create Operations
    {
      key: 'n',
      ctrlKey: true,
      action: () => {
        const targetPath = focusedNode?.type === 'folder' 
          ? focusedNode.path 
          : focusedNode?.path.substring(0, focusedNode.path.lastIndexOf('/')) || ''
        onNewFile?.(targetPath)
      },
      description: 'New file',
      category: 'Create',
      disabled: !onNewFile
    },
    {
      key: 'n',
      ctrlKey: true,
      shiftKey: true,
      action: () => {
        const targetPath = focusedNode?.type === 'folder' 
          ? focusedNode.path 
          : focusedNode?.path.substring(0, focusedNode.path.lastIndexOf('/')) || ''
        onNewFolder?.(targetPath)
      },
      description: 'New folder',
      category: 'Create',
      disabled: !onNewFolder
    },

    // Navigation
    {
      key: 'ArrowUp',
      action: () => onNavigateUp?.(),
      description: 'Navigate up',
      category: 'Navigation',
      disabled: !onNavigateUp
    },
    {
      key: 'ArrowDown',
      action: () => onNavigateDown?.(),
      description: 'Navigate down',
      category: 'Navigation',
      disabled: !onNavigateDown
    },

    // Selection
    {
      key: 'a',
      ctrlKey: true,
      action: () => onSelectAll?.(),
      description: 'Select all',
      category: 'Selection',
      disabled: !onSelectAll
    },

    // Search
    {
      key: 'f',
      ctrlKey: true,
      action: () => onFind?.(),
      description: 'Find in files',
      category: 'Search',
      disabled: !onFind
    },
    {
      key: 'f',
      ctrlKey: true,
      shiftKey: true,
      action: () => onToggleSearch?.(),
      description: 'Toggle search panel',
      category: 'Search',
      disabled: !onToggleSearch
    },

    // View
    {
      key: 'F5',
      action: () => onRefresh?.(),
      description: 'Refresh',
      category: 'View',
      disabled: !onRefresh
    },
    {
      key: 'r',
      ctrlKey: true,
      action: () => onRefresh?.(),
      description: 'Refresh',
      category: 'View',
      disabled: !onRefresh
    },
    {
      key: 'e',
      ctrlKey: true,
      shiftKey: true,
      action: () => onExpandAll?.(),
      description: 'Expand all folders',
      category: 'View',
      disabled: !onExpandAll
    },
    {
      key: 'w',
      ctrlKey: true,
      shiftKey: true,
      action: () => onCollapseAll?.(),
      description: 'Collapse all folders',
      category: 'View',
      disabled: !onCollapseAll
    }
  ]

  shortcutsRef.current = shortcuts

  // Handle keyboard events
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (disabled) return

    // Skip if focus is in an input field
    if (
      event.target instanceof HTMLInputElement ||
      event.target instanceof HTMLTextAreaElement ||
      event.target instanceof HTMLSelectElement ||
      (event.target as HTMLElement)?.contentEditable === 'true'
    ) {
      return
    }

    // Find matching shortcut
    const matchingShortcut = shortcuts.find(shortcut => {
      if (shortcut.disabled) return false

      return (
        shortcut.key === event.key &&
        !!shortcut.ctrlKey === event.ctrlKey &&
        !!shortcut.shiftKey === event.shiftKey &&
        !!shortcut.altKey === event.altKey &&
        !!shortcut.metaKey === event.metaKey
      )
    })

    if (matchingShortcut) {
      event.preventDefault()
      event.stopPropagation()
      matchingShortcut.action()
    }
  }, [shortcuts, disabled])

  // Set up event listeners
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown])

  // Get shortcuts by category
  const getShortcutsByCategory = useCallback(() => {
    const categories: Record<string, KeyboardShortcut[]> = {}
    
    shortcuts.forEach(shortcut => {
      if (!categories[shortcut.category]) {
        categories[shortcut.category] = []
      }
      categories[shortcut.category].push(shortcut)
    })

    return categories
  }, [shortcuts])

  // Get shortcut display string
  const getShortcutString = useCallback((shortcut: KeyboardShortcut) => {
    const parts: string[] = []
    
    if (shortcut.ctrlKey) parts.push('Ctrl')
    if (shortcut.shiftKey) parts.push('Shift')
    if (shortcut.altKey) parts.push('Alt')
    if (shortcut.metaKey) parts.push('Cmd')
    
    parts.push(shortcut.key === ' ' ? 'Space' : shortcut.key)
    
    return parts.join('+')
  }, [])

  // Check if shortcut is available
  const isShortcutAvailable = useCallback((shortcut: KeyboardShortcut) => {
    return !shortcut.disabled && !disabled
  }, [disabled])

  // Get all available shortcuts
  const getAvailableShortcuts = useCallback(() => {
    return shortcuts.filter(shortcut => isShortcutAvailable(shortcut))
  }, [shortcuts, isShortcutAvailable])

  return {
    shortcuts,
    getShortcutsByCategory,
    getShortcutString,
    isShortcutAvailable,
    getAvailableShortcuts,
    
    // Utility functions
    formatShortcut: (key: string, modifiers: { ctrl?: boolean; shift?: boolean; alt?: boolean; meta?: boolean } = {}) => {
      const parts: string[] = []
      if (modifiers.ctrl) parts.push('Ctrl')
      if (modifiers.shift) parts.push('Shift')
      if (modifiers.alt) parts.push('Alt')
      if (modifiers.meta) parts.push('Cmd')
      parts.push(key)
      return parts.join('+')
    }
  }
}

// Predefined shortcut configurations
export const DEFAULT_FILE_SHORTCUTS = {
  copy: { key: 'c', ctrlKey: true },
  cut: { key: 'x', ctrlKey: true },
  paste: { key: 'v', ctrlKey: true },
  delete: { key: 'Delete' },
  rename: { key: 'F2' },
  duplicate: { key: 'd', ctrlKey: true },
  newFile: { key: 'n', ctrlKey: true },
  newFolder: { key: 'n', ctrlKey: true, shiftKey: true },
  refresh: { key: 'F5' },
  selectAll: { key: 'a', ctrlKey: true },
  find: { key: 'f', ctrlKey: true },
  expandAll: { key: 'e', ctrlKey: true, shiftKey: true },
  collapseAll: { key: 'w', ctrlKey: true, shiftKey: true }
} as const

// Hook for displaying keyboard shortcuts help
export const useShortcutHelp = () => {
  const [isVisible, setIsVisible] = useState(false)

  const showHelp = useCallback(() => setIsVisible(true), [])
  const hideHelp = useCallback(() => setIsVisible(false), [])
  const toggleHelp = useCallback(() => setIsVisible(prev => !prev), [])

  // Show help with F1 or Ctrl+?
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (
        event.key === 'F1' ||
        (event.ctrlKey && event.key === '?') ||
        (event.ctrlKey && event.shiftKey && event.key === '/')
      ) {
        event.preventDefault()
        toggleHelp()
      }
      
      if (event.key === 'Escape' && isVisible) {
        hideHelp()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isVisible, toggleHelp, hideHelp])

  return {
    isVisible,
    showHelp,
    hideHelp,
    toggleHelp
  }
}
