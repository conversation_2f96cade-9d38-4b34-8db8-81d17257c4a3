/**
 * Sidebar Component
 * File explorer and project navigation
 */

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Folder,
  FolderOpen,
  File,
  ChevronRight,
  ChevronDown,
  Search,
  Plus,
  MoreHorizontal,
  RefreshCw,
  FolderPlus,
  AlertCircle,
  Keyboard,
  Settings,
  X
} from 'lucide-react'
import { Button, Input } from '../ui'
import { VirtualizedTree } from '../ui/VirtualizedTree'
import { ContextMenu, useContextMenu, createFileContextMenuItems, createFolderContextMenuItems } from '../ui/ContextMenu'
import { DropIndicator, DragPreview, DragDropFeedback } from '../ui/DropIndicator'
import { AdvancedSearchPanel } from '../ui/AdvancedSearchPanel'
import { useFileSystemStore } from '../../stores/fileSystemStore'
import { useTreeKeyboard } from '../../hooks/useTreeKeyboard'
import { useTreeAnimations } from '../../hooks/useTreeAnimations'
import { useClipboard, useSystemClipboard, createFileOperationHandlers } from '../../hooks/useClipboard'
import { useKeyboardShortcuts } from '../../hooks/useKeyboardShortcuts'
import { useDragAndDrop } from '../../hooks/useDragAndDrop'
import { useAdvancedSearch } from '../../hooks/useAdvancedSearch'
import { FileNode } from '../../types'
import { getFileTypeInfo, getFileIcon, getFileColor, isFileEditable, getFileCategory, FileCategory } from '../../utils/fileTypeDetection'
import { cn } from '../../utils'
import { staggerVariants, staggerItemVariants } from '../../design/animations'

// No more mock data - using real file system!

export function Sidebar() {
  const {
    fileTree,
    expandedFolders,
    selectedFile,
    searchQuery,
    searchResults,
    isLoading,
    error,
    isProjectOpen,
    currentProject,
    toggleFolder,
    selectFile,
    setSearchQuery,
    searchFiles,
    clearSearch,
    createFile,
    createFolder,
    refreshProject,
    openProject,
    copyFile,
    moveFile,
    deleteFile,
    renameFile,
    readFile,
  } = useFileSystemStore()

  const [showSearch, setShowSearch] = useState(false)
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false)
  const [useVirtualization, setUseVirtualization] = useState(true)
  const [showKeyboardHelp, setShowKeyboardHelp] = useState(false)
  const [fileTypeFilter, setFileTypeFilter] = useState<string>('all')
  const [selectedNodes, setSelectedNodes] = useState<FileNode[]>([])
  const [isRenaming, setIsRenaming] = useState<string | null>(null)
  const [renameValue, setRenameValue] = useState('')
  const containerRef = useRef<HTMLDivElement>(null)
  const treeHeight = 400 // Will be calculated dynamically

  // Initialize tree animations
  const animations = useTreeAnimations({
    expandDuration: 250,
    collapseDuration: 200,
    staggerDelay: 30,
    enableStagger: true
  })

  // Define handleFileClick after animations to avoid circular dependency
  const handleFileClick = useCallback((item: FileNode) => {
    if (item.type === 'file') {
      selectFile(item.path)
      // TODO: Open file in editor (will be implemented in next task)
      console.log('Opening file:', item.path)
    } else {
      animations.markNodeAsExpanding(item.path)
      toggleFolder(item.path)
      setTimeout(() => animations.unmarkNodeAsExpanding(item.path), 300)
    }
  }, [selectFile, toggleFolder, animations])

  // Initialize clipboard operations
  const clipboard = useClipboard({
    onPaste: async (items, targetPath) => {
      for (const item of items) {
        if (item.operation === 'copy') {
          await copyFile(item.node.path, `${targetPath}/${item.node.name}`)
        } else if (item.operation === 'cut') {
          await moveFile(item.node.path, `${targetPath}/${item.node.name}`)
        }
      }
      await refreshProject()
    }
  })

  const systemClipboard = useSystemClipboard()
  const fileOperations = createFileOperationHandlers(
    { copyFile, moveFile, deleteFile, refreshProject },
    clipboard,
    systemClipboard
  )

  // Initialize context menu
  const contextMenu = useContextMenu()

  // Initialize drag and drop
  const dragAndDrop = useDragAndDrop({
    onMove: async (sourceNode, targetNode, position) => {
      try {
        let targetPath: string

        if (position === 'inside') {
          // Move into folder
          targetPath = `${targetNode.path}/${sourceNode.name}`
        } else {
          // Move to same level as target
          const targetParent = targetNode.path.substring(0, targetNode.path.lastIndexOf('/'))
          targetPath = `${targetParent}/${sourceNode.name}`
        }

        await moveFile(sourceNode.path, targetPath)
        await refreshProject()
      } catch (error) {
        console.error('Failed to move file:', error)
        throw error
      }
    },
    onReorder: async (sourceNode, targetIndex, targetParent) => {
      // For now, reordering is handled by the file system
      // In a more advanced implementation, we could maintain custom order
      console.log('Reorder:', sourceNode.name, 'to index', targetIndex, 'in', targetParent)
    },
    canDrop: (sourceNode, targetNode, position) => {
      // Can't drop on itself
      if (sourceNode.path === targetNode.path) return false

      // Can't drop a parent into its child
      if (targetNode.path.startsWith(sourceNode.path + '/')) return false

      // Can only drop inside folders
      if (position === 'inside' && targetNode.type !== 'folder') return false

      return true
    },
    disabled: isRenaming !== null
  })

  // Initialize advanced search
  const advancedSearch = useAdvancedSearch({
    nodes: fileTree,
    onSearchContent: async (filePath: string, query: string) => {
      // In a real implementation, this would read file content and search
      // For now, we'll return empty matches
      try {
        const content = await readFile(filePath)
        const lines = content.content.split('\n')
        const matches = []

        lines.forEach((line, lineNumber) => {
          const index = line.toLowerCase().indexOf(query.toLowerCase())
          if (index !== -1) {
            matches.push({
              type: 'content' as const,
              text: query,
              startIndex: index,
              endIndex: index + query.length,
              lineNumber: lineNumber + 1,
              context: line.trim()
            })
          }
        })

        return matches
      } catch (error) {
        console.warn('Failed to search content in', filePath, error)
        return []
      }
    },
    debounceMs: 300,
    maxHistoryItems: 50
  })

  // Initialize keyboard navigation
  const keyboard = useTreeKeyboard({
    nodes: searchQuery.trim() ? searchResults : fileTree,
    expandedFolders,
    onNodeSelect: selectFile,
    onToggleExpand: toggleFolder,
    onNodeActivate: handleFileClick,
    enableTypeAhead: true,
    typeAheadDelay: 1000
  })

  // Initialize keyboard shortcuts
  const keyboardShortcuts = useKeyboardShortcuts({
    selectedNodes,
    focusedNode: keyboard.focusedNode,
    onCopy: (nodes) => fileOperations.handleCopy(nodes),
    onCut: (nodes) => fileOperations.handleCut(nodes),
    onPaste: (targetPath) => fileOperations.handlePaste(targetPath),
    onDelete: handleDeleteNodes,
    onRename: handleRenameNode,
    onNewFile: handleNewFile,
    onNewFolder: handleNewFolder,
    onRefresh: handleRefresh,
    disabled: isRenaming !== null
  })

  // Handle search input changes
  useEffect(() => {
    if (searchQuery.trim()) {
      const debounceTimer = setTimeout(() => {
        searchFiles(searchQuery)
      }, 300)
      return () => clearTimeout(debounceTimer)
    } else {
      clearSearch()
    }
  }, [searchQuery, searchFiles, clearSearch])

  // Calculate tree height dynamically
  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect()
        const availableHeight = window.innerHeight - rect.top - 100 // Leave some margin
        // setTreeHeight(Math.max(200, availableHeight))
      }
    }

    updateHeight()
    window.addEventListener('resize', updateHeight)
    return () => window.removeEventListener('resize', updateHeight)
  }, [])

  const handleNewFile = useCallback(async (parentPath?: string) => {
    const targetPath = parentPath || currentProject?.root
    if (!targetPath) return

    try {
      const fileName = prompt('Enter file name:')
      if (fileName) {
        const filePath = `${targetPath}/${fileName}`
        await createFile(filePath)
      }
    } catch (error) {
      console.error('Failed to create file:', error)
    }
  }, [currentProject, createFile])

  const handleNewFolder = useCallback(async (parentPath?: string) => {
    const targetPath = parentPath || currentProject?.root
    if (!targetPath) return

    try {
      const folderName = prompt('Enter folder name:')
      if (folderName) {
        const folderPath = `${targetPath}/${folderName}`
        await createFolder(folderPath)
      }
    } catch (error) {
      console.error('Failed to create folder:', error)
    }
  }, [currentProject, createFolder])

  // File operation handlers
  const handleDeleteNodes = useCallback(async (nodes: FileNode[]) => {
    if (!window.confirm(`Are you sure you want to delete ${nodes.length} item${nodes.length !== 1 ? 's' : ''}?`)) {
      return
    }

    try {
      for (const node of nodes) {
        if (node.type === 'file') {
          await deleteFile(node.path)
        } else {
          await deleteFolder(node.path)
        }
      }
      setSelectedNodes([])
    } catch (error) {
      console.error('Failed to delete items:', error)
    }
  }, [deleteFile, deleteFolder])

  const handleRenameNode = useCallback((node: FileNode) => {
    setIsRenaming(node.path)
    setRenameValue(node.name)
  }, [])

  const handleRenameSubmit = useCallback(async () => {
    if (!isRenaming || !renameValue.trim()) return

    try {
      const oldPath = isRenaming
      const newPath = oldPath.substring(0, oldPath.lastIndexOf('/') + 1) + renameValue.trim()

      await renameFile(oldPath, newPath)
      setIsRenaming(null)
      setRenameValue('')
    } catch (error) {
      console.error('Failed to rename:', error)
    }
  }, [isRenaming, renameValue, renameFile])

  const handleRenameCancel = useCallback(() => {
    setIsRenaming(null)
    setRenameValue('')
  }, [])

  // Context menu handlers
  const handleContextMenu = useCallback((event: React.MouseEvent, node: FileNode) => {
    event.preventDefault()
    event.stopPropagation()

    const isFile = node.type === 'file'
    const items = isFile
      ? createFileContextMenuItems(node.name, node.path, {
          onOpen: () => handleFileClick(node),
          onRename: () => handleRenameNode(node),
          onDelete: () => handleDeleteNodes([node]),
          onCopy: () => fileOperations.handleCopy([node]),
          onCut: () => fileOperations.handleCut([node]),
          onCopyPath: () => fileOperations.handleCopyPath(node.path),
          onDuplicate: () => handleDuplicateNode(node)
        })
      : createFolderContextMenuItems(node.name, node.path, {
          onNewFile: () => handleNewFile(node.path),
          onNewFolder: () => handleNewFolder(node.path),
          onRename: () => handleRenameNode(node),
          onDelete: () => handleDeleteNodes([node]),
          onCopy: () => fileOperations.handleCopy([node]),
          onCut: () => fileOperations.handleCut([node]),
          onPaste: () => fileOperations.handlePaste(node.path),
          onRefresh: () => handleRefresh(),
          onCopyPath: () => fileOperations.handleCopyPath(node.path)
        })

    contextMenu.showContextMenu(event, items, node)
  }, [handleFileClick, handleRenameNode, handleDeleteNodes, fileOperations, handleNewFile, handleNewFolder, handleRefresh, contextMenu])

  const handleDuplicateNode = useCallback(async (node: FileNode) => {
    try {
      const extension = node.name.includes('.') ? `.${node.name.split('.').pop()}` : ''
      const baseName = node.name.replace(extension, '')
      const newName = `${baseName}_copy${extension}`
      const newPath = node.path.substring(0, node.path.lastIndexOf('/') + 1) + newName

      await copyFile(node.path, newPath)
    } catch (error) {
      console.error('Failed to duplicate:', error)
    }
  }, [copyFile])

  // Selection handlers
  const handleNodeClick = useCallback((node: FileNode, event?: React.MouseEvent) => {
    if (event?.ctrlKey) {
      // Multi-select
      setSelectedNodes(prev => {
        const isSelected = prev.some(n => n.path === node.path)
        if (isSelected) {
          return prev.filter(n => n.path !== node.path)
        } else {
          return [...prev, node]
        }
      })
    } else if (event?.shiftKey && selectedNodes.length > 0) {
      // Range select (simplified)
      setSelectedNodes([...selectedNodes, node])
    } else {
      // Single select
      setSelectedNodes([node])
      handleFileClick(node)
    }
  }, [selectedNodes, handleFileClick])

  const handleRefresh = async () => {
    try {
      await refreshProject()
    } catch (error) {
      console.error('Failed to refresh project:', error)
    }
  }

  const handleOpenProject = async () => {
    try {
      const projectPath = await window.electronAPI.openFolderDialog()
      if (projectPath) {
        await openProject(projectPath)
      }
    } catch (error) {
      console.error('Failed to open project:', error)
    }
  }

  const getEnhancedFileIcon = useCallback((item: FileNode) => {
    if (item.type === 'folder') {
      const isExpanded = expandedFolders.has(item.path)
      const IconComponent = getFileIcon(item.name, true, isExpanded)
      const color = getFileColor(item.name, true)

      return (
        <IconComponent
          className="w-4 h-4"
          style={{ color }}
        />
      )
    }

    // Get file type info for advanced icon mapping
    const fileTypeInfo = getFileTypeInfo(item.name)
    const IconComponent = fileTypeInfo.icon
    const color = fileTypeInfo.color
    const isEditable = fileTypeInfo.canEdit

    return (
      <div className="relative">
        <IconComponent
          className={cn(
            "w-4 h-4",
            !isEditable && "opacity-60"
          )}
          style={{ color }}
        />
        {/* Binary file indicator */}
        {fileTypeInfo.isBinary && (
          <div className="absolute -top-1 -right-1 w-2 h-2 bg-warning rounded-full"
               title="Binary file" />
        )}
        {/* Read-only indicator */}
        {!isEditable && (
          <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-muted-foreground rounded-full"
               title="Read-only" />
        )}
      </div>
    )
  }, [expandedFolders])

  // Enhanced file tree rendering with virtualization option
  const renderEnhancedTree = useCallback(() => {
    const displayTree = searchQuery.trim() ? searchResults : fileTree

    if (useVirtualization && displayTree.length > 50) {
      return (
        <VirtualizedTree
          nodes={displayTree}
          expandedFolders={expandedFolders}
          selectedFile={selectedFile}
          onNodeClick={handleFileClick}
          onToggleExpand={toggleFolder}
          renderIcon={getEnhancedFileIcon}
          height={treeHeight}
          itemHeight={28}
          className="focus:outline-none"
          // Drag and drop props
          onDragStart={dragAndDrop.handleDragStart}
          onDragOver={dragAndDrop.handleDragOver}
          onDrop={dragAndDrop.handleDrop}
          onDragEnd={dragAndDrop.handleDragEnd}
          onDragLeave={dragAndDrop.handleDragLeave}
          isDragging={dragAndDrop.isDragging}
          dragItem={dragAndDrop.dragItem}
          getDropIndicator={dragAndDrop.getDropIndicator}
        />
      )
    }

    // Fallback to regular tree for smaller lists or when virtualization is disabled
    return renderRegularTree(displayTree)
  }, [
    searchQuery, searchResults, fileTree, useVirtualization,
    expandedFolders, selectedFile, handleFileClick, toggleFolder,
    treeHeight
  ])

  const renderRegularTree = (items: FileNode[], depth = 0, parentPath: string | null = null) => {
    return items.map((item, index) => {
      const isExpanded = expandedFolders.has(item.path)
      const isSelected = selectedFile === item.path
      const isFocused = keyboard.focusedNode?.path === item.path
      const isAnimating = animations.isNodeExpanding(item.path)
      const isDragging = dragAndDrop.isNodeDragging(item.path)
      const dropIndicator = dragAndDrop.getDropIndicator(item.path)

      return (
        <motion.div
          key={item.path}
          variants={staggerItemVariants}
          className="select-none"
          initial={{ opacity: 0, x: -10 }}
          animate={{
            opacity: isDragging ? 0.5 : 1,
            x: 0,
            scale: isDragging ? 0.95 : 1
          }}
          transition={{ duration: 0.2, delay: index * 0.02 }}
        >
          <DropIndicator
            position={dropIndicator?.position || null}
            isValid={dropIndicator?.isValid || false}
          >
          <div
            className={cn(
              'flex items-center space-x-2 py-1 px-2 rounded cursor-pointer',
              'hover:bg-accent hover:text-accent-foreground transition-all duration-200',
              isSelected && 'bg-accent text-accent-foreground',
              isFocused && 'ring-2 ring-primary ring-opacity-50',
              isDragging && 'opacity-50 scale-95',
              'group relative'
            )}
            style={{ paddingLeft: `${depth * 12 + 8}px` }}
            draggable={!isRenaming}
            onDragStart={(e) => dragAndDrop.handleDragStart(e, item, index, parentPath)}
            onDragOver={(e) => dragAndDrop.handleDragOver(e, item, index)}
            onDrop={(e) => dragAndDrop.handleDrop(e, item, index)}
            onDragEnd={dragAndDrop.handleDragEnd}
            onDragLeave={dragAndDrop.handleDragLeave}
            onClick={(e) => handleNodeClick(item, e)}
            onContextMenu={(e) => handleContextMenu(e, item)}
            tabIndex={isFocused ? 0 : -1}
            role="treeitem"
            aria-expanded={item.type === 'folder' ? isExpanded : undefined}
            aria-selected={isSelected}
          >
            {/* Expand/Collapse Icon */}
            {item.type === 'folder' && item.children && item.children.length > 0 && (
              <motion.button
                className="p-0.5 rounded hover:bg-accent-foreground/10 flex-shrink-0"
                onClick={(e) => {
                  e.stopPropagation()
                  animations.markNodeAsExpanding(item.path)
                  toggleFolder(item.path)
                  setTimeout(() => animations.unmarkNodeAsExpanding(item.path), 300)
                }}
                animate={{ rotate: isExpanded ? 90 : 0 }}
                transition={{ duration: 0.2 }}
                aria-label={isExpanded ? 'Collapse folder' : 'Expand folder'}
              >
                <ChevronRight className="w-3 h-3" />
              </motion.button>
            )}

            {/* Spacer for files without children */}
            {!(item.type === 'folder' && item.children && item.children.length > 0) && (
              <div className="w-4 flex-shrink-0" />
            )}

            {/* File/Folder Icon */}
            <div className="flex-shrink-0">
              {getEnhancedFileIcon(item)}
            </div>

            {/* Name with file type info or rename input */}
            {isRenaming === item.path ? (
              <input
                type="text"
                value={renameValue}
                onChange={(e) => setRenameValue(e.target.value)}
                onBlur={handleRenameCancel}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    handleRenameSubmit()
                  } else if (e.key === 'Escape') {
                    e.preventDefault()
                    handleRenameCancel()
                  }
                }}
                className="flex-1 text-sm bg-background border border-input rounded px-1 py-0.5"
                autoFocus
                onFocus={(e) => e.target.select()}
              />
            ) : (
              <span
                className="flex-1 text-sm truncate select-none"
                title={item.type === 'file' ? getFileTypeInfo(item.name).description : undefined}
              >
                {item.name}
              </span>
            )}

            {/* File Size (for files) */}
            {item.type === 'file' && item.size && (
              <span className="text-xs text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity">
                {formatFileSize(item.size)}
              </span>
            )}

            {/* Loading indicator */}
            {isAnimating && (
              <div className="w-3 h-3 flex-shrink-0">
                <RefreshCw className="w-3 h-3 animate-spin" />
              </div>
            )}

            {/* Actions */}
            <div className="opacity-0 group-hover:opacity-100 transition-opacity">
              <button
                className="p-0.5 rounded hover:bg-accent-foreground/10"
                onClick={(e) => {
                  e.stopPropagation()
                  // TODO: Show context menu
                  console.log('More actions for:', item.name)
                }}
                aria-label="More actions"
              >
                <MoreHorizontal className="w-3 h-3" />
              </button>
            </div>
          </div>

          {/* Children with enhanced animations */}
          <AnimatePresence>
            {item.type === 'folder' && isExpanded && item.children && (
              <motion.div
                initial={{ height: 0, opacity: 0, y: -10 }}
                animate={{ height: 'auto', opacity: 1, y: 0 }}
                exit={{ height: 0, opacity: 0, y: -10 }}
                transition={{
                  duration: 0.25,
                  ease: 'easeOut'
                }}
                className="overflow-hidden"
              >
                {renderRegularTree(item.children, depth + 1, item.path)}
              </motion.div>
            )}
          </AnimatePresence>
          </DropIndicator>
        </motion.div>
      )
    })
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  // Filter files by type
  const filterFilesByType = useCallback((nodes: FileNode[]): FileNode[] => {
    if (fileTypeFilter === 'all') return nodes

    return nodes.map(node => {
      if (node.type === 'folder' && node.children) {
        return {
          ...node,
          children: filterFilesByType(node.children)
        }
      } else if (node.type === 'file') {
        const category = getFileCategory(node.name)
        return fileTypeFilter === category ? node : null
      }
      return node
    }).filter(Boolean) as FileNode[]
  }, [fileTypeFilter])

  // Use search results if searching, otherwise use file tree with filters
  const baseTree = searchQuery.trim() ? searchResults : fileTree
  const displayTree = filterFilesByType(baseTree)

  // Keyboard shortcuts help component
  const KeyboardHelp = () => (
    <AnimatePresence>
      {showKeyboardHelp && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="absolute top-full left-0 right-0 z-50 bg-popover border border-border rounded-md shadow-lg p-4 m-2"
        >
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-semibold">Keyboard Shortcuts</h3>
              <Button
                variant="ghost"
                size="icon-sm"
                onClick={() => setShowKeyboardHelp(false)}
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span>↑/↓</span>
                  <span>Navigate</span>
                </div>
                <div className="flex justify-between">
                  <span>→</span>
                  <span>Expand</span>
                </div>
                <div className="flex justify-between">
                  <span>←</span>
                  <span>Collapse</span>
                </div>
                <div className="flex justify-between">
                  <span>Enter</span>
                  <span>Open</span>
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span>Home</span>
                  <span>First</span>
                </div>
                <div className="flex justify-between">
                  <span>End</span>
                  <span>Last</span>
                </div>
                <div className="flex justify-between">
                  <span>*</span>
                  <span>Expand All</span>
                </div>
                <div className="flex justify-between">
                  <span>Type</span>
                  <span>Search</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )

  return (
    <div className="flex flex-col h-full bg-sidebar text-sidebar-foreground">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-sidebar-border">
        <h2 className="text-sm font-semibold">
          {currentProject ? currentProject.name : 'Explorer'}
        </h2>
        <div className="flex items-center space-x-1">
          {!isProjectOpen && (
            <Button
              variant="ghost"
              size="icon-sm"
              onClick={handleOpenProject}
              title="Open Project"
            >
              <FolderOpen className="w-3 h-3" />
            </Button>
          )}
          {isProjectOpen && (
            <>
              <Button
                variant="ghost"
                size="icon-sm"
                onClick={handleNewFile}
                title="New File"
                disabled={isLoading}
              >
                <Plus className="w-3 h-3" />
              </Button>
              <Button
                variant="ghost"
                size="icon-sm"
                onClick={handleNewFolder}
                title="New Folder"
                disabled={isLoading}
              >
                <FolderPlus className="w-3 h-3" />
              </Button>
              <Button
                variant="ghost"
                size="icon-sm"
                onClick={handleRefresh}
                title="Refresh"
                disabled={isLoading}
              >
                <RefreshCw className={cn("w-3 h-3", isLoading && "animate-spin")} />
              </Button>
              <Button
                variant="ghost"
                size="icon-sm"
                onClick={() => setShowSearch(!showSearch)}
                title="Quick Search"
              >
                <Search className="w-3 h-3" />
              </Button>
              <Button
                variant="ghost"
                size="icon-sm"
                onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
                title="Advanced Search"
                className={cn(showAdvancedSearch && 'bg-accent')}
              >
                <Search className="w-3 h-3" />
                <Settings className="w-2 h-2 absolute -top-0.5 -right-0.5" />
              </Button>
              <Button
                variant="ghost"
                size="icon-sm"
                onClick={() => setShowKeyboardHelp(!showKeyboardHelp)}
                title="Keyboard Shortcuts"
              >
                <Keyboard className="w-3 h-3" />
              </Button>
              <Button
                variant="ghost"
                size="icon-sm"
                onClick={() => setUseVirtualization(!useVirtualization)}
                title={useVirtualization ? 'Disable Virtualization' : 'Enable Virtualization'}
              >
                <Settings className="w-3 h-3" />
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Search */}
      {showSearch && isProjectOpen && (
        <div className="p-3 border-b border-sidebar-border">
          <Input
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<Search className="w-4 h-4" />}
            size="sm"
            autoFocus
          />
          {searchQuery && (
            <div className="text-xs text-muted-foreground mt-1">
              {searchResults.length} result{searchResults.length !== 1 ? 's' : ''} found
            </div>
          )}
        </div>
      )}

      {/* File Type Filter */}
      {isProjectOpen && (
        <div className="p-3 border-b border-sidebar-border">
          <div className="flex items-center space-x-2">
            <label className="text-xs font-medium text-muted-foreground">Filter:</label>
            <select
              value={fileTypeFilter}
              onChange={(e) => setFileTypeFilter(e.target.value)}
              className="flex-1 text-xs bg-background border border-input rounded px-2 py-1"
            >
              <option value="all">All Files</option>
              <option value="code">Code Files</option>
              <option value="markup">Markup</option>
              <option value="data">Data Files</option>
              <option value="config">Config</option>
              <option value="image">Images</option>
              <option value="document">Documents</option>
              <option value="archive">Archives</option>
            </select>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="p-3 border-b border-sidebar-border">
          <div className="flex items-center space-x-2 text-error text-sm">
            <AlertCircle className="w-4 h-4" />
            <span>{error}</span>
          </div>
        </div>
      )}

      {/* Advanced Search Panel */}
      {showAdvancedSearch && (
        <div className="flex-1 overflow-hidden">
          <AdvancedSearchPanel
            searchHook={advancedSearch}
            onResultClick={(filePath) => {
              selectFile(filePath)
              setShowAdvancedSearch(false)
            }}
          />
        </div>
      )}

      {/* File Tree */}
      {!showAdvancedSearch && (
        <div className="flex-1 overflow-hidden relative" ref={containerRef}>
        <KeyboardHelp />

        <motion.div
          className="h-full"
          variants={staggerVariants}
          initial="hidden"
          animate="visible"
          ref={keyboard.containerRef}
          tabIndex={0}
          role="tree"
          aria-label="File Explorer"
        >
          {isLoading ? (
            <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
              <RefreshCw className="w-8 h-8 mb-2 animate-spin" />
              <p className="text-sm">Loading project...</p>
            </div>
          ) : !isProjectOpen ? (
            <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
              <FolderOpen className="w-8 h-8 mb-2" />
              <p className="text-sm">No project opened</p>
              <p className="text-xs">Click the folder icon to open a project</p>
            </div>
          ) : displayTree.length > 0 ? (
            <div className="p-2">
              {renderEnhancedTree()}
            </div>
          ) : searchQuery ? (
            <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
              <Search className="w-8 h-8 mb-2" />
              <p className="text-sm">No files found</p>
              <p className="text-xs">Try a different search term</p>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
              <Folder className="w-8 h-8 mb-2" />
              <p className="text-sm">Empty project</p>
              <p className="text-xs">Create files to get started</p>
            </div>
          )}
        </motion.div>

        {/* Status Bar */}
        {isProjectOpen && (
          <div className="absolute bottom-0 left-0 right-0 bg-card border-t border-border px-2 py-1">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>
                {displayTree.length} item{displayTree.length !== 1 ? 's' : ''}
              </span>
              <div className="flex items-center space-x-2">
                {keyboard.typeAheadQuery && (
                  <span className="bg-accent px-1 rounded">
                    Type: {keyboard.typeAheadQuery}
                  </span>
                )}
                {useVirtualization && displayTree.length > 50 && (
                  <span className="text-primary">Virtual</span>
                )}
              </div>
            </div>
          </div>
        )}
        </div>
      )}

      {/* Context Menu */}
      {contextMenu.contextMenu && (
        <ContextMenu {...contextMenu.contextMenu} />
      )}

      {/* Drag Preview */}
      {dragAndDrop.dragState.dragPreview.visible && dragAndDrop.dragItem && (
        <DragPreview
          node={dragAndDrop.dragItem.node}
          position={dragAndDrop.dragState.dragPreview}
          visible={dragAndDrop.dragState.dragPreview.visible}
        />
      )}

      {/* Drag Drop Feedback */}
      <DragDropFeedback
        isDragging={dragAndDrop.isDragging}
        dragItem={dragAndDrop.dragItem ? {
          node: dragAndDrop.dragItem.node,
          count: selectedNodes.length > 1 ? selectedNodes.length : undefined
        } : null}
        dropTarget={dragAndDrop.dropTarget}
      />
    </div>
  )
}
