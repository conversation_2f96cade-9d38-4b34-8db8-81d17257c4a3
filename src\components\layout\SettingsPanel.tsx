/**
 * Settings Panel Component
 * Application settings and preferences
 */

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Settings, 
  Palette, 
  Code, 
  Zap, 
  Shield, 
  Monitor,
  Save,
  RotateCcw
} from 'lucide-react'
import { Button, Input, Card, CardHeader, CardTitle, CardContent, Badge } from '../ui'
import { ThemeSelector } from '../ui'
import { useLayoutStore } from '../../stores/layoutStore'
import { cn } from '../../utils'
import { fadeVariants, staggerVariants, staggerItemVariants } from '../../design/animations'

interface SettingsSection {
  id: string
  title: string
  icon: React.ReactNode
  description: string
}

const settingsSections: SettingsSection[] = [
  {
    id: 'appearance',
    title: 'Appearance',
    icon: <Palette className="w-4 h-4" />,
    description: 'Theme, colors, and visual preferences'
  },
  {
    id: 'editor',
    title: 'Editor',
    icon: <Code className="w-4 h-4" />,
    description: 'Code editor settings and behavior'
  },
  {
    id: 'ai',
    title: 'AI Assistant',
    icon: <Zap className="w-4 h-4" />,
    description: 'AI model and API configuration'
  },
  {
    id: 'privacy',
    title: 'Privacy & Security',
    icon: <Shield className="w-4 h-4" />,
    description: 'Data handling and security settings'
  },
  {
    id: 'system',
    title: 'System',
    icon: <Monitor className="w-4 h-4" />,
    description: 'Performance and system preferences'
  }
]

export function SettingsPanel() {
  const [activeSection, setActiveSection] = useState('appearance')
  const [hasChanges, setHasChanges] = useState(false)
  const { resetLayout } = useLayoutStore()

  const handleSave = () => {
    // Save settings logic here
    setHasChanges(false)
    console.log('Settings saved')
  }

  const handleReset = () => {
    // Reset settings logic here
    setHasChanges(false)
    console.log('Settings reset')
  }

  const renderAppearanceSettings = () => (
    <motion.div variants={staggerVariants} initial="hidden" animate="visible" className="space-y-6">
      <motion.div variants={staggerItemVariants}>
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Theme</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Color Theme</label>
              <ThemeSelector />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">High Contrast</label>
                <p className="text-xs text-muted-foreground">Increase contrast for better visibility</p>
              </div>
              <input type="checkbox" className="rounded" />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Compact Mode</label>
                <p className="text-xs text-muted-foreground">Reduce spacing and padding</p>
              </div>
              <input type="checkbox" className="rounded" />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={staggerItemVariants}>
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Layout</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Show Status Bar</label>
                <p className="text-xs text-muted-foreground">Display status information at bottom</p>
              </div>
              <input type="checkbox" defaultChecked className="rounded" />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Show Title Bar</label>
                <p className="text-xs text-muted-foreground">Display application title bar</p>
              </div>
              <input type="checkbox" defaultChecked className="rounded" />
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={resetLayout}
              leftIcon={<RotateCcw className="w-3 h-3" />}
            >
              Reset Layout
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  )

  const renderEditorSettings = () => (
    <motion.div variants={staggerVariants} initial="hidden" animate="visible" className="space-y-6">
      <motion.div variants={staggerItemVariants}>
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Editor Behavior</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Font Size</label>
              <Input type="number" defaultValue="14" min="10" max="24" />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Tab Size</label>
              <Input type="number" defaultValue="2" min="1" max="8" />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Auto Save</label>
                <p className="text-xs text-muted-foreground">Automatically save files on change</p>
              </div>
              <input type="checkbox" defaultChecked className="rounded" />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Word Wrap</label>
                <p className="text-xs text-muted-foreground">Wrap long lines</p>
              </div>
              <input type="checkbox" className="rounded" />
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  )

  const renderAISettings = () => (
    <motion.div variants={staggerVariants} initial="hidden" animate="visible" className="space-y-6">
      <motion.div variants={staggerItemVariants}>
        <Card>
          <CardHeader>
            <CardTitle className="text-sm flex items-center gap-2">
              AI Model Configuration
              <Badge variant="success">Connected</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">API Key</label>
              <Input 
                type="password" 
                placeholder="Enter your Gemini API key"
                defaultValue="••••••••••••••••"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Model</label>
              <select className="w-full p-2 border border-input rounded-md bg-background">
                <option>Gemini 2.5 Flash Pro</option>
                <option>Gemini 1.5 Pro</option>
                <option>Gemini 1.5 Flash</option>
              </select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Max Tokens</label>
              <Input type="number" defaultValue="4096" min="100" max="8192" />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Temperature</label>
              <Input type="number" defaultValue="0.7" min="0" max="2" step="0.1" />
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  )

  const renderPrivacySettings = () => (
    <motion.div variants={staggerVariants} initial="hidden" animate="visible" className="space-y-6">
      <motion.div variants={staggerItemVariants}>
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Data & Privacy</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Send Usage Analytics</label>
                <p className="text-xs text-muted-foreground">Help improve the application</p>
              </div>
              <input type="checkbox" className="rounded" />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Store API Keys Locally</label>
                <p className="text-xs text-muted-foreground">Keep API keys on your device</p>
              </div>
              <input type="checkbox" defaultChecked className="rounded" />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Clear Chat History on Exit</label>
                <p className="text-xs text-muted-foreground">Automatically clear conversations</p>
              </div>
              <input type="checkbox" className="rounded" />
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  )

  const renderSystemSettings = () => (
    <motion.div variants={staggerVariants} initial="hidden" animate="visible" className="space-y-6">
      <motion.div variants={staggerItemVariants}>
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Performance</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Hardware Acceleration</label>
                <p className="text-xs text-muted-foreground">Use GPU for better performance</p>
              </div>
              <input type="checkbox" defaultChecked className="rounded" />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Reduce Animations</label>
                <p className="text-xs text-muted-foreground">Improve performance on slower devices</p>
              </div>
              <input type="checkbox" className="rounded" />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Memory Limit (MB)</label>
              <Input type="number" defaultValue="512" min="256" max="2048" />
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  )

  const renderContent = () => {
    switch (activeSection) {
      case 'appearance':
        return renderAppearanceSettings()
      case 'editor':
        return renderEditorSettings()
      case 'ai':
        return renderAISettings()
      case 'privacy':
        return renderPrivacySettings()
      case 'system':
        return renderSystemSettings()
      default:
        return renderAppearanceSettings()
    }
  }

  return (
    <motion.div
      variants={fadeVariants}
      initial="hidden"
      animate="visible"
      className="flex flex-col h-full bg-card"
    >
      {/* Settings Header */}
      <div className="flex items-center justify-between border-b border-border px-4 py-3">
        <div className="flex items-center space-x-2">
          <Settings className="w-5 h-5" />
          <h2 className="font-semibold">Settings</h2>
        </div>
        {hasChanges && (
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleReset}>
              Reset
            </Button>
            <Button size="sm" onClick={handleSave} leftIcon={<Save className="w-3 h-3" />}>
              Save
            </Button>
          </div>
        )}
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Settings Navigation */}
        <div className="w-48 border-r border-border p-2">
          <nav className="space-y-1">
            {settingsSections.map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={cn(
                  'w-full flex items-center space-x-3 px-3 py-2 rounded text-left',
                  'hover:bg-accent hover:text-accent-foreground transition-colors',
                  activeSection === section.id && 'bg-accent text-accent-foreground'
                )}
              >
                {section.icon}
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium truncate">{section.title}</div>
                </div>
              </button>
            ))}
          </nav>
        </div>

        {/* Settings Content */}
        <div className="flex-1 overflow-auto p-4">
          {renderContent()}
        </div>
      </div>
    </motion.div>
  )
}
