/**
 * Token counting utilities for different AI models
 */

export class TokenCounter {
  // Model-specific token estimation methods
  static estimateTokensForGemini(text: string): number {
    // Gemini uses a different tokenization approach
    // More accurate estimation based on Gemini's characteristics
    const words = text.split(/\s+/).length
    const characters = text.length

    // Gemini typically uses ~3.5 characters per token for English
    // But also consider word boundaries and special characters
    const charBasedEstimate = Math.ceil(characters / 3.5)
    const wordBasedEstimate = Math.ceil(words * 1.3)

    // Use the higher estimate for safety
    return Math.max(charBasedEstimate, wordBasedEstimate)
  }

  static estimateTokensForOpenAI(text: string): number {
    // OpenAI GPT models use ~4 characters per token on average
    const characters = text.length
    const words = text.split(/\s+/).length

    // More conservative estimation for OpenAI
    const charBasedEstimate = Math.ceil(characters / 4)
    const wordBasedEstimate = Math.ceil(words * 1.5)

    return Math.max(charBasedEstimate, wordBasedEstimate)
  }

  static estimateTokensGeneric(text: string): number {
    // Generic estimation for unknown models
    // Conservative approach using 4 characters per token
    return Math.ceil(text.length / 4)
  }

  // Context-aware token counting
  static estimateTokensWithContext(
    text: string,
    context: {
      language?: string
      model?: string
      includeSpecialTokens?: boolean
    } = {}
  ): number {
    const { language = 'en', model = 'generic', includeSpecialTokens = true } = context

    let baseTokens: number

    // Model-specific estimation
    switch (model.toLowerCase()) {
      case 'gemini':
      case 'gemini-1.5-flash':
      case 'gemini-1.5-pro':
      case 'gemini-2.5-flash':
      case 'gemini-2.5-pro':
        baseTokens = this.estimateTokensForGemini(text)
        break
      case 'gpt-3.5-turbo':
      case 'gpt-4':
      case 'gpt-4-turbo':
        baseTokens = this.estimateTokensForOpenAI(text)
        break
      default:
        baseTokens = this.estimateTokensGeneric(text)
    }

    // Language-specific adjustments
    const languageMultiplier = this.getLanguageMultiplier(language)
    baseTokens = Math.ceil(baseTokens * languageMultiplier)

    // Add special tokens if needed
    if (includeSpecialTokens) {
      baseTokens += this.estimateSpecialTokens(text)
    }

    return baseTokens
  }

  // Language-specific multipliers
  private static getLanguageMultiplier(language: string): number {
    const multipliers: Record<string, number> = {
      'en': 1.0,     // English baseline
      'es': 1.1,     // Spanish
      'fr': 1.1,     // French
      'de': 1.2,     // German (compound words)
      'it': 1.1,     // Italian
      'pt': 1.1,     // Portuguese
      'ru': 1.3,     // Russian (Cyrillic)
      'ja': 1.5,     // Japanese (complex tokenization)
      'ko': 1.4,     // Korean
      'zh': 1.6,     // Chinese (character-based)
      'ar': 1.3,     // Arabic
      'hi': 1.3,     // Hindi
      'tr': 1.2,     // Turkish
    }

    return multipliers[language.toLowerCase()] || 1.2 // Default for unknown languages
  }

  // Estimate special tokens (system prompts, formatting, etc.)
  private static estimateSpecialTokens(text: string): number {
    let specialTokens = 0

    // Count code blocks
    const codeBlocks = (text.match(/```[\s\S]*?```/g) || []).length
    specialTokens += codeBlocks * 2 // Start and end tokens

    // Count inline code
    const inlineCode = (text.match(/`[^`]+`/g) || []).length
    specialTokens += inlineCode * 2

    // Count markdown formatting
    const boldText = (text.match(/\*\*[^*]+\*\*/g) || []).length
    specialTokens += boldText * 2

    const italicText = (text.match(/\*[^*]+\*/g) || []).length
    specialTokens += italicText * 2

    // Count links
    const links = (text.match(/\[([^\]]+)\]\(([^)]+)\)/g) || []).length
    specialTokens += links * 3 // Link formatting tokens

    // Count lists
    const listItems = (text.match(/^[\s]*[-*+]\s/gm) || []).length
    specialTokens += listItems

    return specialTokens
  }

  // Batch token counting for multiple texts
  static estimateTokensForBatch(
    texts: string[],
    model: string = 'generic'
  ): { total: number; individual: number[] } {
    const individual = texts.map(text =>
      this.estimateTokensWithContext(text, { model })
    )

    const total = individual.reduce((sum, tokens) => sum + tokens, 0)

    return { total, individual }
  }

  // Token counting for conversation context
  static estimateConversationTokens(
    messages: Array<{ role: string; content: string }>,
    model: string = 'generic'
  ): number {
    let totalTokens = 0

    // Add tokens for each message
    for (const message of messages) {
      // Content tokens
      totalTokens += this.estimateTokensWithContext(message.content, { model })

      // Role tokens (system, user, assistant)
      totalTokens += 3 // Approximate tokens for role formatting
    }

    // Add conversation overhead tokens
    totalTokens += 10 // System prompt and conversation structure

    return totalTokens
  }

  // Utility to check if text exceeds token limit
  static exceedsTokenLimit(
    text: string,
    limit: number,
    model: string = 'generic'
  ): boolean {
    const tokens = this.estimateTokensWithContext(text, { model })
    return tokens > limit
  }

  // Truncate text to fit within token limit
  static truncateToTokenLimit(
    text: string,
    limit: number,
    model: string = 'generic'
  ): string {
    const estimatedTokens = this.estimateTokensWithContext(text, { model })

    if (estimatedTokens <= limit) {
      return text
    }

    // Estimate characters per token for this model
    const avgCharsPerToken = text.length / estimatedTokens
    const targetChars = Math.floor(limit * avgCharsPerToken * 0.9) // 10% safety margin

    // Truncate at word boundary
    const truncated = text.substring(0, targetChars)
    const lastSpace = truncated.lastIndexOf(' ')

    if (lastSpace > targetChars * 0.8) {
      return truncated.substring(0, lastSpace) + '...'
    }

    return truncated + '...'
  }

  // Get token usage statistics for a text
  static getTokenStats(text: string, model: string = 'generic'): TokenStats {
    const totalTokens = this.estimateTokensWithContext(text, { model })
    const words = text.split(/\s+/).length
    const characters = text.length
    const lines = text.split('\n').length

    return {
      totalTokens,
      words,
      characters,
      lines,
      avgTokensPerWord: totalTokens / Math.max(words, 1),
      avgCharsPerToken: characters / Math.max(totalTokens, 1)
    }
  }
}

// Supporting interfaces
interface TokenStats {
  totalTokens: number
  words: number
  characters: number
  lines: number
  avgTokensPerWord: number
  avgCharsPerToken: number
}