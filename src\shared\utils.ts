/**
 * Check if the app is running in development mode
 */
export const isDev = process.env.NODE_ENV === 'development' || process.env.DEBUG_PROD === 'true'

/**
 * Check if the app is running in production mode
 */
export const isProd = !isDev

/**
 * Get the current platform
 */
export const platform = process.platform

/**
 * Check if running on Windows
 */
export const isWindows = platform === 'win32'

/**
 * Check if running on macOS
 */
export const isMacOS = platform === 'darwin'

/**
 * Check if running on Linux
 */
export const isLinux = platform === 'linux'

/**
 * Application constants
 */
export const APP_CONSTANTS = {
  APP_NAME: 'AI Code Editor',
  APP_VERSION: '1.0.0',
  WINDOW_MIN_WIDTH: 800,
  WINDOW_MIN_HEIGHT: 600,
  WINDOW_DEFAULT_WIDTH: 1400,
  WINDOW_DEFAULT_HEIGHT: 900,
} as const

/**
 * File system constants
 */
export const FILE_CONSTANTS = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  SUPPORTED_LANGUAGES: [
    'javascript',
    'typescript',
    'python',
    'java',
    'csharp',
    'cpp',
    'c',
    'go',
    'rust',
    'php',
    'ruby',
    'swift',
    'kotlin',
    'dart',
    'html',
    'css',
    'scss',
    'sass',
    'less',
    'json',
    'xml',
    'yaml',
    'toml',
    'markdown',
    'sql',
    'shell',
    'powershell',
    'dockerfile',
    'prisma',
  ],
  BINARY_EXTENSIONS: [
    '.exe',
    '.dll',
    '.so',
    '.dylib',
    '.bin',
    '.img',
    '.iso',
    '.dmg',
    '.pkg',
    '.deb',
    '.rpm',
    '.msi',
    '.zip',
    '.rar',
    '.7z',
    '.tar',
    '.gz',
    '.bz2',
    '.xz',
  ],
} as const

/**
 * AI service constants
 */
export const AI_CONSTANTS = {
  DEFAULT_MODEL: 'gemini-pro',
  MAX_TOKENS: 4096,
  DEFAULT_TEMPERATURE: 0.7,
  MAX_CONTEXT_LENGTH: 32000,
  RATE_LIMIT_REQUESTS_PER_MINUTE: 60,
} as const
