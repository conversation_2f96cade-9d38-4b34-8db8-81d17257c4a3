{"name": "ai-code-editor", "version": "1.0.0", "description": "AI-powered code editor with Gemini 2.5 Flash Pro integration", "main": "dist/main/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:renderer": "vite", "dev:main": "npm run build:main && electron dist/main/main.js", "build:preload": "tsc src/preload/preload.ts --outDir dist/preload --target ES2020 --module CommonJS", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.main.json", "build:all": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "pack": "npm run build && electron-builder --dir", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "keywords": ["electron", "react", "typescript", "ai", "code-editor", "gemini", "vscode"], "author": "AI Code Editor Team", "license": "MIT", "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "electron": "^27.1.2", "electron-builder": "^24.6.4", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.5", "jest-environment-node": "^30.0.5", "ts-jest": "^29.1.1", "typescript": "^5.2.2", "vite": "^4.5.0", "vite-plugin-electron": "^0.15.5"}, "dependencies": {"@google/generative-ai": "^0.1.3", "@monaco-editor/react": "^4.6.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@react-spring/web": "^10.0.1", "@tanstack/react-query": "^5.8.4", "@types/chokidar": "^1.7.5", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "chokidar": "^3.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "electron-store": "^8.1.0", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "monaco-editor": "^0.44.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-window": "^1.8.11", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.3.6", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zustand": "^4.5.7"}, "build": {"appId": "com.aicodeeditor.app", "productName": "AI Code Editor", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.developer-tools"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}