/**
 * Animation System
 * Framer Motion presets and animation utilities
 */

import { Variants, Transition } from 'framer-motion'

// Common easing functions
export const easings = {
  easeInOut: [0.4, 0, 0.2, 1],
  easeOut: [0, 0, 0.2, 1],
  easeIn: [0.4, 0, 1, 1],
  sharp: [0.4, 0, 0.6, 1],
  bounce: [0.68, -0.55, 0.265, 1.55],
} as const

// Duration presets
export const durations = {
  fast: 0.15,
  normal: 0.3,
  slow: 0.5,
  slower: 0.8,
} as const

// Common transitions
export const transitions = {
  default: {
    duration: durations.normal,
    ease: easings.easeInOut,
  },
  fast: {
    duration: durations.fast,
    ease: easings.easeOut,
  },
  slow: {
    duration: durations.slow,
    ease: easings.easeInOut,
  },
  bounce: {
    duration: durations.normal,
    ease: easings.bounce,
  },
  spring: {
    type: 'spring',
    stiffness: 300,
    damping: 30,
  },
  springBouncy: {
    type: 'spring',
    stiffness: 400,
    damping: 25,
  },
} as const

// Fade animations
export const fadeVariants: Variants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: transitions.default,
  },
  exit: {
    opacity: 0,
    transition: transitions.fast,
  },
}

// Slide animations
export const slideVariants = {
  left: {
    hidden: { x: -50, opacity: 0 },
    visible: { x: 0, opacity: 1, transition: transitions.default },
    exit: { x: -50, opacity: 0, transition: transitions.fast },
  },
  right: {
    hidden: { x: 50, opacity: 0 },
    visible: { x: 0, opacity: 1, transition: transitions.default },
    exit: { x: 50, opacity: 0, transition: transitions.fast },
  },
  up: {
    hidden: { y: 50, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: transitions.default },
    exit: { y: 50, opacity: 0, transition: transitions.fast },
  },
  down: {
    hidden: { y: -50, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: transitions.default },
    exit: { y: -50, opacity: 0, transition: transitions.fast },
  },
} as const

// Scale animations
export const scaleVariants: Variants = {
  hidden: {
    scale: 0.8,
    opacity: 0,
  },
  visible: {
    scale: 1,
    opacity: 1,
    transition: transitions.spring,
  },
  exit: {
    scale: 0.8,
    opacity: 0,
    transition: transitions.fast,
  },
}

// Stagger animations for lists
export const staggerVariants: Variants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
  exit: {
    opacity: 0,
    transition: {
      staggerChildren: 0.05,
      staggerDirection: -1,
    },
  },
}

export const staggerItemVariants: Variants = {
  hidden: {
    y: 20,
    opacity: 0,
  },
  visible: {
    y: 0,
    opacity: 1,
    transition: transitions.default,
  },
  exit: {
    y: 20,
    opacity: 0,
    transition: transitions.fast,
  },
}

// Modal/Dialog animations
export const modalVariants: Variants = {
  hidden: {
    scale: 0.95,
    opacity: 0,
  },
  visible: {
    scale: 1,
    opacity: 1,
    transition: transitions.spring,
  },
  exit: {
    scale: 0.95,
    opacity: 0,
    transition: transitions.fast,
  },
}

export const overlayVariants: Variants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: transitions.fast,
  },
  exit: {
    opacity: 0,
    transition: transitions.fast,
  },
}

// Drawer/Sidebar animations
export const drawerVariants = {
  left: {
    hidden: { x: '-100%' },
    visible: { x: 0, transition: transitions.default },
    exit: { x: '-100%', transition: transitions.fast },
  },
  right: {
    hidden: { x: '100%' },
    visible: { x: 0, transition: transitions.default },
    exit: { x: '100%', transition: transitions.fast },
  },
  top: {
    hidden: { y: '-100%' },
    visible: { y: 0, transition: transitions.default },
    exit: { y: '-100%', transition: transitions.fast },
  },
  bottom: {
    hidden: { y: '100%' },
    visible: { y: 0, transition: transitions.default },
    exit: { y: '100%', transition: transitions.fast },
  },
} as const

// Loading animations
export const loadingVariants: Variants = {
  animate: {
    rotate: 360,
    transition: {
      duration: 1,
      repeat: Infinity,
      ease: 'linear',
    },
  },
}

export const pulseVariants: Variants = {
  animate: {
    scale: [1, 1.05, 1],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: easings.easeInOut,
    },
  },
}

// Hover animations
export const hoverVariants: Variants = {
  rest: {
    scale: 1,
  },
  hover: {
    scale: 1.05,
    transition: transitions.fast,
  },
  tap: {
    scale: 0.95,
    transition: transitions.fast,
  },
}

// Page transition animations
export const pageVariants: Variants = {
  initial: {
    opacity: 0,
    x: 20,
  },
  in: {
    opacity: 1,
    x: 0,
    transition: transitions.default,
  },
  out: {
    opacity: 0,
    x: -20,
    transition: transitions.fast,
  },
}

// Notification animations
export const notificationVariants = {
  'top-right': {
    hidden: { x: 400, opacity: 0 },
    visible: { x: 0, opacity: 1, transition: transitions.spring },
    exit: { x: 400, opacity: 0, transition: transitions.fast },
  },
  'top-left': {
    hidden: { x: -400, opacity: 0 },
    visible: { x: 0, opacity: 1, transition: transitions.spring },
    exit: { x: -400, opacity: 0, transition: transitions.fast },
  },
  'bottom-right': {
    hidden: { x: 400, opacity: 0 },
    visible: { x: 0, opacity: 1, transition: transitions.spring },
    exit: { x: 400, opacity: 0, transition: transitions.fast },
  },
  'bottom-left': {
    hidden: { x: -400, opacity: 0 },
    visible: { x: 0, opacity: 1, transition: transitions.spring },
    exit: { x: -400, opacity: 0, transition: transitions.fast },
  },
} as const

// Utility functions
export const createStaggerVariants = (staggerDelay = 0.1, childDelay = 0.1): Variants => ({
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: staggerDelay,
      delayChildren: childDelay,
    },
  },
})

export const createSlideVariants = (
  direction: 'left' | 'right' | 'up' | 'down',
  distance = 50
): Variants => {
  const axis = direction === 'left' || direction === 'right' ? 'x' : 'y'
  const value = direction === 'left' || direction === 'up' ? -distance : distance

  return {
    hidden: { [axis]: value, opacity: 0 },
    visible: { [axis]: 0, opacity: 1, transition: transitions.default },
    exit: { [axis]: value, opacity: 0, transition: transitions.fast },
  }
}

export const createScaleVariants = (scale = 0.8): Variants => ({
  hidden: { scale, opacity: 0 },
  visible: { scale: 1, opacity: 1, transition: transitions.spring },
  exit: { scale, opacity: 0, transition: transitions.fast },
})
