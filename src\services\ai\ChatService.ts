import { AIMessage, ProjectContext } from '../../types'
import { IChatService, ChatSession } from './interfaces'
import { v4 as uuidv4 } from 'uuid'

/**
 * Chat Service for managing chat sessions and messages
 */
export class ChatService implements IChatService {
  private sessions: Map<string, ChatSession> = new Map()
  private storageKey = 'ai-chat-sessions'
  private maxSessionsInMemory = 50
  private maxMessagesPerSession = 1000

  constructor() {
    this.loadSessionsFromStorage()
  }

  // Session management
  async createSession(projectId?: string): Promise<string> {
    const sessionId = uuidv4()
    const now = new Date()

    const session: ChatSession = {
      id: sessionId,
      projectId,
      title: this.generateSessionTitle(),
      messages: [],
      context: {
        files: [],
        dependencies: [],
        frameworks: []
      },
      createdAt: now,
      updatedAt: now,
      metadata: {}
    }

    this.sessions.set(sessionId, session)
    await this.persistSessions()

    return sessionId
  }

  async getSession(sessionId: string): Promise<ChatSession | null> {
    const session = this.sessions.get(sessionId)
    if (!session) {
      // Try to load from storage if not in memory
      await this.loadSessionsFromStorage()
      return this.sessions.get(sessionId) || null
    }
    return session
  }

  async deleteSession(sessionId: string): Promise<void> {
    this.sessions.delete(sessionId)
    await this.persistSessions()
  }

  async getAllSessions(): Promise<ChatSession[]> {
    await this.loadSessionsFromStorage()
    return Array.from(this.sessions.values())
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
  }

  // Message operations
  async addMessage(sessionId: string, message: AIMessage): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) {
      throw new Error(`Session ${sessionId} not found`)
    }

    // Add message to session
    session.messages.push(message)
    session.updatedAt = new Date()

    // Update title if this is the first user message
    if (message.type === 'user' && session.messages.filter(m => m.type === 'user').length === 1) {
      session.title = this.generateTitleFromMessage(message.content)
    }

    // Limit messages per session
    if (session.messages.length > this.maxMessagesPerSession) {
      session.messages = session.messages.slice(-this.maxMessagesPerSession)
    }

    this.sessions.set(sessionId, session)
    await this.persistSessions()
  }

  async getMessages(sessionId: string): Promise<AIMessage[]> {
    const session = await this.getSession(sessionId)
    return session?.messages || []
  }

  async clearMessages(sessionId: string): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) {
      throw new Error(`Session ${sessionId} not found`)
    }

    session.messages = []
    session.updatedAt = new Date()
    this.sessions.set(sessionId, session)
    await this.persistSessions()
  }

  // Context management
  async updateSessionContext(sessionId: string, context: ProjectContext): Promise<void> {
    const session = await this.getSession(sessionId)
    if (!session) {
      throw new Error(`Session ${sessionId} not found`)
    }

    session.context = { ...session.context, ...context }
    session.updatedAt = new Date()
    this.sessions.set(sessionId, session)
    await this.persistSessions()
  }

  async getSessionContext(sessionId: string): Promise<ProjectContext | null> {
    const session = await this.getSession(sessionId)
    return session?.context || null
  }

  // Advanced operations
  async searchSessions(query: string): Promise<ChatSession[]> {
    const allSessions = await this.getAllSessions()
    const lowercaseQuery = query.toLowerCase()

    return allSessions.filter(session => {
      // Search in title
      if (session.title?.toLowerCase().includes(lowercaseQuery)) {
        return true
      }

      // Search in messages
      return session.messages.some(message =>
        message.content.toLowerCase().includes(lowercaseQuery)
      )
    })
  }

  async getSessionsByProject(projectId: string): Promise<ChatSession[]> {
    const allSessions = await this.getAllSessions()
    return allSessions.filter(session => session.projectId === projectId)
  }

  async duplicateSession(sessionId: string): Promise<string> {
    const originalSession = await this.getSession(sessionId)
    if (!originalSession) {
      throw new Error(`Session ${sessionId} not found`)
    }

    const newSessionId = await this.createSession(originalSession.projectId)
    const newSession = await this.getSession(newSessionId)

    if (newSession) {
      newSession.title = `${originalSession.title} (Copy)`
      newSession.context = { ...originalSession.context }
      newSession.messages = originalSession.messages.map(msg => ({
        ...msg,
        id: uuidv4() // Generate new IDs for copied messages
      }))

      this.sessions.set(newSessionId, newSession)
      await this.persistSessions()
    }

    return newSessionId
  }

  async exportSession(sessionId: string): Promise<string> {
    const session = await this.getSession(sessionId)
    if (!session) {
      throw new Error(`Session ${sessionId} not found`)
    }

    return JSON.stringify(session, null, 2)
  }

  async importSession(sessionData: string): Promise<string> {
    try {
      const session: ChatSession = JSON.parse(sessionData)

      // Generate new ID and update timestamps
      const newSessionId = uuidv4()
      session.id = newSessionId
      session.createdAt = new Date()
      session.updatedAt = new Date()

      // Generate new message IDs
      session.messages = session.messages.map(msg => ({
        ...msg,
        id: uuidv4()
      }))

      this.sessions.set(newSessionId, session)
      await this.persistSessions()

      return newSessionId
    } catch (error) {
      throw new Error(`Failed to import session: ${error}`)
    }
  }

  // Statistics and analytics
  getSessionStatistics(sessionId: string): Promise<SessionStatistics> {
    return new Promise(async (resolve) => {
      const session = await this.getSession(sessionId)
      if (!session) {
        throw new Error(`Session ${sessionId} not found`)
      }

      const userMessages = session.messages.filter(m => m.type === 'user')
      const aiMessages = session.messages.filter(m => m.type === 'ai')
      const totalTokens = session.messages.reduce((sum, msg) =>
        sum + (msg.metadata?.tokensUsed || 0), 0
      )

      resolve({
        totalMessages: session.messages.length,
        userMessages: userMessages.length,
        aiMessages: aiMessages.length,
        totalTokens,
        averageTokensPerMessage: totalTokens / Math.max(session.messages.length, 1),
        sessionDuration: session.updatedAt.getTime() - session.createdAt.getTime(),
        lastActivity: session.updatedAt
      })
    })
  }

  // Storage operations
  private async loadSessionsFromStorage(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem(this.storageKey)
        if (stored) {
          const sessionsData = JSON.parse(stored)

          // Convert date strings back to Date objects
          for (const sessionData of sessionsData) {
            sessionData.createdAt = new Date(sessionData.createdAt)
            sessionData.updatedAt = new Date(sessionData.updatedAt)

            // Convert message timestamps
            sessionData.messages.forEach((msg: any) => {
              msg.timestamp = new Date(msg.timestamp)
            })

            this.sessions.set(sessionData.id, sessionData)
          }

          // Limit sessions in memory
          if (this.sessions.size > this.maxSessionsInMemory) {
            const sortedSessions = Array.from(this.sessions.values())
              .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())

            this.sessions.clear()
            sortedSessions.slice(0, this.maxSessionsInMemory).forEach(session => {
              this.sessions.set(session.id, session)
            })
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load sessions from storage:', error)
    }
  }

  private async persistSessions(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const sessionsArray = Array.from(this.sessions.values())
        localStorage.setItem(this.storageKey, JSON.stringify(sessionsArray))
      }
    } catch (error) {
      console.warn('Failed to persist sessions to storage:', error)
    }
  }

  // Title generation utilities
  private generateSessionTitle(): string {
    const now = new Date()
    const timeString = now.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    })
    return `Chat ${timeString}`
  }

  private generateTitleFromMessage(content: string): string {
    // Extract first meaningful part of the message
    const cleaned = content.trim().replace(/\n+/g, ' ')
    const maxLength = 50

    if (cleaned.length <= maxLength) {
      return cleaned
    }

    // Try to break at word boundary
    const truncated = cleaned.substring(0, maxLength)
    const lastSpace = truncated.lastIndexOf(' ')

    if (lastSpace > maxLength * 0.7) {
      return truncated.substring(0, lastSpace) + '...'
    }

    return truncated + '...'
  }

  // Cleanup and maintenance
  async cleanupOldSessions(daysOld: number = 30): Promise<number> {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysOld)

    const allSessions = await this.getAllSessions()
    const oldSessions = allSessions.filter(session =>
      session.updatedAt < cutoffDate
    )

    for (const session of oldSessions) {
      await this.deleteSession(session.id)
    }

    return oldSessions.length
  }

  async getStorageUsage(): Promise<StorageUsage> {
    const allSessions = await this.getAllSessions()
    const totalSessions = allSessions.length
    const totalMessages = allSessions.reduce((sum, session) =>
      sum + session.messages.length, 0
    )

    let estimatedSize = 0
    if (typeof window !== 'undefined' && window.localStorage) {
      const stored = localStorage.getItem(this.storageKey)
      estimatedSize = stored ? stored.length : 0
    }

    return {
      totalSessions,
      totalMessages,
      estimatedSizeBytes: estimatedSize,
      estimatedSizeMB: estimatedSize / (1024 * 1024)
    }
  }

  // Cleanup method
  cleanup(): void {
    this.sessions.clear()
  }
}

// Supporting interfaces
interface SessionStatistics {
  totalMessages: number
  userMessages: number
  aiMessages: number
  totalTokens: number
  averageTokensPerMessage: number
  sessionDuration: number
  lastActivity: Date
}

interface StorageUsage {
  totalSessions: number
  totalMessages: number
  estimatedSizeBytes: number
  estimatedSizeMB: number
}