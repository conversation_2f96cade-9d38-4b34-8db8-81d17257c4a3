/**
 * Chat Panel Component
 * AI chat interface panel
 */

import React, { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, <PERSON><PERSON>, User, Sparkles, Copy, ThumbsUp, ThumbsDown } from 'lucide-react'
import { Button, Input, Badge } from '../ui'
import { cn } from '../../utils'
import { fadeVariants, staggerVariants, staggerItemVariants } from '../../design/animations'

interface ChatMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  isLoading?: boolean
}

const mockMessages: ChatMessage[] = [
  {
    id: '1',
    type: 'assistant',
    content: 'Hello! I\'m your AI coding assistant. I can help you with code review, debugging, writing new features, and answering questions about your project. How can I assist you today?',
    timestamp: new Date(Date.now() - 300000)
  },
  {
    id: '2',
    type: 'user',
    content: 'Can you help me create a responsive layout component?',
    timestamp: new Date(Date.now() - 240000)
  },
  {
    id: '3',
    type: 'assistant',
    content: 'Absolutely! I can help you create a responsive layout component. Here\'s a modern approach using CSS Grid and Flexbox:\n\n```tsx\ninterface LayoutProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function ResponsiveLayout({ children, className }: LayoutProps) {\n  return (\n    <div className={cn(\n      "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",\n      className\n    )}>\n      {children}\n    </div>\n  )\n}\n```\n\nThis component uses Tailwind CSS classes for responsive breakpoints. Would you like me to explain the breakpoints or add more features?',
    timestamp: new Date(Date.now() - 180000)
  }
]

export function ChatPanel() {
  const [messages, setMessages] = useState<ChatMessage[]>(mockMessages)
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    // Auto-scroll to bottom when new messages are added
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: input.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsLoading(true)

    // Simulate AI response
    setTimeout(() => {
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: 'I understand your request. Let me help you with that. This is a simulated response for demonstration purposes. In the actual implementation, this would connect to the Gemini API.',
        timestamp: new Date()
      }

      setMessages(prev => [...prev, assistantMessage])
      setIsLoading(false)
    }, 2000)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const copyToClipboard = (content: string) => {
    navigator.clipboard.writeText(content)
    // You could show a toast notification here
  }

  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  return (
    <motion.div
      variants={fadeVariants}
      initial="hidden"
      animate="visible"
      className="flex flex-col h-full bg-card"
    >
      {/* Chat Header */}
      <div className="flex items-center justify-between border-b border-border px-4 py-3">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Bot className="w-5 h-5 text-primary" />
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="absolute -top-1 -right-1"
            >
              <Sparkles className="w-3 h-3 text-accent" />
            </motion.div>
          </div>
          <div>
            <h3 className="font-semibold text-sm">AI Assistant</h3>
            <p className="text-xs text-muted-foreground">Gemini 2.5 Flash Pro</p>
          </div>
        </div>
        <Badge variant="success" className="text-xs">
          Online
        </Badge>
      </div>

      {/* Messages Area */}
      <motion.div
        variants={staggerVariants}
        initial="hidden"
        animate="visible"
        className="flex-1 overflow-auto p-4 space-y-4"
      >
        {messages.map((message) => (
          <motion.div
            key={message.id}
            variants={staggerItemVariants}
            className={cn(
              'flex space-x-3',
              message.type === 'user' ? 'justify-end' : 'justify-start'
            )}
          >
            {/* Avatar */}
            {message.type === 'assistant' && (
              <div className="flex-shrink-0">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <Bot className="w-4 h-4 text-primary" />
                </div>
              </div>
            )}

            {/* Message Content */}
            <div
              className={cn(
                'max-w-[80%] rounded-lg px-3 py-2 group',
                message.type === 'user'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted'
              )}
            >
              <div className="text-sm whitespace-pre-wrap">
                {message.content}
              </div>
              
              {/* Message Actions */}
              <div className="flex items-center justify-between mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <span className="text-xs opacity-70">
                  {formatTimestamp(message.timestamp)}
                </span>
                
                {message.type === 'assistant' && (
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => copyToClipboard(message.content)}
                      className="p-1 rounded hover:bg-accent hover:text-accent-foreground"
                      title="Copy message"
                    >
                      <Copy className="w-3 h-3" />
                    </button>
                    <button
                      className="p-1 rounded hover:bg-accent hover:text-accent-foreground"
                      title="Good response"
                    >
                      <ThumbsUp className="w-3 h-3" />
                    </button>
                    <button
                      className="p-1 rounded hover:bg-accent hover:text-accent-foreground"
                      title="Poor response"
                    >
                      <ThumbsDown className="w-3 h-3" />
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* User Avatar */}
            {message.type === 'user' && (
              <div className="flex-shrink-0">
                <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
                  <User className="w-4 h-4 text-secondary-foreground" />
                </div>
              </div>
            )}
          </motion.div>
        ))}

        {/* Loading Indicator */}
        {isLoading && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex space-x-3"
          >
            <div className="flex-shrink-0">
              <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                <Bot className="w-4 h-4 text-primary" />
              </div>
            </div>
            <div className="bg-muted rounded-lg px-3 py-2">
              <div className="flex space-x-1">
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}
                  className="w-2 h-2 bg-muted-foreground rounded-full"
                />
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}
                  className="w-2 h-2 bg-muted-foreground rounded-full"
                />
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}
                  className="w-2 h-2 bg-muted-foreground rounded-full"
                />
              </div>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </motion.div>

      {/* Input Area */}
      <div className="border-t border-border p-4">
        <div className="flex space-x-2">
          <Input
            ref={inputRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Ask me anything about your code..."
            className="flex-1"
            disabled={isLoading}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!input.trim() || isLoading}
            size="icon"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
          <span>Press Enter to send, Shift+Enter for new line</span>
          <span>Tokens: 1,234 / 4,096</span>
        </div>
      </div>
    </motion.div>
  )
}
