import { ISettingsManager, EditorSettings } from '../ai/interfaces'

/**
 * Settings Manager for handling application configuration
 * Provides secure storage for settings and API keys
 */
export class SettingsManager implements ISettingsManager {
  private settings: Map<string, any> = new Map()
  private encryptedKeys: Map<string, string> = new Map()
  private storageKey = 'ai-app-settings'
  private keysStorageKey = 'ai-app-encrypted-keys'
  private isInitialized = false

  constructor() {
    this.initializeSettings()
  }

  // General settings management
  async get<T>(key: string): Promise<T | null> {
    await this.ensureInitialized()
    return this.settings.get(key) || null
  }

  async set<T>(key: string, value: T): Promise<void> {
    await this.ensureInitialized()
    this.settings.set(key, value)
    await this.persistSettings()
  }

  async delete(key: string): Promise<void> {
    await this.ensureInitialized()
    this.settings.delete(key)
    await this.persistSettings()
  }

  async getAll(): Promise<Record<string, any>> {
    await this.ensureInitialized()
    const result: Record<string, any> = {}
    for (const [key, value] of this.settings) {
      result[key] = value
    }
    return result
  }

  async clear(): Promise<void> {
    this.settings.clear()
    this.encryptedKeys.clear()
    await this.persistSettings()
    await this.persistEncryptedKeys()
  }

  // API key management with encryption
  async getApiKey(provider: string): Promise<string | null> {
    await this.ensureInitialized()
    const encryptedKey = this.encryptedKeys.get(`apiKey.${provider}`)
    if (!encryptedKey) return null

    try {
      return await this.decryptValue(encryptedKey)
    } catch (error) {
      console.warn(`Failed to decrypt API key for ${provider}:`, error)
      return null
    }
  }

  async setApiKey(provider: string, key: string): Promise<void> {
    await this.ensureInitialized()

    try {
      const encryptedKey = await this.encryptValue(key)
      this.encryptedKeys.set(`apiKey.${provider}`, encryptedKey)
      await this.persistEncryptedKeys()
    } catch (error) {
      throw new Error(`Failed to encrypt API key for ${provider}: ${error}`)
    }
  }

  async deleteApiKey(provider: string): Promise<void> {
    await this.ensureInitialized()
    this.encryptedKeys.delete(`apiKey.${provider}`)
    await this.persistEncryptedKeys()
  }

  // Model settings
  async getDefaultModel(): Promise<string | null> {
    return await this.get<string>('defaultModel')
  }

  async setDefaultModel(modelId: string): Promise<void> {
    await this.set('defaultModel', modelId)
  }

  // Token settings
  async getTokenLimit(): Promise<number> {
    const limit = await this.get<number>('tokenLimit.daily')
    return limit || 100000 // Default 100K tokens per day
  }

  async setTokenLimit(limit: number): Promise<void> {
    await this.set('tokenLimit.daily', limit)
  }

  // Theme and UI settings
  async getTheme(): Promise<'light' | 'dark' | 'auto'> {
    const theme = await this.get<string>('theme')
    return (theme as 'light' | 'dark' | 'auto') || 'auto'
  }

  async setTheme(theme: 'light' | 'dark' | 'auto'): Promise<void> {
    await this.set('theme', theme)
  }

  // Editor settings
  async getEditorSettings(): Promise<EditorSettings> {
    const settings = await this.get<Partial<EditorSettings>>('editorSettings')

    // Return default settings merged with user settings
    return {
      fontSize: 14,
      fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
      tabSize: 2,
      wordWrap: true,
      lineNumbers: true,
      minimap: true,
      autoSave: true,
      autoSaveDelay: 1000,
      theme: 'vs-dark',
      ...settings
    }
  }

  async setEditorSettings(settings: Partial<EditorSettings>): Promise<void> {
    const currentSettings = await this.getEditorSettings()
    const newSettings = { ...currentSettings, ...settings }
    await this.set('editorSettings', newSettings)
  }

  // Advanced settings management
  async getSettingsGroup(groupPrefix: string): Promise<Record<string, any>> {
    await this.ensureInitialized()
    const result: Record<string, any> = {}

    for (const [key, value] of this.settings) {
      if (key.startsWith(groupPrefix)) {
        const subKey = key.substring(groupPrefix.length)
        result[subKey] = value
      }
    }

    return result
  }

  async setSettingsGroup(groupPrefix: string, settings: Record<string, any>): Promise<void> {
    await this.ensureInitialized()

    // Clear existing group settings
    const keysToDelete: string[] = []
    for (const key of this.settings.keys()) {
      if (key.startsWith(groupPrefix)) {
        keysToDelete.push(key)
      }
    }

    for (const key of keysToDelete) {
      this.settings.delete(key)
    }

    // Set new group settings
    for (const [subKey, value] of Object.entries(settings)) {
      this.settings.set(groupPrefix + subKey, value)
    }

    await this.persistSettings()
  }

  async hasApiKey(provider: string): Promise<boolean> {
    await this.ensureInitialized()
    return this.encryptedKeys.has(`apiKey.${provider}`)
  }

  async getConfiguredProviders(): Promise<string[]> {
    await this.ensureInitialized()
    const providers: string[] = []

    for (const key of this.encryptedKeys.keys()) {
      if (key.startsWith('apiKey.')) {
        providers.push(key.substring(7)) // Remove 'apiKey.' prefix
      }
    }

    return providers
  }

  // Import/Export functionality
  async exportSettings(): Promise<string> {
    await this.ensureInitialized()
    const allSettings = await this.getAll()

    // Don't export encrypted keys for security
    const exportData = {
      settings: allSettings,
      timestamp: new Date().toISOString(),
      version: '1.0'
    }

    return JSON.stringify(exportData, null, 2)
  }

  async importSettings(settingsJson: string): Promise<void> {
    try {
      const importData = JSON.parse(settingsJson)

      if (!importData.settings || typeof importData.settings !== 'object') {
        throw new Error('Invalid settings format')
      }

      // Clear existing settings (but keep API keys)
      this.settings.clear()

      // Import new settings
      for (const [key, value] of Object.entries(importData.settings)) {
        this.settings.set(key, value)
      }

      await this.persistSettings()
    } catch (error) {
      throw new Error(`Failed to import settings: ${error}`)
    }
  }

  // Validation and migration
  async validateSettings(): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = []

    try {
      // Validate theme setting
      const theme = await this.getTheme()
      if (!['light', 'dark', 'auto'].includes(theme)) {
        errors.push(`Invalid theme: ${theme}`)
      }

      // Validate token limit
      const tokenLimit = await this.getTokenLimit()
      if (tokenLimit < 1000 || tokenLimit > 10000000) {
        errors.push(`Token limit out of range: ${tokenLimit}`)
      }

      // Validate editor settings
      const editorSettings = await this.getEditorSettings()
      if (editorSettings.fontSize < 8 || editorSettings.fontSize > 72) {
        errors.push(`Font size out of range: ${editorSettings.fontSize}`)
      }

      if (editorSettings.tabSize < 1 || editorSettings.tabSize > 8) {
        errors.push(`Tab size out of range: ${editorSettings.tabSize}`)
      }

    } catch (error) {
      errors.push(`Validation error: ${error}`)
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  async migrateSettings(fromVersion: string, toVersion: string): Promise<void> {
    // Future migration logic can be added here
    console.log(`Migrating settings from ${fromVersion} to ${toVersion}`)
  }

  // Private utility methods
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initializeSettings()
    }
  }

  private async initializeSettings(): Promise<void> {
    try {
      await Promise.all([
        this.loadSettingsFromStorage(),
        this.loadEncryptedKeysFromStorage()
      ])
      this.isInitialized = true
    } catch (error) {
      console.warn('Failed to initialize settings:', error)
      this.isInitialized = true // Continue with empty settings
    }
  }

  private async loadSettingsFromStorage(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem(this.storageKey)
        if (stored) {
          const data = JSON.parse(stored)

          for (const [key, value] of Object.entries(data)) {
            this.settings.set(key, value)
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load settings from storage:', error)
    }
  }

  private async loadEncryptedKeysFromStorage(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem(this.keysStorageKey)
        if (stored) {
          const data = JSON.parse(stored)

          for (const [key, value] of Object.entries(data)) {
            this.encryptedKeys.set(key, value as string)
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load encrypted keys from storage:', error)
    }
  }

  private async persistSettings(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const data: Record<string, any> = {}
        for (const [key, value] of this.settings) {
          data[key] = value
        }
        localStorage.setItem(this.storageKey, JSON.stringify(data))
      }
    } catch (error) {
      console.warn('Failed to persist settings:', error)
    }
  }

  private async persistEncryptedKeys(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const data: Record<string, string> = {}
        for (const [key, value] of this.encryptedKeys) {
          data[key] = value
        }
        localStorage.setItem(this.keysStorageKey, JSON.stringify(data))
      }
    } catch (error) {
      console.warn('Failed to persist encrypted keys:', error)
    }
  }

  // Simple encryption/decryption using Web Crypto API
  private async encryptValue(value: string): Promise<string> {
    try {
      if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
        // Generate a key from a password (in a real app, this should be more secure)
        const password = await this.getOrCreateEncryptionKey()
        const encoder = new TextEncoder()
        const data = encoder.encode(value)

        // Generate a random IV
        const iv = window.crypto.getRandomValues(new Uint8Array(12))

        // Import the key
        const key = await window.crypto.subtle.importKey(
          'raw',
          encoder.encode(password),
          { name: 'AES-GCM' },
          false,
          ['encrypt']
        )

        // Encrypt the data
        const encrypted = await window.crypto.subtle.encrypt(
          { name: 'AES-GCM', iv },
          key,
          data
        )

        // Combine IV and encrypted data
        const combined = new Uint8Array(iv.length + encrypted.byteLength)
        combined.set(iv)
        combined.set(new Uint8Array(encrypted), iv.length)

        // Convert to base64
        return btoa(String.fromCharCode(...combined))
      } else {
        // Fallback: simple base64 encoding (not secure, but better than plain text)
        return btoa(value)
      }
    } catch (error) {
      console.warn('Encryption failed, using base64 fallback:', error)
      return btoa(value)
    }
  }

  private async decryptValue(encryptedValue: string): Promise<string> {
    try {
      if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
        const password = await this.getOrCreateEncryptionKey()
        const encoder = new TextEncoder()
        const decoder = new TextDecoder()

        // Convert from base64
        const combined = new Uint8Array(
          atob(encryptedValue).split('').map(char => char.charCodeAt(0))
        )

        // Extract IV and encrypted data
        const iv = combined.slice(0, 12)
        const encrypted = combined.slice(12)

        // Import the key
        const key = await window.crypto.subtle.importKey(
          'raw',
          encoder.encode(password),
          { name: 'AES-GCM' },
          false,
          ['decrypt']
        )

        // Decrypt the data
        const decrypted = await window.crypto.subtle.decrypt(
          { name: 'AES-GCM', iv },
          key,
          encrypted
        )

        return decoder.decode(decrypted)
      } else {
        // Fallback: simple base64 decoding
        return atob(encryptedValue)
      }
    } catch (error) {
      console.warn('Decryption failed, trying base64 fallback:', error)
      try {
        return atob(encryptedValue)
      } catch (fallbackError) {
        throw new Error('Failed to decrypt value')
      }
    }
  }

  private async getOrCreateEncryptionKey(): Promise<string> {
    const keyName = 'encryption-key'
    let key = this.settings.get(keyName)

    if (!key) {
      // Generate a random key
      if (typeof window !== 'undefined' && window.crypto) {
        const array = new Uint8Array(32)
        window.crypto.getRandomValues(array)
        key = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
      } else {
        // Fallback: generate a simple key
        key = Math.random().toString(36).substring(2) + Math.random().toString(36).substring(2)
      }

      this.settings.set(keyName, key)
      await this.persistSettings()
    }

    return key
  }

  // Cleanup method
  cleanup(): void {
    this.settings.clear()
    this.encryptedKeys.clear()
    this.isInitialized = false
  }

  // Statistics and monitoring
  getStorageStats(): {
    settingsCount: number
    encryptedKeysCount: number
    estimatedSize: number
  } {
    let estimatedSize = 0

    if (typeof window !== 'undefined' && window.localStorage) {
      const settingsData = localStorage.getItem(this.storageKey)
      const keysData = localStorage.getItem(this.keysStorageKey)

      estimatedSize = (settingsData?.length || 0) + (keysData?.length || 0)
    }

    return {
      settingsCount: this.settings.size,
      encryptedKeysCount: this.encryptedKeys.size,
      estimatedSize
    }
  }
}