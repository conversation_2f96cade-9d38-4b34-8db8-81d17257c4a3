// Core application types
export interface AppInfo {
  version: string
  platform: string
  name: string
}

// File system types
export interface FileNode {
  name: string
  path: string
  type: 'file' | 'folder'
  children?: FileNode[]
  extension?: string
  size?: number
  lastModified?: Date
}

export interface ProjectStructure {
  root: string
  files: FileNode[]
  name: string
  type?: ProjectType
}

export interface FileContent {
  path: string
  content: string
  language: string
  encoding: string
  lastModified: Date
}

export interface FileEvent {
  type: 'add' | 'change' | 'unlink' | 'addDir' | 'unlinkDir'
  path: string
  stats?: any
}

// Project types
export type ProjectType = 
  | 'react'
  | 'vue'
  | 'angular'
  | 'node'
  | 'python'
  | 'java'
  | 'csharp'
  | 'go'
  | 'rust'
  | 'php'
  | 'ruby'
  | 'unknown'

// AI service types
export interface AIModel {
  id: string
  name: string
  provider: 'gemini' | 'openai' | 'anthropic'
  version: string
  maxTokens: number
  costPerToken: number
  description?: string
}

export interface AIMessage {
  id: string
  type: 'user' | 'ai' | 'system'
  content: string
  timestamp: Date
  metadata?: MessageMetadata
}

export interface MessageMetadata {
  tokensUsed?: number
  model?: string
  context?: string[]
  attachments?: Attachment[]
  processingTime?: number
}

export interface Attachment {
  type: 'file' | 'image' | 'code' | 'url'
  name: string
  content: string
  metadata?: Record<string, any>
}

export interface AIResponse {
  content: string
  tokensUsed: number
  model: string
  processingTime: number
  confidence?: number
  suggestions?: string[]
}

export interface ProjectContext {
  files: FileContent[]
  currentFile?: string
  selectedCode?: string
  projectType?: ProjectType
  dependencies?: Dependency[]
  frameworks?: Framework[]
}

// Chat types
export interface ChatSession {
  id: string
  projectId?: string
  messages: AIMessage[]
  context: ProjectContext
  createdAt: Date
  updatedAt: Date
  title?: string
}

// Settings types
export interface AppSettings {
  theme: 'light' | 'dark' | 'auto'
  fontSize: number
  fontFamily: string
  autoSave: boolean
  aiModel: string
  apiKeys: Record<string, string>
  tokenLimit: number
  language: string
  shortcuts: Record<string, string>
}

export interface TokenUsage {
  daily: number
  monthly: number
  total: number
  remaining: number
  cost: number
  lastReset: Date
}

// Terminal types
export interface Terminal {
  id: string
  pid: number
  cwd: string
  shell: string
  isActive: boolean
  title?: string
}

export interface CommandResult {
  exitCode: number
  stdout: string
  stderr: string
  duration: number
}

// Error types
export interface AppError {
  code: string
  message: string
  details?: any
  timestamp: Date
  stack?: string
}

export type ErrorType = 
  | 'file_system'
  | 'ai_service'
  | 'terminal'
  | 'network'
  | 'validation'
  | 'unknown'

// UI types
export interface PanelConfig {
  id: string
  title: string
  icon: string
  defaultSize: number
  minSize: number
  maxSize: number
  resizable: boolean
  collapsible: boolean
  position: 'left' | 'right' | 'bottom' | 'center'
  visible: boolean
}

export interface LayoutState {
  panels: Record<string, PanelConfig>
  activePanel?: string
  sidebarWidth: number
  rightPanelWidth: number
  bottomPanelHeight: number
}

// Code analysis types
export interface CodeAnalysis {
  errors: ErrorDetection[]
  warnings: ErrorDetection[]
  suggestions: CodeSuggestion[]
  metrics: CodeMetrics
}

export interface ErrorDetection {
  id: string
  type: 'syntax' | 'runtime' | 'logic' | 'performance' | 'security'
  severity: 'error' | 'warning' | 'info'
  message: string
  line: number
  column: number
  suggestion?: string
  autoFix?: boolean
}

export interface CodeSuggestion {
  id: string
  type: 'refactor' | 'optimize' | 'style' | 'best_practice'
  message: string
  line: number
  column: number
  replacement?: string
  confidence: number
}

export interface CodeMetrics {
  complexity: number
  maintainability: number
  performance: number
  security: number
  testCoverage?: number
  linesOfCode: number
}

// External service types
export interface SearchResult {
  title: string
  url: string
  snippet: string
  source: string
  relevance: number
}

export interface WebContent {
  url: string
  title: string
  content: string
  metadata: Record<string, any>
  extractedAt: Date
}

// Framework and dependency types
export interface Framework {
  name: string
  version: string
  type: 'frontend' | 'backend' | 'fullstack' | 'mobile' | 'desktop'
  configFiles: string[]
}

export interface Dependency {
  name: string
  version: string
  type: 'production' | 'development' | 'peer' | 'optional'
  description?: string
  homepage?: string
}

// Theme types
export interface ThemeColors {
  primary: string
  secondary: string
  accent: string
  background: string
  surface: string
  text: {
    primary: string
    secondary: string
    muted: string
  }
  semantic: {
    success: string
    warning: string
    error: string
    info: string
  }
}

export interface Theme {
  name: string
  type: 'light' | 'dark'
  colors: ThemeColors
  fonts: {
    mono: string
    sans: string
  }
  spacing: Record<string, string>
  borderRadius: Record<string, string>
  shadows: Record<string, string>
}
