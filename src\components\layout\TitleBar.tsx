/**
 * Title Bar Component
 * Application title bar with window controls and menu
 */

import React from 'react'
import { motion } from 'framer-motion'
import { Code2, Menu, Minimize2, Maximize2, X, <PERSON><PERSON><PERSON>, Search } from 'lucide-react'
import { useLayoutStore } from '../../stores/layoutStore'
import { ThemeToggle } from '../ui'
import { cn } from '../../utils'

export function TitleBar() {
  const { 
    togglePanel, 
    showPanel, 
    panels,
    isFullscreen,
    toggleFullscreen 
  } = useLayoutStore()

  const handleMinimize = () => {
    // In Electron, this would minimize the window
    if (window.electronAPI?.minimize) {
      window.electronAPI.minimize()
    }
  }

  const handleMaximize = () => {
    // In Electron, this would maximize/restore the window
    if (window.electronAPI?.maximize) {
      window.electronAPI.maximize()
    } else {
      toggleFullscreen()
    }
  }

  const handleClose = () => {
    // In Electron, this would close the window
    if (window.electronAPI?.close) {
      window.electronAPI.close()
    }
  }

  const handleMenuClick = () => {
    // Toggle sidebar or show command palette
    if (panels.sidebar.isVisible) {
      togglePanel('sidebar')
    } else {
      showPanel('sidebar')
    }
  }

  const handleSearchClick = () => {
    // Open search/command palette
    // This would trigger a global search modal
    console.log('Search clicked')
  }

  const handleSettingsClick = () => {
    togglePanel('settings')
  }

  return (
    <motion.div
      className={cn(
        'flex items-center justify-between h-8 bg-card border-b border-border',
        'select-none drag-region px-2'
      )}
      initial={{ opacity: 0, y: -8 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
    >
      {/* Left Section - Menu and App Info */}
      <div className="flex items-center space-x-2 no-drag">
        {/* Menu Button */}
        <button
          onClick={handleMenuClick}
          className={cn(
            'p-1 rounded hover:bg-accent hover:text-accent-foreground',
            'transition-colors focus:outline-none focus:ring-2 focus:ring-ring',
            panels.sidebar.isVisible && 'bg-accent text-accent-foreground'
          )}
          title="Toggle sidebar (Ctrl+B)"
        >
          <Menu className="w-4 h-4" />
        </button>

        {/* App Icon and Name */}
        <div className="flex items-center space-x-2">
          <Code2 className="w-4 h-4 text-primary" />
          <span className="text-sm font-medium">AI Code Editor</span>
        </div>
      </div>

      {/* Center Section - File Path or Project Name */}
      <div className="flex-1 flex items-center justify-center">
        <div className="text-xs text-muted-foreground max-w-md truncate">
          {/* This would show current file path or project name */}
          Welcome to AI Code Editor
        </div>
      </div>

      {/* Right Section - Controls */}
      <div className="flex items-center space-x-1 no-drag">
        {/* Search Button */}
        <button
          onClick={handleSearchClick}
          className="p-1 rounded hover:bg-accent hover:text-accent-foreground transition-colors focus:outline-none focus:ring-2 focus:ring-ring"
          title="Search (Ctrl+Shift+P)"
        >
          <Search className="w-4 h-4" />
        </button>

        {/* Settings Button */}
        <button
          onClick={handleSettingsClick}
          className={cn(
            'p-1 rounded hover:bg-accent hover:text-accent-foreground',
            'transition-colors focus:outline-none focus:ring-2 focus:ring-ring',
            panels.settings.isVisible && 'bg-accent text-accent-foreground'
          )}
          title="Settings"
        >
          <Settings className="w-4 h-4" />
        </button>

        {/* Theme Toggle */}
        <ThemeToggle size="sm" />

        {/* Window Controls */}
        <div className="flex items-center ml-2 space-x-1">
          {/* Minimize */}
          <button
            onClick={handleMinimize}
            className="p-1 rounded hover:bg-accent hover:text-accent-foreground transition-colors focus:outline-none"
            title="Minimize"
          >
            <Minimize2 className="w-3 h-3" />
          </button>

          {/* Maximize/Restore */}
          <button
            onClick={handleMaximize}
            className="p-1 rounded hover:bg-accent hover:text-accent-foreground transition-colors focus:outline-none"
            title={isFullscreen ? "Restore" : "Maximize"}
          >
            <Maximize2 className="w-3 h-3" />
          </button>

          {/* Close */}
          <button
            onClick={handleClose}
            className="p-1 rounded hover:bg-destructive hover:text-destructive-foreground transition-colors focus:outline-none"
            title="Close"
          >
            <X className="w-3 h-3" />
          </button>
        </div>
      </div>
    </motion.div>
  )
}

// Extend window interface for Electron APIs
declare global {
  interface Window {
    electronAPI?: {
      minimize: () => void
      maximize: () => void
      close: () => void
      getVersion: () => Promise<string>
      getPlatform: () => Promise<string>
    }
  }
}
