/**
 * Layout Store
 * Manages application layout state, panel sizes, and visibility
 */

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { persist } from 'zustand/middleware'

// Panel types
export type PanelType = 'sidebar' | 'editor' | 'terminal' | 'chat' | 'settings'

// Panel position
export type PanelPosition = 'left' | 'right' | 'bottom' | 'top'

// Panel state
export interface PanelState {
  id: string
  type: PanelType
  position: PanelPosition
  size: number // width for vertical panels, height for horizontal panels
  minSize: number
  maxSize: number
  isVisible: boolean
  isCollapsed: boolean
  isResizable: boolean
  order: number
}

// Layout configuration
export interface LayoutConfig {
  panels: Record<string, PanelState>
  activeEditor: string | null
  sidebarWidth: number
  terminalHeight: number
  chatWidth: number
  isFullscreen: boolean
  showStatusBar: boolean
  showTitleBar: boolean
  compactMode: boolean
}

// Layout actions
export interface LayoutActions {
  // Panel management
  togglePanel: (panelId: string) => void
  showPanel: (panelId: string) => void
  hidePanel: (panelId: string) => void
  collapsePanel: (panelId: string) => void
  expandPanel: (panelId: string) => void
  resizePanel: (panelId: string, size: number) => void
  
  // Layout management
  setActiveEditor: (editorId: string | null) => void
  toggleFullscreen: () => void
  toggleStatusBar: () => void
  toggleTitleBar: () => void
  setCompactMode: (enabled: boolean) => void
  
  // Responsive behavior
  adaptToScreenSize: (width: number, height: number) => void
  resetLayout: () => void
  saveLayoutPreset: (name: string) => void
  loadLayoutPreset: (name: string) => void
  
  // Panel ordering
  reorderPanels: (panelIds: string[]) => void
  movePanelToPosition: (panelId: string, position: PanelPosition) => void
}

// Default panel configurations
const defaultPanels: Record<string, PanelState> = {
  sidebar: {
    id: 'sidebar',
    type: 'sidebar',
    position: 'left',
    size: 280,
    minSize: 200,
    maxSize: 500,
    isVisible: true,
    isCollapsed: false,
    isResizable: true,
    order: 0,
  },
  terminal: {
    id: 'terminal',
    type: 'terminal',
    position: 'bottom',
    size: 200,
    minSize: 100,
    maxSize: 400,
    isVisible: false,
    isCollapsed: false,
    isResizable: true,
    order: 0,
  },
  chat: {
    id: 'chat',
    type: 'chat',
    position: 'right',
    size: 350,
    minSize: 250,
    maxSize: 600,
    isVisible: false,
    isCollapsed: false,
    isResizable: true,
    order: 0,
  },
  settings: {
    id: 'settings',
    type: 'settings',
    position: 'right',
    size: 300,
    minSize: 250,
    maxSize: 500,
    isVisible: false,
    isCollapsed: false,
    isResizable: true,
    order: 1,
  },
}

// Default layout configuration
const defaultLayout: LayoutConfig = {
  panels: defaultPanels,
  activeEditor: null,
  sidebarWidth: 280,
  terminalHeight: 200,
  chatWidth: 350,
  isFullscreen: false,
  showStatusBar: true,
  showTitleBar: true,
  compactMode: false,
}

// Breakpoints for responsive behavior
const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280,
}

export type LayoutState = LayoutConfig & LayoutActions

export const useLayoutStore = create<LayoutState>()(
  persist(
    subscribeWithSelector((set, get) => ({
      ...defaultLayout,

      // Panel management
      togglePanel: (panelId: string) => {
        set((state) => ({
          panels: {
            ...state.panels,
            [panelId]: {
              ...state.panels[panelId],
              isVisible: !state.panels[panelId]?.isVisible,
            },
          },
        }))
      },

      showPanel: (panelId: string) => {
        set((state) => ({
          panels: {
            ...state.panels,
            [panelId]: {
              ...state.panels[panelId],
              isVisible: true,
              isCollapsed: false,
            },
          },
        }))
      },

      hidePanel: (panelId: string) => {
        set((state) => ({
          panels: {
            ...state.panels,
            [panelId]: {
              ...state.panels[panelId],
              isVisible: false,
            },
          },
        }))
      },

      collapsePanel: (panelId: string) => {
        set((state) => ({
          panels: {
            ...state.panels,
            [panelId]: {
              ...state.panels[panelId],
              isCollapsed: true,
            },
          },
        }))
      },

      expandPanel: (panelId: string) => {
        set((state) => ({
          panels: {
            ...state.panels,
            [panelId]: {
              ...state.panels[panelId],
              isCollapsed: false,
            },
          },
        }))
      },

      resizePanel: (panelId: string, size: number) => {
        set((state) => {
          const panel = state.panels[panelId]
          if (!panel) return state

          const clampedSize = Math.max(panel.minSize, Math.min(panel.maxSize, size))
          
          return {
            panels: {
              ...state.panels,
              [panelId]: {
                ...panel,
                size: clampedSize,
              },
            },
            // Update convenience properties
            ...(panelId === 'sidebar' && { sidebarWidth: clampedSize }),
            ...(panelId === 'terminal' && { terminalHeight: clampedSize }),
            ...(panelId === 'chat' && { chatWidth: clampedSize }),
          }
        })
      },

      // Layout management
      setActiveEditor: (editorId: string | null) => {
        set({ activeEditor: editorId })
      },

      toggleFullscreen: () => {
        set((state) => ({ isFullscreen: !state.isFullscreen }))
      },

      toggleStatusBar: () => {
        set((state) => ({ showStatusBar: !state.showStatusBar }))
      },

      toggleTitleBar: () => {
        set((state) => ({ showTitleBar: !state.showTitleBar }))
      },

      setCompactMode: (enabled: boolean) => {
        set({ compactMode: enabled })
      },

      // Responsive behavior
      adaptToScreenSize: (width: number, height: number) => {
        set((state) => {
          const updates: Partial<LayoutConfig> = {}

          // Mobile: Hide sidebar and chat, reduce terminal height
          if (width <= BREAKPOINTS.mobile) {
            updates.panels = {
              ...state.panels,
              sidebar: { ...state.panels.sidebar, isVisible: false },
              chat: { ...state.panels.chat, isVisible: false },
              terminal: { ...state.panels.terminal, size: Math.min(state.panels.terminal.size, 150) },
            }
            updates.compactMode = true
          }
          // Tablet: Collapse sidebar, hide chat
          else if (width <= BREAKPOINTS.tablet) {
            updates.panels = {
              ...state.panels,
              sidebar: { ...state.panels.sidebar, isCollapsed: true },
              chat: { ...state.panels.chat, isVisible: false },
            }
            updates.compactMode = true
          }
          // Desktop: Show all panels
          else {
            updates.compactMode = false
          }

          return { ...state, ...updates }
        })
      },

      resetLayout: () => {
        set(defaultLayout)
      },

      saveLayoutPreset: (name: string) => {
        const state = get()
        const preset = {
          panels: state.panels,
          sidebarWidth: state.sidebarWidth,
          terminalHeight: state.terminalHeight,
          chatWidth: state.chatWidth,
          showStatusBar: state.showStatusBar,
          showTitleBar: state.showTitleBar,
          compactMode: state.compactMode,
        }
        localStorage.setItem(`layout-preset-${name}`, JSON.stringify(preset))
      },

      loadLayoutPreset: (name: string) => {
        try {
          const preset = localStorage.getItem(`layout-preset-${name}`)
          if (preset) {
            const parsedPreset = JSON.parse(preset)
            set((state) => ({ ...state, ...parsedPreset }))
          }
        } catch (error) {
          console.error('Failed to load layout preset:', error)
        }
      },

      // Panel ordering
      reorderPanels: (panelIds: string[]) => {
        set((state) => {
          const updatedPanels = { ...state.panels }
          panelIds.forEach((panelId, index) => {
            if (updatedPanels[panelId]) {
              updatedPanels[panelId] = {
                ...updatedPanels[panelId],
                order: index,
              }
            }
          })
          return { panels: updatedPanels }
        })
      },

      movePanelToPosition: (panelId: string, position: PanelPosition) => {
        set((state) => ({
          panels: {
            ...state.panels,
            [panelId]: {
              ...state.panels[panelId],
              position,
            },
          },
        }))
      },
    })),
    {
      name: 'ai-code-editor-layout',
      partialize: (state) => ({
        panels: state.panels,
        sidebarWidth: state.sidebarWidth,
        terminalHeight: state.terminalHeight,
        chatWidth: state.chatWidth,
        showStatusBar: state.showStatusBar,
        showTitleBar: state.showTitleBar,
        compactMode: state.compactMode,
      }),
    }
  )
)
