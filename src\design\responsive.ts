/**
 * Responsive Design System
 * Breakpoints, utilities, and responsive helpers
 */

import { breakpoints } from './tokens'

// Breakpoint values
export const bp = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const

export type Breakpoint = keyof typeof bp

// Media query utilities
export const mediaQueries = {
  sm: `(min-width: ${bp.sm}px)`,
  md: `(min-width: ${bp.md}px)`,
  lg: `(min-width: ${bp.lg}px)`,
  xl: `(min-width: ${bp.xl}px)`,
  '2xl': `(min-width: ${bp['2xl']}px)`,
  
  // Max width queries
  'max-sm': `(max-width: ${bp.sm - 1}px)`,
  'max-md': `(max-width: ${bp.md - 1}px)`,
  'max-lg': `(max-width: ${bp.lg - 1}px)`,
  'max-xl': `(max-width: ${bp.xl - 1}px)`,
  'max-2xl': `(max-width: ${bp['2xl'] - 1}px)`,
  
  // Range queries
  'sm-md': `(min-width: ${bp.sm}px) and (max-width: ${bp.md - 1}px)`,
  'md-lg': `(min-width: ${bp.md}px) and (max-width: ${bp.lg - 1}px)`,
  'lg-xl': `(min-width: ${bp.lg}px) and (max-width: ${bp.xl - 1}px)`,
  'xl-2xl': `(min-width: ${bp.xl}px) and (max-width: ${bp['2xl'] - 1}px)`,
  
  // Special queries
  mobile: `(max-width: ${bp.md - 1}px)`,
  tablet: `(min-width: ${bp.md}px) and (max-width: ${bp.lg - 1}px)`,
  desktop: `(min-width: ${bp.lg}px)`,
  
  // Orientation
  portrait: '(orientation: portrait)',
  landscape: '(orientation: landscape)',
  
  // High DPI
  retina: '(-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)',
  
  // Reduced motion
  'reduced-motion': '(prefers-reduced-motion: reduce)',
  'no-reduced-motion': '(prefers-reduced-motion: no-preference)',
  
  // Color scheme
  'dark-mode': '(prefers-color-scheme: dark)',
  'light-mode': '(prefers-color-scheme: light)',
} as const

// Hook for responsive values
export const useBreakpoint = () => {
  if (typeof window === 'undefined') {
    return {
      isSm: false,
      isMd: false,
      isLg: false,
      isXl: false,
      is2Xl: false,
      isMobile: false,
      isTablet: false,
      isDesktop: false,
      current: 'sm' as Breakpoint,
    }
  }

  const isSm = window.matchMedia(mediaQueries.sm).matches
  const isMd = window.matchMedia(mediaQueries.md).matches
  const isLg = window.matchMedia(mediaQueries.lg).matches
  const isXl = window.matchMedia(mediaQueries.xl).matches
  const is2Xl = window.matchMedia(mediaQueries['2xl']).matches
  
  const isMobile = window.matchMedia(mediaQueries.mobile).matches
  const isTablet = window.matchMedia(mediaQueries.tablet).matches
  const isDesktop = window.matchMedia(mediaQueries.desktop).matches

  let current: Breakpoint = 'sm'
  if (is2Xl) current = '2xl'
  else if (isXl) current = 'xl'
  else if (isLg) current = 'lg'
  else if (isMd) current = 'md'

  return {
    isSm,
    isMd,
    isLg,
    isXl,
    is2Xl,
    isMobile,
    isTablet,
    isDesktop,
    current,
  }
}

// Responsive value utilities
export type ResponsiveValue<T> = T | Partial<Record<Breakpoint, T>>

export const getResponsiveValue = <T>(
  value: ResponsiveValue<T>,
  breakpoint: Breakpoint,
  fallback?: T
): T => {
  if (typeof value === 'object' && value !== null) {
    const responsiveObj = value as Partial<Record<Breakpoint, T>>
    
    // Try exact match first
    if (responsiveObj[breakpoint] !== undefined) {
      return responsiveObj[breakpoint]!
    }
    
    // Fallback to smaller breakpoints
    const breakpointOrder: Breakpoint[] = ['sm', 'md', 'lg', 'xl', '2xl']
    const currentIndex = breakpointOrder.indexOf(breakpoint)
    
    for (let i = currentIndex - 1; i >= 0; i--) {
      const bp = breakpointOrder[i]
      if (responsiveObj[bp] !== undefined) {
        return responsiveObj[bp]!
      }
    }
    
    // Fallback to any available value
    const availableValue = Object.values(responsiveObj).find(v => v !== undefined)
    if (availableValue !== undefined) {
      return availableValue
    }
  } else if (value !== undefined) {
    return value as T
  }
  
  return fallback as T
}

// Container utilities
export const containerSizes = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1400px',
} as const

// Grid system
export const gridCols = {
  1: '1',
  2: '2',
  3: '3',
  4: '4',
  5: '5',
  6: '6',
  7: '7',
  8: '8',
  9: '9',
  10: '10',
  11: '11',
  12: '12',
} as const

// Responsive spacing scale
export const responsiveSpacing = {
  xs: { base: '0.5rem', md: '0.75rem' },
  sm: { base: '1rem', md: '1.25rem' },
  md: { base: '1.5rem', md: '2rem' },
  lg: { base: '2rem', md: '3rem' },
  xl: { base: '3rem', md: '4rem' },
  '2xl': { base: '4rem', md: '6rem' },
} as const

// Responsive typography scale
export const responsiveTypography = {
  xs: { base: '0.75rem', md: '0.75rem' },
  sm: { base: '0.875rem', md: '0.875rem' },
  base: { base: '1rem', md: '1rem' },
  lg: { base: '1.125rem', md: '1.125rem' },
  xl: { base: '1.25rem', md: '1.5rem' },
  '2xl': { base: '1.5rem', md: '1.875rem' },
  '3xl': { base: '1.875rem', md: '2.25rem' },
  '4xl': { base: '2.25rem', md: '3rem' },
  '5xl': { base: '3rem', md: '4rem' },
  '6xl': { base: '3.75rem', md: '5rem' },
} as const

// Layout utilities
export const layoutUtilities = {
  // Flexbox utilities
  flex: {
    center: 'flex items-center justify-center',
    'center-between': 'flex items-center justify-between',
    'center-start': 'flex items-center justify-start',
    'center-end': 'flex items-center justify-end',
    'start-between': 'flex items-start justify-between',
    'end-between': 'flex items-end justify-between',
    col: 'flex flex-col',
    'col-center': 'flex flex-col items-center justify-center',
    'col-start': 'flex flex-col items-start',
    'col-end': 'flex flex-col items-end',
  },
  
  // Grid utilities
  grid: {
    center: 'grid place-items-center',
    'cols-1': 'grid grid-cols-1',
    'cols-2': 'grid grid-cols-2',
    'cols-3': 'grid grid-cols-3',
    'cols-4': 'grid grid-cols-4',
    'cols-auto': 'grid grid-cols-auto',
    responsive: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  },
  
  // Position utilities
  position: {
    'absolute-center': 'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2',
    'absolute-top-left': 'absolute top-0 left-0',
    'absolute-top-right': 'absolute top-0 right-0',
    'absolute-bottom-left': 'absolute bottom-0 left-0',
    'absolute-bottom-right': 'absolute bottom-0 right-0',
    'fixed-center': 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2',
  },
  
  // Size utilities
  size: {
    full: 'w-full h-full',
    screen: 'w-screen h-screen',
    'min-screen': 'min-w-screen min-h-screen',
    square: 'aspect-square',
    video: 'aspect-video',
  },
} as const

// Responsive visibility utilities
export const visibilityUtilities = {
  'show-mobile': 'block md:hidden',
  'show-tablet': 'hidden md:block lg:hidden',
  'show-desktop': 'hidden lg:block',
  'hide-mobile': 'hidden md:block',
  'hide-tablet': 'block md:hidden lg:block',
  'hide-desktop': 'block lg:hidden',
} as const

// CSS-in-JS media query helper
export const mq = (breakpoint: keyof typeof mediaQueries) => `@media ${mediaQueries[breakpoint]}`

// Responsive component props helper
export type ResponsiveProps<T> = {
  [K in keyof T]: ResponsiveValue<T[K]>
}
