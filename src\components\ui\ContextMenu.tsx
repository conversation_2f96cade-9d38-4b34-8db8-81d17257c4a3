/**
 * Context Menu Component
 * Right-click context menu with keyboard shortcuts and smart menu items
 */

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { createPortal } from 'react-dom'
import {
  Co<PERSON>,
  Cut,
  Paste,
  Trash2,
  Edit3,
  FolderPlus,
  FilePlus,
  Download,
  Upload,
  Eye,
  Settings,
  Info,
  RefreshCw,
  GitBranch,
  Terminal,
  ExternalLink,
  Scissors,
  ClipboardCopy,
  ClipboardPaste
} from 'lucide-react'
import { cn } from '../../utils'

export interface ContextMenuItem {
  id: string
  label: string
  icon?: React.ComponentType<any>
  shortcut?: string
  action: () => void
  disabled?: boolean
  danger?: boolean
  separator?: boolean
  submenu?: ContextMenuItem[]
}

export interface ContextMenuProps {
  items: ContextMenuItem[]
  position: { x: number; y: number } | null
  onClose: () => void
  className?: string
}

export interface UseContextMenuOptions {
  onContextMenu?: (event: React.MouseEvent, target: any) => ContextMenuItem[]
}

export const ContextMenu: React.FC<ContextMenuProps> = ({
  items,
  position,
  onClose,
  className
}) => {
  const menuRef = useRef<HTMLDivElement>(null)
  const [submenuPosition, setSubmenuPosition] = useState<{ x: number; y: number } | null>(null)
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null)

  // Close menu on outside click or escape
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (position) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleKeyDown)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [position, onClose])

  // Adjust menu position to stay within viewport
  const getAdjustedPosition = useCallback(() => {
    if (!position || !menuRef.current) return position

    const menu = menuRef.current
    const rect = menu.getBoundingClientRect()
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    }

    let { x, y } = position

    // Adjust horizontal position
    if (x + rect.width > viewport.width) {
      x = viewport.width - rect.width - 10
    }

    // Adjust vertical position
    if (y + rect.height > viewport.height) {
      y = viewport.height - rect.height - 10
    }

    return { x: Math.max(10, x), y: Math.max(10, y) }
  }, [position])

  const handleItemClick = useCallback((item: ContextMenuItem) => {
    if (item.disabled) return

    if (item.submenu) {
      setActiveSubmenu(activeSubmenu === item.id ? null : item.id)
      return
    }

    item.action()
    onClose()
  }, [activeSubmenu, onClose])

  const handleSubmenuHover = useCallback((item: ContextMenuItem, event: React.MouseEvent) => {
    if (!item.submenu) return

    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    setSubmenuPosition({
      x: rect.right + 5,
      y: rect.top
    })
    setActiveSubmenu(item.id)
  }, [])

  if (!position) return null

  const adjustedPosition = getAdjustedPosition()

  return createPortal(
    <AnimatePresence>
      <motion.div
        ref={menuRef}
        initial={{ opacity: 0, scale: 0.95, y: -10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: -10 }}
        transition={{ duration: 0.15 }}
        className={cn(
          'fixed z-50 min-w-48 bg-popover border border-border rounded-md shadow-lg py-1',
          'focus:outline-none',
          className
        )}
        style={{
          left: adjustedPosition?.x,
          top: adjustedPosition?.y
        }}
        role="menu"
        tabIndex={-1}
      >
        {items.map((item, index) => {
          if (item.separator) {
            return (
              <div
                key={`separator-${index}`}
                className="h-px bg-border my-1"
                role="separator"
              />
            )
          }

          const IconComponent = item.icon

          return (
            <motion.div
              key={item.id}
              className={cn(
                'flex items-center justify-between px-3 py-2 text-sm cursor-pointer',
                'hover:bg-accent hover:text-accent-foreground transition-colors',
                item.disabled && 'opacity-50 cursor-not-allowed',
                item.danger && 'text-destructive hover:bg-destructive hover:text-destructive-foreground',
                activeSubmenu === item.id && 'bg-accent text-accent-foreground'
              )}
              onClick={() => handleItemClick(item)}
              onMouseEnter={(e) => handleSubmenuHover(item, e)}
              role="menuitem"
              tabIndex={-1}
            >
              <div className="flex items-center space-x-3">
                {IconComponent && (
                  <IconComponent className="w-4 h-4 flex-shrink-0" />
                )}
                <span className="flex-1">{item.label}</span>
              </div>

              <div className="flex items-center space-x-2">
                {item.shortcut && (
                  <span className="text-xs text-muted-foreground font-mono">
                    {item.shortcut}
                  </span>
                )}
                {item.submenu && (
                  <svg className="w-3 h-3" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z" />
                  </svg>
                )}
              </div>
            </motion.div>
          )
        })}

        {/* Submenu */}
        {activeSubmenu && submenuPosition && (
          <ContextMenu
            items={items.find(item => item.id === activeSubmenu)?.submenu || []}
            position={submenuPosition}
            onClose={() => setActiveSubmenu(null)}
            className="ml-1"
          />
        )}
      </motion.div>
    </AnimatePresence>,
    document.body
  )
}

// Hook for using context menu
export const useContextMenu = (options: UseContextMenuOptions = {}) => {
  const [contextMenu, setContextMenu] = useState<{
    items: ContextMenuItem[]
    position: { x: number; y: number }
  } | null>(null)

  const showContextMenu = useCallback((
    event: React.MouseEvent,
    items: ContextMenuItem[],
    target?: any
  ) => {
    event.preventDefault()
    event.stopPropagation()

    setContextMenu({
      items,
      position: { x: event.clientX, y: event.clientY }
    })
  }, [])

  const hideContextMenu = useCallback(() => {
    setContextMenu(null)
  }, [])

  const handleContextMenu = useCallback((event: React.MouseEvent, target?: any) => {
    if (options.onContextMenu) {
      const items = options.onContextMenu(event, target)
      if (items.length > 0) {
        showContextMenu(event, items, target)
      }
    }
  }, [options.onContextMenu, showContextMenu])

  return {
    contextMenu: contextMenu ? {
      items: contextMenu.items,
      position: contextMenu.position,
      onClose: hideContextMenu
    } : null,
    showContextMenu,
    hideContextMenu,
    handleContextMenu
  }
}

// Predefined context menu items
export const createFileContextMenuItems = (
  fileName: string,
  filePath: string,
  actions: {
    onOpen?: () => void
    onRename?: () => void
    onDelete?: () => void
    onCopy?: () => void
    onCut?: () => void
    onDuplicate?: () => void
    onRevealInExplorer?: () => void
    onCopyPath?: () => void
    onProperties?: () => void
  }
): ContextMenuItem[] => [
  {
    id: 'open',
    label: 'Open',
    icon: Eye,
    action: actions.onOpen || (() => {}),
    disabled: !actions.onOpen
  },
  {
    id: 'separator-1',
    label: '',
    separator: true,
    action: () => {}
  },
  {
    id: 'rename',
    label: 'Rename',
    icon: Edit3,
    shortcut: 'F2',
    action: actions.onRename || (() => {}),
    disabled: !actions.onRename
  },
  {
    id: 'duplicate',
    label: 'Duplicate',
    icon: Copy,
    action: actions.onDuplicate || (() => {}),
    disabled: !actions.onDuplicate
  },
  {
    id: 'separator-2',
    label: '',
    separator: true,
    action: () => {}
  },
  {
    id: 'cut',
    label: 'Cut',
    icon: Scissors,
    shortcut: 'Ctrl+X',
    action: actions.onCut || (() => {}),
    disabled: !actions.onCut
  },
  {
    id: 'copy',
    label: 'Copy',
    icon: ClipboardCopy,
    shortcut: 'Ctrl+C',
    action: actions.onCopy || (() => {}),
    disabled: !actions.onCopy
  },
  {
    id: 'separator-3',
    label: '',
    separator: true,
    action: () => {}
  },
  {
    id: 'delete',
    label: 'Delete',
    icon: Trash2,
    shortcut: 'Del',
    danger: true,
    action: actions.onDelete || (() => {}),
    disabled: !actions.onDelete
  },
  {
    id: 'separator-4',
    label: '',
    separator: true,
    action: () => {}
  },
  {
    id: 'copy-path',
    label: 'Copy Path',
    icon: ClipboardCopy,
    action: actions.onCopyPath || (() => {}),
    disabled: !actions.onCopyPath
  },
  {
    id: 'reveal',
    label: 'Reveal in Explorer',
    icon: ExternalLink,
    action: actions.onRevealInExplorer || (() => {}),
    disabled: !actions.onRevealInExplorer
  },
  {
    id: 'separator-5',
    label: '',
    separator: true,
    action: () => {}
  },
  {
    id: 'properties',
    label: 'Properties',
    icon: Info,
    action: actions.onProperties || (() => {}),
    disabled: !actions.onProperties
  }
]

export const createFolderContextMenuItems = (
  folderName: string,
  folderPath: string,
  actions: {
    onOpen?: () => void
    onNewFile?: () => void
    onNewFolder?: () => void
    onRename?: () => void
    onDelete?: () => void
    onCopy?: () => void
    onCut?: () => void
    onPaste?: () => void
    onRefresh?: () => void
    onRevealInExplorer?: () => void
    onCopyPath?: () => void
    onOpenInTerminal?: () => void
    onProperties?: () => void
  }
): ContextMenuItem[] => [
  {
    id: 'new',
    label: 'New',
    icon: FilePlus,
    submenu: [
      {
        id: 'new-file',
        label: 'File',
        icon: FilePlus,
        action: actions.onNewFile || (() => {}),
        disabled: !actions.onNewFile
      },
      {
        id: 'new-folder',
        label: 'Folder',
        icon: FolderPlus,
        action: actions.onNewFolder || (() => {}),
        disabled: !actions.onNewFolder
      }
    ],
    action: () => {}
  },
  {
    id: 'separator-1',
    label: '',
    separator: true,
    action: () => {}
  },
  {
    id: 'rename',
    label: 'Rename',
    icon: Edit3,
    shortcut: 'F2',
    action: actions.onRename || (() => {}),
    disabled: !actions.onRename
  },
  {
    id: 'separator-2',
    label: '',
    separator: true,
    action: () => {}
  },
  {
    id: 'cut',
    label: 'Cut',
    icon: Scissors,
    shortcut: 'Ctrl+X',
    action: actions.onCut || (() => {}),
    disabled: !actions.onCut
  },
  {
    id: 'copy',
    label: 'Copy',
    icon: ClipboardCopy,
    shortcut: 'Ctrl+C',
    action: actions.onCopy || (() => {}),
    disabled: !actions.onCopy
  },
  {
    id: 'paste',
    label: 'Paste',
    icon: ClipboardPaste,
    shortcut: 'Ctrl+V',
    action: actions.onPaste || (() => {}),
    disabled: !actions.onPaste
  },
  {
    id: 'separator-3',
    label: '',
    separator: true,
    action: () => {}
  },
  {
    id: 'delete',
    label: 'Delete',
    icon: Trash2,
    shortcut: 'Del',
    danger: true,
    action: actions.onDelete || (() => {}),
    disabled: !actions.onDelete
  },
  {
    id: 'separator-4',
    label: '',
    separator: true,
    action: () => {}
  },
  {
    id: 'refresh',
    label: 'Refresh',
    icon: RefreshCw,
    shortcut: 'F5',
    action: actions.onRefresh || (() => {}),
    disabled: !actions.onRefresh
  },
  {
    id: 'copy-path',
    label: 'Copy Path',
    icon: ClipboardCopy,
    action: actions.onCopyPath || (() => {}),
    disabled: !actions.onCopyPath
  },
  {
    id: 'reveal',
    label: 'Reveal in Explorer',
    icon: ExternalLink,
    action: actions.onRevealInExplorer || (() => {}),
    disabled: !actions.onRevealInExplorer
  },
  {
    id: 'terminal',
    label: 'Open in Terminal',
    icon: Terminal,
    action: actions.onOpenInTerminal || (() => {}),
    disabled: !actions.onOpenInTerminal
  },
  {
    id: 'separator-5',
    label: '',
    separator: true,
    action: () => {}
  },
  {
    id: 'properties',
    label: 'Properties',
    icon: Info,
    action: actions.onProperties || (() => {}),
    disabled: !actions.onProperties
  }
]

export default ContextMenu
