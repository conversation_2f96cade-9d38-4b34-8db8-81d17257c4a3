import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { AIModel, AIResponse, TokenUsage } from '../types'

/**
 * AI Store State Interface
 */
interface AIState {
  // Model management
  availableModels: AIModel[]
  currentModel: AIModel | null
  isLoadingModels: boolean
  modelError: string | null

  // Provider management
  availableProviders: string[]
  activeProvider: string | null
  providerConfigurations: Record<string, any>

  // Chat state
  isProcessing: boolean
  lastResponse: AIResponse | null
  responseError: string | null

  // Token usage
  tokenUsage: TokenUsage | null
  isLoadingTokens: boolean
  tokenError: string | null

  // Configuration
  isConfigured: boolean
  apiKeys: Record<string, boolean> // provider -> hasKey

  // Actions
  setAvailableModels: (models: AIModel[]) => void
  setCurrentModel: (model: AIModel | null) => void
  setLoadingModels: (loading: boolean) => void
  setModelError: (error: string | null) => void

  setAvailableProviders: (providers: string[]) => void
  setActiveProvider: (provider: string | null) => void
  setProviderConfiguration: (provider: string, config: any) => void

  setProcessing: (processing: boolean) => void
  setLastResponse: (response: AIResponse | null) => void
  setResponseError: (error: string | null) => void

  setTokenUsage: (usage: TokenUsage | null) => void
  setLoadingTokens: (loading: boolean) => void
  setTokenError: (error: string | null) => void

  setConfigured: (configured: boolean) => void
  setApiKeyStatus: (provider: string, hasKey: boolean) => void

  // Computed getters
  getModelsByProvider: (provider: string) => AIModel[]
  getRecommendedModels: () => AIModel[]
  getCurrentProviderModels: () => AIModel[]

  // Reset functions
  resetState: () => void
  resetErrors: () => void
}

/**
 * Initial state
 */
const initialState = {
  availableModels: [],
  currentModel: null,
  isLoadingModels: false,
  modelError: null,

  availableProviders: [],
  activeProvider: null,
  providerConfigurations: {},

  isProcessing: false,
  lastResponse: null,
  responseError: null,

  tokenUsage: null,
  isLoadingTokens: false,
  tokenError: null,

  isConfigured: false,
  apiKeys: {},
}

/**
 * AI Store using Zustand
 */
export const useAIStore = create<AIState>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    // Model management actions
    setAvailableModels: (models) => set({ availableModels: models }),
    setCurrentModel: (model) => set({ currentModel: model }),
    setLoadingModels: (loading) => set({ isLoadingModels: loading }),
    setModelError: (error) => set({ modelError: error }),

    // Provider management actions
    setAvailableProviders: (providers) => set({ availableProviders: providers }),
    setActiveProvider: (provider) => set({ activeProvider: provider }),
    setProviderConfiguration: (provider, config) => set((state) => ({
      providerConfigurations: {
        ...state.providerConfigurations,
        [provider]: config
      }
    })),

    // Chat actions
    setProcessing: (processing) => set({ isProcessing: processing }),
    setLastResponse: (response) => set({ lastResponse: response }),
    setResponseError: (error) => set({ responseError: error }),

    // Token management actions
    setTokenUsage: (usage) => set({ tokenUsage: usage }),
    setLoadingTokens: (loading) => set({ isLoadingTokens: loading }),
    setTokenError: (error) => set({ tokenError: error }),

    // Configuration actions
    setConfigured: (configured) => set({ isConfigured: configured }),
    setApiKeyStatus: (provider, hasKey) => set((state) => ({
      apiKeys: {
        ...state.apiKeys,
        [provider]: hasKey
      }
    })),

    // Computed getters
    getModelsByProvider: (provider) => {
      const { availableModels } = get()
      return availableModels.filter(model => model.provider === provider)
    },

    getRecommendedModels: () => {
      const { availableModels } = get()
      return availableModels
        .filter(model => model.provider === 'gemini') // Prefer Gemini models
        .sort((a, b) => {
          // Sort by version (newer first), then by type (Pro > Flash)
          const versionCompare = b.version.localeCompare(a.version)
          if (versionCompare !== 0) return versionCompare

          const aIsPro = a.name.toLowerCase().includes('pro')
          const bIsPro = b.name.toLowerCase().includes('pro')
          if (aIsPro && !bIsPro) return -1
          if (!aIsPro && bIsPro) return 1

          return 0
        })
        .slice(0, 5) // Top 5 recommendations
    },

    getCurrentProviderModels: () => {
      const { activeProvider, availableModels } = get()
      if (!activeProvider) return []
      return availableModels.filter(model => model.provider === activeProvider)
    },

    // Reset functions
    resetState: () => set(initialState),
    resetErrors: () => set({
      modelError: null,
      responseError: null,
      tokenError: null
    })
  }))
)

// Store selectors for better performance
export const selectCurrentModel = (state: AIState) => state.currentModel
export const selectAvailableModels = (state: AIState) => state.availableModels
export const selectIsProcessing = (state: AIState) => state.isProcessing
export const selectTokenUsage = (state: AIState) => state.tokenUsage
export const selectIsConfigured = (state: AIState) => state.isConfigured
export const selectActiveProvider = (state: AIState) => state.activeProvider
export const selectLastResponse = (state: AIState) => state.lastResponse

// Helper hooks for common operations
export const useCurrentModel = () => useAIStore(selectCurrentModel)
export const useAvailableModels = () => useAIStore(selectAvailableModels)
export const useIsProcessing = () => useAIStore(selectIsProcessing)
export const useTokenUsage = () => useAIStore(selectTokenUsage)
export const useIsConfigured = () => useAIStore(selectIsConfigured)
export const useActiveProvider = () => useAIStore(selectActiveProvider)
export const useLastResponse = () => useAIStore(selectLastResponse)

// Computed selectors
export const useRecommendedModels = () => useAIStore(state => state.getRecommendedModels())
export const useCurrentProviderModels = () => useAIStore(state => state.getCurrentProviderModels())
export const useModelsByProvider = (provider: string) =>
  useAIStore(state => state.getModelsByProvider(provider))

// Error selectors
export const useAIErrors = () => useAIStore(state => ({
  modelError: state.modelError,
  responseError: state.responseError,
  tokenError: state.tokenError
}))

// Loading state selectors
export const useAILoadingStates = () => useAIStore(state => ({
  isLoadingModels: state.isLoadingModels,
  isProcessing: state.isProcessing,
  isLoadingTokens: state.isLoadingTokens
}))

// Configuration selectors
export const useAIConfiguration = () => useAIStore(state => ({
  isConfigured: state.isConfigured,
  apiKeys: state.apiKeys,
  activeProvider: state.activeProvider,
  currentModel: state.currentModel
}))

// Store actions for external use
export const aiStoreActions = {
  setAvailableModels: (models: AIModel[]) => useAIStore.getState().setAvailableModels(models),
  setCurrentModel: (model: AIModel | null) => useAIStore.getState().setCurrentModel(model),
  setActiveProvider: (provider: string | null) => useAIStore.getState().setActiveProvider(provider),
  setProcessing: (processing: boolean) => useAIStore.getState().setProcessing(processing),
  setTokenUsage: (usage: TokenUsage | null) => useAIStore.getState().setTokenUsage(usage),
  setConfigured: (configured: boolean) => useAIStore.getState().setConfigured(configured),
  resetErrors: () => useAIStore.getState().resetErrors(),
  resetState: () => useAIStore.getState().resetState()
}