import { app, BrowserWindow, ipc<PERSON>ain, shell, dialog } from 'electron'
import { join } from 'node:path'
import { AIHandlers } from './handlers/aiHandlers'
import { SettingsHandlers } from './handlers/settingsHandlers'
import { FileSystemHandlers } from './handlers/fileSystemHandlers'

// Check if the app is running in development mode
const isDev = process.env.NODE_ENV === 'development' || process.env.DEBUG_PROD === 'true'

// Keep a global reference of the window object
let mainWindow: BrowserWindow | null = null

// Initialize handlers
let aiHandlers: AIHandlers | null = null
let settingsHandlers: SettingsHandlers | null = null
let fileSystemHandlers: FileSystemHandlers | null = null

const createWindow = (): void => {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 800,
    minHeight: 600,
    show: false,
    autoHideMenuBar: true,
    titleBarStyle: 'hiddenInset',
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: join(__dirname, '../preload/preload.js'),
      webSecurity: !isDev,
    },
  })

  // Load the app
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000')
    // Open DevTools in development
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show()

    if (isDev) {
      mainWindow?.focus()
    }

    // Set main window reference for file system handlers
    if (fileSystemHandlers && mainWindow) {
      fileSystemHandlers.setMainWindow(mainWindow)
    }
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })
}

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
  // Initialize handlers
  initializeHandlers()

  createWindow()

  // On macOS, re-create window when dock icon is clicked
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    // Cleanup handlers before quitting
    cleanupHandlers()
    app.quit()
  }
})

// Security: Prevent new window creation
app.on('web-contents-created', (_, contents) => {
  contents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })
})

// Handle app protocol for deep linking
if (isDev) {
  if (process.platform === 'win32') {
    process.argv.forEach((arg) => {
      if (arg === '--squirrel-firstrun') {
        // Handle first run after installation
      }
    })
  }
}

// IPC handlers will be added here as we develop features
ipcMain.handle('app:getVersion', () => {
  return app.getVersion()
})

ipcMain.handle('app:getPlatform', () => {
  return process.platform
})

// Dialog handlers
ipcMain.handle('dialog:openFolder', async () => {
  if (!mainWindow) return null

  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory'],
    title: 'Select Project Folder'
  })

  if (result.canceled) {
    return null
  }

  return result.filePaths[0]
})

ipcMain.handle('dialog:saveFile', async (_, defaultPath?: string) => {
  if (!mainWindow) return null

  const result = await dialog.showSaveDialog(mainWindow, {
    title: 'Save File',
    defaultPath,
    filters: [
      { name: 'All Files', extensions: ['*'] },
      { name: 'Text Files', extensions: ['txt'] },
      { name: 'JavaScript', extensions: ['js', 'jsx'] },
      { name: 'TypeScript', extensions: ['ts', 'tsx'] },
      { name: 'JSON', extensions: ['json'] },
      { name: 'Markdown', extensions: ['md'] },
    ]
  })

  if (result.canceled) {
    return null
  }

  return result.filePath
})

// Prevent navigation to external URLs
app.on('web-contents-created', (_, contents) => {
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)
    
    if (parsedUrl.origin !== 'http://localhost:3000' && !isDev) {
      event.preventDefault()
    }
  })
})

// Initialize handlers
function initializeHandlers(): void {
  try {
    // Initialize settings handlers first
    settingsHandlers = new SettingsHandlers()

    // Initialize file system handlers
    fileSystemHandlers = new FileSystemHandlers()

    // Initialize AI handlers with shared settings manager
    aiHandlers = new AIHandlers()

    // Set main window reference for file system handlers
    if (mainWindow && fileSystemHandlers) {
      fileSystemHandlers.setMainWindow(mainWindow)
    }

    console.log('Handlers initialized successfully')
  } catch (error) {
    console.error('Failed to initialize handlers:', error)
  }
}

// Cleanup handlers
function cleanupHandlers(): void {
  try {
    if (fileSystemHandlers) {
      fileSystemHandlers.cleanup()
      fileSystemHandlers = null
    }

    if (aiHandlers) {
      aiHandlers.cleanup()
      aiHandlers = null
    }

    if (settingsHandlers) {
      settingsHandlers.cleanup()
      settingsHandlers = null
    }

    console.log('Handlers cleaned up successfully')
  } catch (error) {
    console.error('Failed to cleanup handlers:', error)
  }
}
