"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AI_CONSTANTS = exports.FILE_CONSTANTS = exports.APP_CONSTANTS = exports.isLinux = exports.isMacOS = exports.isWindows = exports.platform = exports.isProd = exports.isDev = void 0;
/**
 * Check if the app is running in development mode
 */
exports.isDev = process.env.NODE_ENV === 'development' || process.env.DEBUG_PROD === 'true';
/**
 * Check if the app is running in production mode
 */
exports.isProd = !exports.isDev;
/**
 * Get the current platform
 */
exports.platform = process.platform;
/**
 * Check if running on Windows
 */
exports.isWindows = exports.platform === 'win32';
/**
 * Check if running on macOS
 */
exports.isMacOS = exports.platform === 'darwin';
/**
 * Check if running on Linux
 */
exports.isLinux = exports.platform === 'linux';
/**
 * Application constants
 */
exports.APP_CONSTANTS = {
    APP_NAME: 'AI Code Editor',
    APP_VERSION: '1.0.0',
    WINDOW_MIN_WIDTH: 800,
    WINDOW_MIN_HEIGHT: 600,
    WINDOW_DEFAULT_WIDTH: 1400,
    WINDOW_DEFAULT_HEIGHT: 900,
};
/**
 * File system constants
 */
exports.FILE_CONSTANTS = {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    SUPPORTED_LANGUAGES: [
        'javascript',
        'typescript',
        'python',
        'java',
        'csharp',
        'cpp',
        'c',
        'go',
        'rust',
        'php',
        'ruby',
        'swift',
        'kotlin',
        'dart',
        'html',
        'css',
        'scss',
        'sass',
        'less',
        'json',
        'xml',
        'yaml',
        'toml',
        'markdown',
        'sql',
        'shell',
        'powershell',
        'dockerfile',
        'prisma',
    ],
    BINARY_EXTENSIONS: [
        '.exe',
        '.dll',
        '.so',
        '.dylib',
        '.bin',
        '.img',
        '.iso',
        '.dmg',
        '.pkg',
        '.deb',
        '.rpm',
        '.msi',
        '.zip',
        '.rar',
        '.7z',
        '.tar',
        '.gz',
        '.bz2',
        '.xz',
    ],
};
/**
 * AI service constants
 */
exports.AI_CONSTANTS = {
    DEFAULT_MODEL: 'gemini-pro',
    MAX_TOKENS: 4096,
    DEFAULT_TEMPERATURE: 0.7,
    MAX_CONTEXT_LENGTH: 32000,
    RATE_LIMIT_REQUESTS_PER_MINUTE: 60,
};
