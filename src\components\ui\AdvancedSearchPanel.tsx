/**
 * Advanced Search Panel Component
 * Comprehensive search interface with filters, options, and history
 */

import React, { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Search,
  Filter,
  History,
  Star,
  X,
  ChevronDown,
  ChevronUp,
  Settings,
  Clock,
  FileText,
  Folder,
  Calendar,
  HardDrive,
  Regex,
  Globe,
  CaseSensitive,
  WholeWord,
  Eye,
  EyeOff
} from 'lucide-react'
import { Button, Input, Card, CardContent } from '../ui'
import { useAdvancedSearch, SearchOptions, FilterOptions, SearchHistory } from '../../hooks/useAdvancedSearch'
import { cn } from '../../utils'

interface AdvancedSearchPanelProps {
  searchHook: ReturnType<typeof useAdvancedSearch>
  onResultClick?: (filePath: string) => void
  className?: string
}

export const AdvancedSearchPanel: React.FC<AdvancedSearchPanelProps> = ({
  searchHook,
  onResultClick,
  className
}) => {
  const {
    searchOptions,
    filterOptions,
    searchResults,
    isSearching,
    searchHistory,
    searchStats,
    updateSearchOptions,
    updateFilterOptions,
    addToHistory,
    toggleFavorite,
    clearSearch,
    hasResults,
    hasQuery,
    favoriteSearches,
    recentSearches
  } = searchHook

  const [showFilters, setShowFilters] = useState(false)
  const [showHistory, setShowHistory] = useState(false)
  const [showAdvanced, setShowAdvanced] = useState(false)

  // Handle search input change
  const handleSearchChange = useCallback((value: string) => {
    updateSearchOptions({ query: value })
    
    if (value.trim() && hasResults) {
      addToHistory(value, searchOptions, filterOptions, searchResults.length)
    }
  }, [updateSearchOptions, addToHistory, searchOptions, filterOptions, searchResults.length, hasResults])

  // Handle search option toggle
  const handleOptionToggle = useCallback((option: keyof SearchOptions) => {
    updateSearchOptions({ [option]: !searchOptions[option] })
  }, [updateSearchOptions, searchOptions])

  // Handle filter change
  const handleFilterChange = useCallback((filter: keyof FilterOptions, value: any) => {
    updateFilterOptions({ [filter]: value })
  }, [updateFilterOptions])

  // Handle history item click
  const handleHistoryClick = useCallback((historyItem: SearchHistory) => {
    updateSearchOptions(historyItem.options)
    updateFilterOptions(historyItem.filters)
  }, [updateSearchOptions, updateFilterOptions])

  // Render search options
  const renderSearchOptions = () => (
    <div className="flex flex-wrap gap-2 p-3 border-b border-border">
      <Button
        variant={searchOptions.searchType === 'name' ? 'default' : 'outline'}
        size="sm"
        onClick={() => updateSearchOptions({ searchType: 'name' })}
      >
        <FileText className="w-3 h-3 mr-1" />
        Name
      </Button>
      
      <Button
        variant={searchOptions.searchType === 'content' ? 'default' : 'outline'}
        size="sm"
        onClick={() => updateSearchOptions({ searchType: 'content' })}
      >
        <Search className="w-3 h-3 mr-1" />
        Content
      </Button>
      
      <Button
        variant={searchOptions.searchType === 'both' ? 'default' : 'outline'}
        size="sm"
        onClick={() => updateSearchOptions({ searchType: 'both' })}
      >
        Both
      </Button>

      <div className="flex-1" />

      <Button
        variant="ghost"
        size="sm"
        onClick={() => setShowAdvanced(!showAdvanced)}
        className={cn(showAdvanced && 'bg-accent')}
      >
        <Settings className="w-3 h-3" />
      </Button>
    </div>
  )

  // Render advanced options
  const renderAdvancedOptions = () => (
    <AnimatePresence>
      {showAdvanced && (
        <motion.div
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: 'auto', opacity: 1 }}
          exit={{ height: 0, opacity: 0 }}
          className="border-b border-border overflow-hidden"
        >
          <div className="p-3 space-y-3">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={searchOptions.useRegex ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleOptionToggle('useRegex')}
                title="Use Regular Expression"
              >
                <Regex className="w-3 h-3 mr-1" />
                Regex
              </Button>
              
              <Button
                variant={searchOptions.useGlob ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleOptionToggle('useGlob')}
                title="Use Glob Patterns"
              >
                <Globe className="w-3 h-3 mr-1" />
                Glob
              </Button>
              
              <Button
                variant={searchOptions.caseSensitive ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleOptionToggle('caseSensitive')}
                title="Case Sensitive"
              >
                <CaseSensitive className="w-3 h-3 mr-1" />
                Aa
              </Button>
              
              <Button
                variant={searchOptions.wholeWord ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleOptionToggle('wholeWord')}
                title="Whole Word"
              >
                <WholeWord className="w-3 h-3 mr-1" />
                Word
              </Button>
              
              <Button
                variant={searchOptions.includeHidden ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleOptionToggle('includeHidden')}
                title="Include Hidden Files"
              >
                {searchOptions.includeHidden ? <Eye className="w-3 h-3" /> : <EyeOff className="w-3 h-3" />}
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium">Max Results:</label>
              <Input
                type="number"
                value={searchOptions.maxResults}
                onChange={(e) => updateSearchOptions({ maxResults: parseInt(e.target.value) || 100 })}
                className="w-20"
                min="1"
                max="1000"
              />
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )

  // Render filters
  const renderFilters = () => (
    <AnimatePresence>
      {showFilters && (
        <motion.div
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: 'auto', opacity: 1 }}
          exit={{ height: 0, opacity: 0 }}
          className="border-b border-border overflow-hidden"
        >
          <div className="p-3 space-y-3">
            <div>
              <label className="text-sm font-medium mb-2 block">File Types</label>
              <Input
                placeholder="js,ts,tsx,css (comma separated)"
                value={filterOptions.fileTypes.join(',')}
                onChange={(e) => handleFilterChange('fileTypes', e.target.value.split(',').filter(Boolean))}
              />
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Categories</label>
              <div className="flex flex-wrap gap-2">
                {['code', 'markup', 'data', 'config', 'image', 'document'].map(category => (
                  <Button
                    key={category}
                    variant={filterOptions.categories.includes(category) ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => {
                      const newCategories = filterOptions.categories.includes(category)
                        ? filterOptions.categories.filter(c => c !== category)
                        : [...filterOptions.categories, category]
                      handleFilterChange('categories', newCategories)
                    }}
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Exclude Patterns</label>
              <Input
                placeholder="node_modules,*.log,dist (comma separated)"
                value={filterOptions.excludePatterns.join(',')}
                onChange={(e) => handleFilterChange('excludePatterns', e.target.value.split(',').filter(Boolean))}
              />
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )

  // Render search history
  const renderHistory = () => (
    <AnimatePresence>
      {showHistory && (
        <motion.div
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: 'auto', opacity: 1 }}
          exit={{ height: 0, opacity: 0 }}
          className="border-b border-border overflow-hidden"
        >
          <div className="p-3 space-y-2 max-h-48 overflow-y-auto">
            {favoriteSearches.length > 0 && (
              <div>
                <h4 className="text-xs font-medium text-muted-foreground mb-2">Favorites</h4>
                {favoriteSearches.map(item => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between p-2 rounded hover:bg-accent cursor-pointer"
                    onClick={() => handleHistoryClick(item)}
                  >
                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                      <Star className="w-3 h-3 text-yellow-500" />
                      <span className="text-sm truncate">{item.query}</span>
                      <span className="text-xs text-muted-foreground">({item.resultCount})</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon-sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        toggleFavorite(item.id)
                      }}
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}

            {recentSearches.length > 0 && (
              <div>
                <h4 className="text-xs font-medium text-muted-foreground mb-2">Recent</h4>
                {recentSearches.map(item => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between p-2 rounded hover:bg-accent cursor-pointer"
                    onClick={() => handleHistoryClick(item)}
                  >
                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                      <Clock className="w-3 h-3 text-muted-foreground" />
                      <span className="text-sm truncate">{item.query}</span>
                      <span className="text-xs text-muted-foreground">({item.resultCount})</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon-sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        toggleFavorite(item.id)
                      }}
                    >
                      <Star className="w-3 h-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}

            {searchHistory.length === 0 && (
              <div className="text-center text-muted-foreground text-sm py-4">
                No search history
              </div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )

  // Render search results
  const renderResults = () => (
    <div className="flex-1 overflow-auto">
      {isSearching && (
        <div className="flex items-center justify-center p-8">
          <div className="flex items-center space-x-2 text-muted-foreground">
            <Search className="w-4 h-4 animate-pulse" />
            <span>Searching...</span>
          </div>
        </div>
      )}

      {!isSearching && hasQuery && !hasResults && (
        <div className="flex flex-col items-center justify-center p-8 text-muted-foreground">
          <Search className="w-8 h-8 mb-2" />
          <p className="text-sm">No results found</p>
          <p className="text-xs">Try adjusting your search terms or filters</p>
        </div>
      )}

      {!isSearching && hasResults && (
        <div className="p-2">
          <div className="text-xs text-muted-foreground mb-2 px-2">
            {searchResults.length} result{searchResults.length !== 1 ? 's' : ''} 
            {searchStats.searchTime > 0 && ` in ${searchStats.searchTime}ms`}
          </div>
          
          <div className="space-y-1">
            {searchResults.map((result, index) => (
              <motion.div
                key={result.node.path}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.02 }}
                className="p-2 rounded hover:bg-accent cursor-pointer"
                onClick={() => onResultClick?.(result.node.path)}
              >
                <div className="flex items-center space-x-2">
                  {result.node.type === 'folder' ? (
                    <Folder className="w-4 h-4 text-blue-500" />
                  ) : (
                    <FileText className="w-4 h-4 text-muted-foreground" />
                  )}
                  <span className="font-medium text-sm">{result.node.name}</span>
                  <span className="text-xs text-muted-foreground">
                    ({result.matches.length} match{result.matches.length !== 1 ? 'es' : ''})
                  </span>
                </div>
                
                <div className="text-xs text-muted-foreground mt-1 pl-6">
                  {result.node.path}
                </div>

                {result.matches.length > 0 && (
                  <div className="pl-6 mt-1 space-y-1">
                    {result.matches.slice(0, 3).map((match, matchIndex) => (
                      <div key={matchIndex} className="text-xs">
                        <span className="text-muted-foreground">{match.type}:</span>
                        <span className="ml-1 bg-yellow-200 dark:bg-yellow-800 px-1 rounded">
                          {match.text}
                        </span>
                        {match.context && (
                          <span className="ml-1 text-muted-foreground">
                            ...{match.context}...
                          </span>
                        )}
                      </div>
                    ))}
                    {result.matches.length > 3 && (
                      <div className="text-xs text-muted-foreground">
                        +{result.matches.length - 3} more matches
                      </div>
                    )}
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {!hasQuery && (
        <div className="flex flex-col items-center justify-center p-8 text-muted-foreground">
          <Search className="w-8 h-8 mb-2" />
          <p className="text-sm">Start typing to search</p>
          <p className="text-xs">Use filters and options for advanced search</p>
        </div>
      )}
    </div>
  )

  return (
    <Card className={cn('flex flex-col h-full', className)}>
      {/* Search Input */}
      <div className="p-3 border-b border-border">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="Search files and content..."
            value={searchOptions.query}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10 pr-20"
          />
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
            <Button
              variant="ghost"
              size="icon-sm"
              onClick={() => setShowFilters(!showFilters)}
              className={cn(showFilters && 'bg-accent')}
              title="Filters"
            >
              <Filter className="w-3 h-3" />
            </Button>
            <Button
              variant="ghost"
              size="icon-sm"
              onClick={() => setShowHistory(!showHistory)}
              className={cn(showHistory && 'bg-accent')}
              title="History"
            >
              <History className="w-3 h-3" />
            </Button>
            {hasQuery && (
              <Button
                variant="ghost"
                size="icon-sm"
                onClick={clearSearch}
                title="Clear"
              >
                <X className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Search Options */}
      {renderSearchOptions()}

      {/* Advanced Options */}
      {renderAdvancedOptions()}

      {/* Filters */}
      {renderFilters()}

      {/* History */}
      {renderHistory()}

      {/* Results */}
      {renderResults()}
    </Card>
  )
}

export default AdvancedSearchPanel
