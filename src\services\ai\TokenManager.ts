import { TokenUsage } from '../../types'
import { ITokenManager, ISettingsManager } from './interfaces'

/**
 * Token Manager for tracking AI token usage and costs
 */
export class TokenManager implements ITokenManager {
  private settingsManager: ISettingsManager | null = null
  private storageKey = 'ai-token-usage'
  private modelCosts: Map<string, number> = new Map()
  private usageCache: TokenUsageData | null = null
  private lastSaveTime = 0
  private readonly SAVE_INTERVAL = 5000 // Save every 5 seconds

  constructor(settingsManager?: ISettingsManager) {
    this.settingsManager = settingsManager || null
    this.initializeModelCosts()
    this.loadUsageFromStorage()
  }

  // Usage tracking
  async recordUsage(tokens: number, model: string, cost?: number): Promise<void> {
    const usage = await this.getUsageData()
    const now = new Date()
    const today = this.getDateKey(now)
    const month = this.getMonthKey(now)

    // Calculate cost if not provided
    const actualCost = cost ?? await this.calculateCost(tokens, model)

    // Update daily usage
    if (!usage.daily[today]) {
      usage.daily[today] = { tokens: 0, cost: 0, requests: 0 }
    }
    usage.daily[today].tokens += tokens
    usage.daily[today].cost += actualCost
    usage.daily[today].requests += 1

    // Update monthly usage
    if (!usage.monthly[month]) {
      usage.monthly[month] = { tokens: 0, cost: 0, requests: 0 }
    }
    usage.monthly[month].tokens += tokens
    usage.monthly[month].cost += actualCost
    usage.monthly[month].requests += 1

    // Update total usage
    usage.total.tokens += tokens
    usage.total.cost += actualCost
    usage.total.requests += 1

    // Update model-specific usage
    if (!usage.byModel[model]) {
      usage.byModel[model] = { tokens: 0, cost: 0, requests: 0 }
    }
    usage.byModel[model].tokens += tokens
    usage.byModel[model].cost += actualCost
    usage.byModel[model].requests += 1

    // Update last reset time
    usage.lastReset = now

    this.usageCache = usage
    await this.saveUsageToStorage()
  }

  async getUsage(period: 'daily' | 'monthly' | 'total' = 'total'): Promise<TokenUsage> {
    const usage = await this.getUsageData()
    const now = new Date()

    switch (period) {
      case 'daily': {
        const today = this.getDateKey(now)
        const dailyData = usage.daily[today] || { tokens: 0, cost: 0, requests: 0 }
        const limit = await this.getTokenLimit('daily')

        return {
          daily: dailyData.tokens,
          monthly: 0,
          total: usage.total.tokens,
          remaining: Math.max(0, limit - dailyData.tokens),
          cost: dailyData.cost,
          lastReset: usage.lastReset
        }
      }

      case 'monthly': {
        const month = this.getMonthKey(now)
        const monthlyData = usage.monthly[month] || { tokens: 0, cost: 0, requests: 0 }
        const limit = await this.getTokenLimit('monthly')

        return {
          daily: 0,
          monthly: monthlyData.tokens,
          total: usage.total.tokens,
          remaining: Math.max(0, limit - monthlyData.tokens),
          cost: monthlyData.cost,
          lastReset: usage.lastReset
        }
      }

      default: {
        const dailyLimit = await this.getTokenLimit('daily')
        const monthlyLimit = await this.getTokenLimit('monthly')
        const today = this.getDateKey(now)
        const month = this.getMonthKey(now)
        const dailyUsed = usage.daily[today]?.tokens || 0
        const monthlyUsed = usage.monthly[month]?.tokens || 0

        return {
          daily: dailyUsed,
          monthly: monthlyUsed,
          total: usage.total.tokens,
          remaining: Math.min(
            Math.max(0, dailyLimit - dailyUsed),
            Math.max(0, monthlyLimit - monthlyUsed)
          ),
          cost: usage.total.cost,
          lastReset: usage.lastReset
        }
      }
    }
  }

  async resetUsage(period: 'daily' | 'monthly' | 'total' = 'total'): Promise<void> {
    const usage = await this.getUsageData()
    const now = new Date()

    switch (period) {
      case 'daily':
        usage.daily = {}
        break
      case 'monthly':
        usage.monthly = {}
        break
      case 'total':
        usage.daily = {}
        usage.monthly = {}
        usage.total = { tokens: 0, cost: 0, requests: 0 }
        usage.byModel = {}
        break
    }

    usage.lastReset = now
    this.usageCache = usage
    await this.saveUsageToStorage()
  }

  // Limits and warnings
  async setTokenLimit(limit: number, period: 'daily' | 'monthly'): Promise<void> {
    if (this.settingsManager) {
      await this.settingsManager.set(`tokenLimit.${period}`, limit)
    }
  }

  async getTokenLimit(period: 'daily' | 'monthly'): Promise<number> {
    if (this.settingsManager) {
      const limit = await this.settingsManager.get<number>(`tokenLimit.${period}`)
      if (limit !== null) return limit
    }

    // Default limits
    return period === 'daily' ? 100000 : 1000000 // 100K daily, 1M monthly
  }

  async isLimitExceeded(period: 'daily' | 'monthly'): Promise<boolean> {
    const usage = await this.getUsage(period)
    const limit = await this.getTokenLimit(period)

    const currentUsage = period === 'daily' ? usage.daily : usage.monthly
    return currentUsage >= limit
  }

  async getRemainingTokens(period: 'daily' | 'monthly'): Promise<number> {
    const usage = await this.getUsage(period)
    return Math.max(0, usage.remaining)
  }

  // Cost calculation
  async calculateCost(tokens: number, model: string): Promise<number> {
    const costPerToken = this.modelCosts.get(model) || 0.000001 // Default cost
    return tokens * costPerToken
  }

  async getTotalCost(period: 'daily' | 'monthly' | 'total' = 'total'): Promise<number> {
    const usage = await this.getUsageData()
    const now = new Date()

    switch (period) {
      case 'daily': {
        const today = this.getDateKey(now)
        return usage.daily[today]?.cost || 0
      }
      case 'monthly': {
        const month = this.getMonthKey(now)
        return usage.monthly[month]?.cost || 0
      }
      default:
        return usage.total.cost
    }
  }

  // Advanced analytics
  async getUsageAnalytics(): Promise<UsageAnalytics> {
    const usage = await this.getUsageData()
    const now = new Date()

    // Calculate trends
    const last7Days = this.getLast7DaysUsage(usage, now)
    const last30Days = this.getLast30DaysUsage(usage, now)

    // Calculate averages
    const avgDaily = last7Days.reduce((sum, day) => sum + day.tokens, 0) / 7
    const avgMonthly = last30Days.reduce((sum, day) => sum + day.tokens, 0) / 30

    // Find peak usage
    const peakDay = last30Days.reduce((peak, day) =>
      day.tokens > peak.tokens ? day : peak, { date: '', tokens: 0, cost: 0, requests: 0 }
    )

    // Model usage breakdown
    const modelUsage = Object.entries(usage.byModel).map(([model, data]) => ({
      model,
      tokens: data.tokens,
      cost: data.cost,
      requests: data.requests,
      percentage: (data.tokens / usage.total.tokens) * 100
    })).sort((a, b) => b.tokens - a.tokens)

    return {
      totalTokens: usage.total.tokens,
      totalCost: usage.total.cost,
      totalRequests: usage.total.requests,
      averageDailyUsage: avgDaily,
      averageMonthlyUsage: avgMonthly,
      peakUsageDay: peakDay,
      modelBreakdown: modelUsage,
      last7Days,
      last30Days,
      costTrend: this.calculateCostTrend(last30Days),
      usageTrend: this.calculateUsageTrend(last30Days)
    }
  }

  async getModelUsageStats(model: string): Promise<ModelUsageStats> {
    const usage = await this.getUsageData()
    const modelData = usage.byModel[model]

    if (!modelData) {
      return {
        model,
        totalTokens: 0,
        totalCost: 0,
        totalRequests: 0,
        averageTokensPerRequest: 0,
        costPerToken: this.modelCosts.get(model) || 0,
        lastUsed: null
      }
    }

    return {
      model,
      totalTokens: modelData.tokens,
      totalCost: modelData.cost,
      totalRequests: modelData.requests,
      averageTokensPerRequest: modelData.tokens / Math.max(modelData.requests, 1),
      costPerToken: this.modelCosts.get(model) || 0,
      lastUsed: usage.lastReset
    }
  }

  // Utility methods
  private initializeModelCosts(): void {
    // Gemini model costs (per token)
    this.modelCosts.set('gemini-2.5-flash', 0.000001)
    this.modelCosts.set('gemini-2.5-pro', 0.000002)
    this.modelCosts.set('gemini-1.5-flash', 0.0000005)
    this.modelCosts.set('gemini-1.5-pro', 0.000001)

    // Default fallback cost
    this.modelCosts.set('default', 0.000001)
  }

  private async getUsageData(): Promise<TokenUsageData> {
    if (this.usageCache) {
      return this.usageCache
    }

    await this.loadUsageFromStorage()
    return this.usageCache || this.createEmptyUsageData()
  }

  private createEmptyUsageData(): TokenUsageData {
    return {
      daily: {},
      monthly: {},
      total: { tokens: 0, cost: 0, requests: 0 },
      byModel: {},
      lastReset: new Date()
    }
  }

  private getDateKey(date: Date): string {
    return date.toISOString().split('T')[0] // YYYY-MM-DD
  }

  private getMonthKey(date: Date): string {
    return date.toISOString().substring(0, 7) // YYYY-MM
  }

  private getLast7DaysUsage(usage: TokenUsageData, now: Date): DailyUsage[] {
    const result: DailyUsage[] = []

    for (let i = 6; i >= 0; i--) {
      const date = new Date(now)
      date.setDate(date.getDate() - i)
      const dateKey = this.getDateKey(date)
      const dayData = usage.daily[dateKey] || { tokens: 0, cost: 0, requests: 0 }

      result.push({
        date: dateKey,
        tokens: dayData.tokens,
        cost: dayData.cost,
        requests: dayData.requests
      })
    }

    return result
  }

  private getLast30DaysUsage(usage: TokenUsageData, now: Date): DailyUsage[] {
    const result: DailyUsage[] = []

    for (let i = 29; i >= 0; i--) {
      const date = new Date(now)
      date.setDate(date.getDate() - i)
      const dateKey = this.getDateKey(date)
      const dayData = usage.daily[dateKey] || { tokens: 0, cost: 0, requests: 0 }

      result.push({
        date: dateKey,
        tokens: dayData.tokens,
        cost: dayData.cost,
        requests: dayData.requests
      })
    }

    return result
  }

  private calculateCostTrend(dailyUsage: DailyUsage[]): 'increasing' | 'decreasing' | 'stable' {
    if (dailyUsage.length < 7) return 'stable'

    const firstWeek = dailyUsage.slice(0, 7).reduce((sum, day) => sum + day.cost, 0)
    const lastWeek = dailyUsage.slice(-7).reduce((sum, day) => sum + day.cost, 0)

    const change = (lastWeek - firstWeek) / Math.max(firstWeek, 0.01)

    if (change > 0.1) return 'increasing'
    if (change < -0.1) return 'decreasing'
    return 'stable'
  }

  private calculateUsageTrend(dailyUsage: DailyUsage[]): 'increasing' | 'decreasing' | 'stable' {
    if (dailyUsage.length < 7) return 'stable'

    const firstWeek = dailyUsage.slice(0, 7).reduce((sum, day) => sum + day.tokens, 0)
    const lastWeek = dailyUsage.slice(-7).reduce((sum, day) => sum + day.tokens, 0)

    const change = (lastWeek - firstWeek) / Math.max(firstWeek, 1)

    if (change > 0.1) return 'increasing'
    if (change < -0.1) return 'decreasing'
    return 'stable'
  }

  // Storage operations
  private async loadUsageFromStorage(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem(this.storageKey)
        if (stored) {
          const data = JSON.parse(stored)

          // Convert date strings back to Date objects
          data.lastReset = new Date(data.lastReset)

          this.usageCache = data
        }
      }
    } catch (error) {
      console.warn('Failed to load token usage from storage:', error)
      this.usageCache = this.createEmptyUsageData()
    }

    if (!this.usageCache) {
      this.usageCache = this.createEmptyUsageData()
    }
  }

  private async saveUsageToStorage(): Promise<void> {
    // Throttle saves to avoid excessive localStorage writes
    const now = Date.now()
    if (now - this.lastSaveTime < this.SAVE_INTERVAL) {
      return
    }

    try {
      if (typeof window !== 'undefined' && window.localStorage && this.usageCache) {
        localStorage.setItem(this.storageKey, JSON.stringify(this.usageCache))
        this.lastSaveTime = now
      }
    } catch (error) {
      console.warn('Failed to save token usage to storage:', error)
    }
  }

  // Model cost management
  setModelCost(model: string, costPerToken: number): void {
    this.modelCosts.set(model, costPerToken)
  }

  getModelCost(model: string): number {
    return this.modelCosts.get(model) || this.modelCosts.get('default') || 0.000001
  }

  getAllModelCosts(): Record<string, number> {
    const costs: Record<string, number> = {}
    for (const [model, cost] of this.modelCosts) {
      costs[model] = cost
    }
    return costs
  }

  // Warning and notification helpers
  async shouldWarnUser(period: 'daily' | 'monthly'): Promise<boolean> {
    const usage = await this.getUsage(period)
    const limit = await this.getTokenLimit(period)
    const currentUsage = period === 'daily' ? usage.daily : usage.monthly

    // Warn at 80% of limit
    return currentUsage >= limit * 0.8
  }

  async getUsagePercentage(period: 'daily' | 'monthly'): Promise<number> {
    const usage = await this.getUsage(period)
    const limit = await this.getTokenLimit(period)
    const currentUsage = period === 'daily' ? usage.daily : usage.monthly

    return Math.min(100, (currentUsage / limit) * 100)
  }

  // Cleanup and maintenance
  async cleanupOldData(daysToKeep: number = 90): Promise<void> {
    const usage = await this.getUsageData()
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)
    const cutoffKey = this.getDateKey(cutoffDate)

    // Clean up daily data
    for (const dateKey in usage.daily) {
      if (dateKey < cutoffKey) {
        delete usage.daily[dateKey]
      }
    }

    // Clean up monthly data (keep more months)
    const monthCutoff = new Date()
    monthCutoff.setMonth(monthCutoff.getMonth() - 12) // Keep 12 months
    const monthCutoffKey = this.getMonthKey(monthCutoff)

    for (const monthKey in usage.monthly) {
      if (monthKey < monthCutoffKey) {
        delete usage.monthly[monthKey]
      }
    }

    this.usageCache = usage
    await this.saveUsageToStorage()
  }

  // Settings manager setter
  setSettingsManager(settingsManager: ISettingsManager): void {
    this.settingsManager = settingsManager
  }

  // Cleanup
  cleanup(): void {
    this.usageCache = null
    this.modelCosts.clear()
    this.settingsManager = null
  }
}

// Supporting interfaces and types
interface TokenUsageData {
  daily: Record<string, UsageEntry>
  monthly: Record<string, UsageEntry>
  total: UsageEntry
  byModel: Record<string, UsageEntry>
  lastReset: Date
}

interface UsageEntry {
  tokens: number
  cost: number
  requests: number
}

interface DailyUsage {
  date: string
  tokens: number
  cost: number
  requests: number
}

interface UsageAnalytics {
  totalTokens: number
  totalCost: number
  totalRequests: number
  averageDailyUsage: number
  averageMonthlyUsage: number
  peakUsageDay: DailyUsage
  modelBreakdown: ModelUsageBreakdown[]
  last7Days: DailyUsage[]
  last30Days: DailyUsage[]
  costTrend: 'increasing' | 'decreasing' | 'stable'
  usageTrend: 'increasing' | 'decreasing' | 'stable'
}

interface ModelUsageBreakdown {
  model: string
  tokens: number
  cost: number
  requests: number
  percentage: number
}

interface ModelUsageStats {
  model: string
  totalTokens: number
  totalCost: number
  totalRequests: number
  averageTokensPerRequest: number
  costPerToken: number
  lastUsed: Date | null
}