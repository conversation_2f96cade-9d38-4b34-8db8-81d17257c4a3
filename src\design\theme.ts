/**
 * Theme System
 * Light and Dark theme definitions with CSS variables
 */

import { colors } from './tokens'

export type ThemeMode = 'light' | 'dark' | 'auto'

export interface ThemeColors {
  // Base colors
  background: string
  foreground: string
  
  // Card colors
  card: string
  'card-foreground': string
  
  // Popover colors
  popover: string
  'popover-foreground': string
  
  // Primary colors
  primary: string
  'primary-foreground': string
  
  // Secondary colors
  secondary: string
  'secondary-foreground': string
  
  // Muted colors
  muted: string
  'muted-foreground': string
  
  // Accent colors
  accent: string
  'accent-foreground': string
  
  // Destructive colors
  destructive: string
  'destructive-foreground': string
  
  // Border and input
  border: string
  input: string
  ring: string
  
  // Semantic colors
  success: string
  'success-foreground': string
  warning: string
  'warning-foreground': string
  error: string
  'error-foreground': string
  info: string
  'info-foreground': string
  
  // Editor specific
  editor: string
  'editor-foreground': string
  'editor-selection': string
  'editor-line-highlight': string
  
  // Sidebar specific
  sidebar: string
  'sidebar-foreground': string
  'sidebar-border': string
  
  // Terminal specific
  terminal: string
  'terminal-foreground': string
  'terminal-selection': string
}

// Light Theme
export const lightTheme: ThemeColors = {
  background: '0 0% 100%',
  foreground: '222.2 84% 4.9%',
  
  card: '0 0% 100%',
  'card-foreground': '222.2 84% 4.9%',
  
  popover: '0 0% 100%',
  'popover-foreground': '222.2 84% 4.9%',
  
  primary: '221.2 83.2% 53.3%',
  'primary-foreground': '210 40% 98%',
  
  secondary: '210 40% 96%',
  'secondary-foreground': '222.2 84% 4.9%',
  
  muted: '210 40% 96%',
  'muted-foreground': '215.4 16.3% 46.9%',
  
  accent: '210 40% 96%',
  'accent-foreground': '222.2 84% 4.9%',
  
  destructive: '0 84.2% 60.2%',
  'destructive-foreground': '210 40% 98%',
  
  border: '214.3 31.8% 91.4%',
  input: '214.3 31.8% 91.4%',
  ring: '221.2 83.2% 53.3%',
  
  success: '142.1 76.2% 36.3%',
  'success-foreground': '355.7 100% 97.3%',
  warning: '32.6 94.6% 43.7%',
  'warning-foreground': '210 40% 98%',
  error: '0 84.2% 60.2%',
  'error-foreground': '210 40% 98%',
  info: '221.2 83.2% 53.3%',
  'info-foreground': '210 40% 98%',
  
  editor: '0 0% 100%',
  'editor-foreground': '222.2 84% 4.9%',
  'editor-selection': '221.2 83.2% 53.3% / 0.2',
  'editor-line-highlight': '210 40% 96%',
  
  sidebar: '210 40% 98%',
  'sidebar-foreground': '222.2 84% 4.9%',
  'sidebar-border': '214.3 31.8% 91.4%',
  
  terminal: '222.2 84% 4.9%',
  'terminal-foreground': '210 40% 98%',
  'terminal-selection': '221.2 83.2% 53.3% / 0.3',
}

// Dark Theme
export const darkTheme: ThemeColors = {
  background: '222.2 84% 4.9%',
  foreground: '210 40% 98%',
  
  card: '222.2 84% 4.9%',
  'card-foreground': '210 40% 98%',
  
  popover: '222.2 84% 4.9%',
  'popover-foreground': '210 40% 98%',
  
  primary: '217.2 91.2% 59.8%',
  'primary-foreground': '222.2 84% 4.9%',
  
  secondary: '217.2 32.6% 17.5%',
  'secondary-foreground': '210 40% 98%',
  
  muted: '217.2 32.6% 17.5%',
  'muted-foreground': '215 20.2% 65.1%',
  
  accent: '217.2 32.6% 17.5%',
  'accent-foreground': '210 40% 98%',
  
  destructive: '0 62.8% 30.6%',
  'destructive-foreground': '210 40% 98%',
  
  border: '217.2 32.6% 17.5%',
  input: '217.2 32.6% 17.5%',
  ring: '224.3 76.3% 94.1%',
  
  success: '142.1 70.6% 45.3%',
  'success-foreground': '144.9 80.4% 10%',
  warning: '32.6 94.6% 43.7%',
  'warning-foreground': '20.5 90.2% 4.3%',
  error: '0 62.8% 30.6%',
  'error-foreground': '210 40% 98%',
  info: '217.2 91.2% 59.8%',
  'info-foreground': '222.2 84% 4.9%',
  
  editor: '222.2 84% 4.9%',
  'editor-foreground': '210 40% 98%',
  'editor-selection': '217.2 91.2% 59.8% / 0.2',
  'editor-line-highlight': '217.2 32.6% 17.5%',
  
  sidebar: '217.2 32.6% 15.5%',
  'sidebar-foreground': '210 40% 98%',
  'sidebar-border': '217.2 32.6% 17.5%',
  
  terminal: '222.2 84% 4.9%',
  'terminal-foreground': '142.1 70.6% 45.3%',
  'terminal-selection': '217.2 91.2% 59.8% / 0.3',
}

// Theme utilities
export const createThemeVariables = (theme: ThemeColors): Record<string, string> => {
  const variables: Record<string, string> = {}
  
  Object.entries(theme).forEach(([key, value]) => {
    variables[`--${key}`] = value
  })
  
  return variables
}

export const applyTheme = (mode: ThemeMode) => {
  const root = document.documentElement
  const theme = mode === 'dark' ? darkTheme : lightTheme
  const variables = createThemeVariables(theme)
  
  // Remove existing theme classes
  root.classList.remove('light', 'dark')
  
  // Add new theme class
  root.classList.add(mode === 'dark' ? 'dark' : 'light')
  
  // Apply CSS variables
  Object.entries(variables).forEach(([property, value]) => {
    root.style.setProperty(property, value)
  })
}

export const getSystemTheme = (): 'light' | 'dark' => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  }
  return 'light'
}

export const resolveTheme = (mode: ThemeMode): 'light' | 'dark' => {
  if (mode === 'auto') {
    return getSystemTheme()
  }
  return mode
}

// Theme configuration
export const themeConfig = {
  defaultMode: 'auto' as ThemeMode,
  storageKey: 'ai-code-editor-theme',
  attribute: 'data-theme',
  enableSystem: true,
  disableTransitionOnChange: false,
}
