# AI Code Editor

Modern AI-powered code editor built with Electron, React, and TypeScript. Integrated with Gemini 2.5 Flash Pro for intelligent code assistance.

## Features

- 🤖 **AI-Powered Assistance**: Integrated with Gemini 2.5 Flash Pro for intelligent code completion, analysis, and suggestions
- 📁 **Project Management**: Open and manage projects with VS Code-like file explorer
- 🎨 **Modern UI**: Beautiful, responsive interface with dark/light theme support
- 🔧 **Multi-Language Support**: Syntax highlighting for JavaScript, TypeScript, Python, and more
- 💬 **AI Chat**: Real-time chat with AI assistant for coding help
- 🖥️ **Integrated Terminal**: Built-in terminal for command execution
- 🔍 **Advanced Search**: Fuzzy search across files and content
- 🎯 **Live Preview**: Real-time preview for web projects
- 📊 **Code Analysis**: Real-time error detection and code quality metrics
- 🌐 **Web Integration**: Search web documentation and scrape content
- 🎨 **Figma Integration**: Create and analyze UI designs
- ⚡ **Performance Optimized**: Fast loading and smooth animations

## Tech Stack

### Frontend
- **Electron**: Cross-platform desktop framework
- **React 18**: UI library with Suspense and Concurrent Features
- **TypeScript**: Type-safe JavaScript
- **Tailwind CSS**: Utility-first CSS framework
- **Framer Motion**: Animation library
- **Radix UI**: Accessible component primitives
- **Monaco Editor**: VS Code editor component
- **Zustand**: State management
- **React Query**: Server state management

### Backend (Electron Main)
- **Node.js**: Runtime environment
- **Chokidar**: File system watching
- **Electron Store**: Secure data storage

### External Integrations
- **Gemini API**: AI model integration
- **Figma API**: Design tool integration
- **Google Custom Search API**: Web search
- **Puppeteer**: Web scraping

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Git

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/ai-code-editor.git
cd ai-code-editor
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

Add your API keys to the `.env` file:
```
GEMINI_API_KEY=your_gemini_api_key
FIGMA_ACCESS_TOKEN=your_figma_token
GOOGLE_SEARCH_API_KEY=your_google_api_key
```

### Development

Start the development server:
```bash
npm run dev
```

This will start both the Vite dev server for the renderer process and compile the main process.

### Building

Build for production:
```bash
npm run build
```

Create distributable packages:
```bash
npm run dist
```

### Testing

Run tests:
```bash
npm test
```

Run tests in watch mode:
```bash
npm run test:watch
```

## Project Structure

```
ai-code-editor/
├── src/
│   ├── main/           # Electron main process
│   ├── preload/        # Preload scripts
│   ├── renderer/       # React frontend
│   ├── shared/         # Shared utilities
│   ├── services/       # External service integrations
│   ├── components/     # React components
│   ├── types/          # TypeScript type definitions
│   ├── utils/          # Utility functions
│   ├── hooks/          # React hooks
│   ├── stores/         # State management
│   └── assets/         # Static assets
├── tests/              # Test files
├── docs/               # Documentation
└── build/              # Build output
```

## Configuration

### AI Models

Configure AI models in the settings panel:
- Gemini 2.5 Flash Pro (default)
- Custom model endpoints

### Themes

Switch between light and dark themes or create custom themes.

### Keyboard Shortcuts

- `Ctrl+O`: Open project
- `Ctrl+S`: Save file
- `Ctrl+Shift+C`: Open AI chat
- `Ctrl+,`: Open settings
- `Ctrl+Shift+P`: Command palette

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [VS Code](https://code.visualstudio.com/) for inspiration
- [Monaco Editor](https://microsoft.github.io/monaco-editor/) for the editor component
- [Radix UI](https://www.radix-ui.com/) for accessible components
- [Tailwind CSS](https://tailwindcss.com/) for styling
- [Framer Motion](https://www.framer.com/motion/) for animations
