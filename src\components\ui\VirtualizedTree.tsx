/**
 * Virtualized Tree Component
 * High-performance tree view with virtual scrolling for large directories
 */

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react'
import { FixedSizeList as List } from 'react-window'
import { motion, AnimatePresence } from 'framer-motion'
import { FileNode } from '../../types'
import { cn } from '../../utils'
import { DropIndicator } from './DropIndicator'

interface FlattenedNode extends FileNode {
  depth: number
  isExpanded?: boolean
  hasChildren: boolean
  index: number
}

interface VirtualizedTreeProps {
  nodes: FileNode[]
  expandedFolders: Set<string>
  selectedFile: string | null
  onNodeClick: (node: FileNode) => void
  onToggleExpand: (path: string) => void
  renderIcon: (node: FileNode) => React.ReactNode
  height: number
  itemHeight?: number
  className?: string
  // Drag and drop props
  onDragStart?: (event: React.DragEvent, node: FileNode, index: number) => void
  onDragOver?: (event: React.DragEvent, node: FileNode, index: number) => void
  onDrop?: (event: React.DragEvent, node: FileNode, index: number) => void
  onDragEnd?: () => void
  onDragLeave?: (event: React.DragEvent) => void
  isDragging?: boolean
  dragItem?: { node: FileNode } | null
  getDropIndicator?: (nodePath: string) => { position: 'before' | 'after' | 'inside' | null; isValid: boolean } | null
}

interface TreeItemProps {
  index: number
  style: React.CSSProperties
  data: {
    flattenedNodes: FlattenedNode[]
    selectedFile: string | null
    onNodeClick: (node: FileNode) => void
    onToggleExpand: (path: string) => void
    renderIcon: (node: FileNode) => React.ReactNode
    // Drag and drop
    onDragStart?: (event: React.DragEvent, node: FileNode, index: number) => void
    onDragOver?: (event: React.DragEvent, node: FileNode, index: number) => void
    onDrop?: (event: React.DragEvent, node: FileNode, index: number) => void
    onDragEnd?: () => void
    onDragLeave?: (event: React.DragEvent) => void
    isDragging?: boolean
    dragItem?: { node: FileNode } | null
    getDropIndicator?: (nodePath: string) => { position: 'before' | 'after' | 'inside' | null; isValid: boolean } | null
  }
}

const TreeItem: React.FC<TreeItemProps> = ({ index, style, data }) => {
  const {
    flattenedNodes, selectedFile, onNodeClick, onToggleExpand, renderIcon,
    onDragStart, onDragOver, onDrop, onDragEnd, onDragLeave,
    isDragging, dragItem, getDropIndicator
  } = data
  const node = flattenedNodes[index]

  if (!node) return null

  const isSelected = selectedFile === node.path
  const paddingLeft = node.depth * 16 + 8
  const isNodeDragging = dragItem?.node.path === node.path
  const dropIndicator = getDropIndicator?.(node.path)

  return (
    <div style={style}>
      <DropIndicator
        position={dropIndicator?.position || null}
        isValid={dropIndicator?.isValid || false}
      >
        <motion.div
          initial={{ opacity: 0, x: -10 }}
          animate={{
            opacity: isNodeDragging ? 0.5 : 1,
            x: 0,
            scale: isNodeDragging ? 0.95 : 1
          }}
          transition={{ duration: 0.15, delay: index * 0.01 }}
          className={cn(
            'flex items-center space-x-2 py-1 px-2 cursor-pointer group',
            'hover:bg-accent hover:text-accent-foreground transition-colors',
            isSelected && 'bg-accent text-accent-foreground',
            isNodeDragging && 'opacity-50 scale-95'
          )}
          style={{ paddingLeft }}
          draggable={!isNodeDragging}
          onDragStart={(e) => onDragStart?.(e, node, index)}
          onDragOver={(e) => onDragOver?.(e, node, index)}
          onDrop={(e) => onDrop?.(e, node, index)}
          onDragEnd={onDragEnd}
          onDragLeave={onDragLeave}
          onClick={() => onNodeClick(node)}
        >
        {/* Expand/Collapse Button */}
        {node.hasChildren && (
          <button
            className="p-0.5 rounded hover:bg-accent-foreground/10 flex-shrink-0"
            onClick={(e) => {
              e.stopPropagation()
              onToggleExpand(node.path)
            }}
          >
            <motion.div
              animate={{ rotate: node.isExpanded ? 90 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <svg className="w-3 h-3" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z" />
              </svg>
            </motion.div>
          </button>
        )}

        {/* Spacer for files without children */}
        {!node.hasChildren && <div className="w-4 flex-shrink-0" />}

        {/* File/Folder Icon */}
        <div className="flex-shrink-0">
          {renderIcon(node)}
        </div>

        {/* Name */}
        <span className="flex-1 text-sm truncate select-none">
          {node.name}
        </span>

        {/* File Size */}
        {node.type === 'file' && node.size && (
          <span className="text-xs text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity">
            {formatFileSize(node.size)}
          </span>
        )}
      </motion.div>
      </DropIndicator>
    </div>
  )
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

export const VirtualizedTree: React.FC<VirtualizedTreeProps> = ({
  nodes,
  expandedFolders,
  selectedFile,
  onNodeClick,
  onToggleExpand,
  renderIcon,
  height,
  itemHeight = 28,
  className,
  // Drag and drop props
  onDragStart,
  onDragOver,
  onDrop,
  onDragEnd,
  onDragLeave,
  isDragging,
  dragItem,
  getDropIndicator
}) => {
  const listRef = useRef<List>(null)
  const [focusedIndex, setFocusedIndex] = useState<number>(-1)

  // Flatten the tree structure for virtualization
  const flattenedNodes = useMemo(() => {
    const flatten = (nodes: FileNode[], depth = 0): FlattenedNode[] => {
      const result: FlattenedNode[] = []
      
      nodes.forEach((node, index) => {
        const isExpanded = expandedFolders.has(node.path)
        const hasChildren = node.type === 'folder' && node.children && node.children.length > 0
        
        result.push({
          ...node,
          depth,
          isExpanded,
          hasChildren,
          index: result.length
        })

        // Add children if folder is expanded
        if (isExpanded && node.children) {
          result.push(...flatten(node.children, depth + 1))
        }
      })
      
      return result
    }

    return flatten(nodes)
  }, [nodes, expandedFolders])

  // Keyboard navigation
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (flattenedNodes.length === 0) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setFocusedIndex(prev => Math.min(prev + 1, flattenedNodes.length - 1))
        break
      
      case 'ArrowUp':
        e.preventDefault()
        setFocusedIndex(prev => Math.max(prev - 1, 0))
        break
      
      case 'ArrowRight':
        e.preventDefault()
        if (focusedIndex >= 0) {
          const node = flattenedNodes[focusedIndex]
          if (node.hasChildren && !node.isExpanded) {
            onToggleExpand(node.path)
          }
        }
        break
      
      case 'ArrowLeft':
        e.preventDefault()
        if (focusedIndex >= 0) {
          const node = flattenedNodes[focusedIndex]
          if (node.hasChildren && node.isExpanded) {
            onToggleExpand(node.path)
          } else if (node.depth > 0) {
            // Move to parent
            const parentDepth = node.depth - 1
            for (let i = focusedIndex - 1; i >= 0; i--) {
              if (flattenedNodes[i].depth === parentDepth) {
                setFocusedIndex(i)
                break
              }
            }
          }
        }
        break
      
      case 'Enter':
      case ' ':
        e.preventDefault()
        if (focusedIndex >= 0) {
          const node = flattenedNodes[focusedIndex]
          onNodeClick(node)
        }
        break
    }
  }, [flattenedNodes, focusedIndex, onToggleExpand, onNodeClick])

  // Set up keyboard event listeners
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown])

  // Scroll to focused item
  useEffect(() => {
    if (focusedIndex >= 0 && listRef.current) {
      listRef.current.scrollToItem(focusedIndex, 'smart')
    }
  }, [focusedIndex])

  // Auto-focus first item when nodes change
  useEffect(() => {
    if (flattenedNodes.length > 0 && focusedIndex === -1) {
      setFocusedIndex(0)
    }
  }, [flattenedNodes.length, focusedIndex])

  const itemData = {
    flattenedNodes,
    selectedFile,
    onNodeClick,
    onToggleExpand,
    renderIcon,
    // Drag and drop
    onDragStart,
    onDragOver,
    onDrop,
    onDragEnd,
    onDragLeave,
    isDragging,
    dragItem,
    getDropIndicator
  }

  if (flattenedNodes.length === 0) {
    return (
      <div className={cn('flex items-center justify-center h-full text-muted-foreground', className)}>
        <div className="text-center">
          <div className="text-sm">No files to display</div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('focus:outline-none', className)} tabIndex={0}>
      <List
        ref={listRef}
        height={height}
        itemCount={flattenedNodes.length}
        itemSize={itemHeight}
        itemData={itemData}
        overscanCount={5}
      >
        {TreeItem}
      </List>
    </div>
  )
}

export default VirtualizedTree
