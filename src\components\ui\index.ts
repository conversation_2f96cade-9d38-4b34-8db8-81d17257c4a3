/**
 * UI Components Index
 * Centralized exports for all UI components
 */

// Base components
export { Button, buttonVariants } from './Button'
export { Input, inputVariants } from './Input'
export { 
  Card, 
  CardHeader, 
  CardFooter, 
  CardTitle, 
  CardDescription, 
  CardContent,
  cardVariants 
} from './Card'
export { Badge, badgeVariants } from './Badge'

// Provider components
export {
  ThemeProvider,
  useTheme,
  ThemeToggle,
  ThemeSelector
} from '../providers/ThemeProvider'

// Advanced components
export { VirtualizedTree } from './VirtualizedTree'
export { ContextMenu, useContextMenu, createFileContextMenuItems, createFolderContextMenuItems } from './ContextMenu'
export { DropIndicator, DragPreview, DragDropFeedback, DropZone, DragHandle } from './DropIndicator'
export { AdvancedSearchPanel } from './AdvancedSearchPanel'

// Re-export design system
export * from '../../design/tokens'
export * from '../../design/theme'
export * from '../../design/animations'
export * from '../../design/responsive'

// Re-export utilities
export { cn } from '../../utils'
