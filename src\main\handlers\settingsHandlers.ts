import { ipcMain } from 'electron'
import { SettingsManager } from '../../services/settings/SettingsManager'
import { EditorSettings } from '../../services/ai/interfaces'

/**
 * Settings Handlers for IPC communication
 */
export class SettingsHandlers {
  private settingsManager: SettingsManager

  constructor(settingsManager?: SettingsManager) {
    this.settingsManager = settingsManager || new SettingsManager()
    this.registerHandlers()
  }

  private registerHandlers(): void {
    // General settings
    ipcMain.handle('settings:get', this.handleGet.bind(this))
    ipcMain.handle('settings:set', this.handleSet.bind(this))
    ipcMain.handle('settings:delete', this.handleDelete.bind(this))
    ipcMain.handle('settings:getAll', this.handleGetAll.bind(this))
    ipcMain.handle('settings:clear', this.handleClear.bind(this))

    // Theme settings
    ipcMain.handle('settings:getTheme', this.handleGetTheme.bind(this))
    ipcMain.handle('settings:setTheme', this.handleSetTheme.bind(this))

    // Editor settings
    ipcMain.handle('settings:getEditorSettings', this.handleGetEditorSettings.bind(this))
    ipcMain.handle('settings:setEditorSettings', this.handleSetEditorSettings.bind(this))

    // Model settings
    ipcMain.handle('settings:getDefaultModel', this.handleGetDefaultModel.bind(this))
    ipcMain.handle('settings:setDefaultModel', this.handleSetDefaultModel.bind(this))

    // Token settings
    ipcMain.handle('settings:getTokenLimit', this.handleGetTokenLimit.bind(this))
    ipcMain.handle('settings:setTokenLimit', this.handleSetTokenLimit.bind(this))

    // Settings groups
    ipcMain.handle('settings:getGroup', this.handleGetGroup.bind(this))
    ipcMain.handle('settings:setGroup', this.handleSetGroup.bind(this))

    // Import/Export
    ipcMain.handle('settings:export', this.handleExport.bind(this))
    ipcMain.handle('settings:import', this.handleImport.bind(this))

    // Validation
    ipcMain.handle('settings:validate', this.handleValidate.bind(this))

    // Statistics
    ipcMain.handle('settings:getStats', this.handleGetStats.bind(this))
  }

  // General settings handlers
  private async handleGet(_event: any, key: string): Promise<any> {
    return await this.settingsManager.get(key)
  }

  private async handleSet(_event: any, key: string, value: any): Promise<void> {
    await this.settingsManager.set(key, value)
  }

  private async handleDelete(_event: any, key: string): Promise<void> {
    await this.settingsManager.delete(key)
  }

  private async handleGetAll(): Promise<Record<string, any>> {
    return await this.settingsManager.getAll()
  }

  private async handleClear(): Promise<void> {
    await this.settingsManager.clear()
  }

  // Theme settings handlers
  private async handleGetTheme(): Promise<'light' | 'dark' | 'auto'> {
    return await this.settingsManager.getTheme()
  }

  private async handleSetTheme(_event: any, theme: 'light' | 'dark' | 'auto'): Promise<void> {
    await this.settingsManager.setTheme(theme)
  }

  // Editor settings handlers
  private async handleGetEditorSettings(): Promise<EditorSettings> {
    return await this.settingsManager.getEditorSettings()
  }

  private async handleSetEditorSettings(_event: any, settings: Partial<EditorSettings>): Promise<void> {
    await this.settingsManager.setEditorSettings(settings)
  }

  // Model settings handlers
  private async handleGetDefaultModel(): Promise<string | null> {
    return await this.settingsManager.getDefaultModel()
  }

  private async handleSetDefaultModel(_event: any, modelId: string): Promise<void> {
    await this.settingsManager.setDefaultModel(modelId)
  }

  // Token settings handlers
  private async handleGetTokenLimit(): Promise<number> {
    return await this.settingsManager.getTokenLimit()
  }

  private async handleSetTokenLimit(_event: any, limit: number): Promise<void> {
    await this.settingsManager.setTokenLimit(limit)
  }

  // Settings groups handlers
  private async handleGetGroup(_event: any, groupPrefix: string): Promise<Record<string, any>> {
    return await this.settingsManager.getSettingsGroup(groupPrefix)
  }

  private async handleSetGroup(_event: any, groupPrefix: string, settings: Record<string, any>): Promise<void> {
    await this.settingsManager.setSettingsGroup(groupPrefix, settings)
  }

  // Import/Export handlers
  private async handleExport(): Promise<string> {
    return await this.settingsManager.exportSettings()
  }

  private async handleImport(_event: any, settingsJson: string): Promise<void> {
    await this.settingsManager.importSettings(settingsJson)
  }

  // Validation handlers
  private async handleValidate(): Promise<{ isValid: boolean; errors: string[] }> {
    return await this.settingsManager.validateSettings()
  }

  // Statistics handlers
  private async handleGetStats(): Promise<any> {
    return this.settingsManager.getStorageStats()
  }

  // Cleanup method
  cleanup(): void {
    // Remove all IPC handlers
    ipcMain.removeAllListeners('settings:get')
    ipcMain.removeAllListeners('settings:set')
    ipcMain.removeAllListeners('settings:delete')
    ipcMain.removeAllListeners('settings:getAll')
    ipcMain.removeAllListeners('settings:clear')

    ipcMain.removeAllListeners('settings:getTheme')
    ipcMain.removeAllListeners('settings:setTheme')

    ipcMain.removeAllListeners('settings:getEditorSettings')
    ipcMain.removeAllListeners('settings:setEditorSettings')

    ipcMain.removeAllListeners('settings:getDefaultModel')
    ipcMain.removeAllListeners('settings:setDefaultModel')

    ipcMain.removeAllListeners('settings:getTokenLimit')
    ipcMain.removeAllListeners('settings:setTokenLimit')

    ipcMain.removeAllListeners('settings:getGroup')
    ipcMain.removeAllListeners('settings:setGroup')

    ipcMain.removeAllListeners('settings:export')
    ipcMain.removeAllListeners('settings:import')

    ipcMain.removeAllListeners('settings:validate')
    ipcMain.removeAllListeners('settings:getStats')

    // Cleanup settings manager
    this.settingsManager.cleanup()
  }

  // Get settings manager instance for sharing with other handlers
  getSettingsManager(): SettingsManager {
    return this.settingsManager
  }
}