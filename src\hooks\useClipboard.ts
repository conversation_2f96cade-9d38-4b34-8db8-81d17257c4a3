/**
 * Clipboard Operations Hook
 * Handles file/folder copy, cut, paste operations with keyboard shortcuts
 */

import { useState, useCallback, useEffect } from 'react'
import { FileNode } from '../types'

export type ClipboardOperation = 'copy' | 'cut'

export interface ClipboardItem {
  node: FileNode
  operation: ClipboardOperation
  timestamp: number
}

export interface UseClipboardOptions {
  onCopy?: (items: ClipboardItem[]) => void
  onCut?: (items: ClipboardItem[]) => void
  onPaste?: (items: ClipboardItem[], targetPath: string) => Promise<void>
  onClear?: () => void
}

export const useClipboard = (options: UseClipboardOptions = {}) => {
  const [clipboardItems, setClipboardItems] = useState<ClipboardItem[]>([])
  const [isOperationInProgress, setIsOperationInProgress] = useState(false)

  // Copy items to clipboard
  const copyItems = useCallback((nodes: FileNode[]) => {
    const items: ClipboardItem[] = nodes.map(node => ({
      node,
      operation: 'copy',
      timestamp: Date.now()
    }))

    setClipboardItems(items)
    options.onCopy?.(items)

    // Copy paths to system clipboard
    const paths = nodes.map(node => node.path).join('\n')
    if (navigator.clipboard) {
      navigator.clipboard.writeText(paths).catch(console.error)
    }
  }, [options])

  // Cut items to clipboard
  const cutItems = useCallback((nodes: FileNode[]) => {
    const items: ClipboardItem[] = nodes.map(node => ({
      node,
      operation: 'cut',
      timestamp: Date.now()
    }))

    setClipboardItems(items)
    options.onCut?.(items)

    // Copy paths to system clipboard
    const paths = nodes.map(node => node.path).join('\n')
    if (navigator.clipboard) {
      navigator.clipboard.writeText(paths).catch(console.error)
    }
  }, [options])

  // Paste items from clipboard
  const pasteItems = useCallback(async (targetPath: string) => {
    if (clipboardItems.length === 0 || !options.onPaste) return

    setIsOperationInProgress(true)
    try {
      await options.onPaste(clipboardItems, targetPath)
      
      // Clear clipboard if it was a cut operation
      const hasCutItems = clipboardItems.some(item => item.operation === 'cut')
      if (hasCutItems) {
        setClipboardItems([])
      }
    } catch (error) {
      console.error('Paste operation failed:', error)
      throw error
    } finally {
      setIsOperationInProgress(false)
    }
  }, [clipboardItems, options])

  // Clear clipboard
  const clearClipboard = useCallback(() => {
    setClipboardItems([])
    options.onClear?.()
  }, [options])

  // Check if clipboard has items
  const hasItems = clipboardItems.length > 0
  const hasCopyItems = clipboardItems.some(item => item.operation === 'copy')
  const hasCutItems = clipboardItems.some(item => item.operation === 'cut')

  // Get clipboard summary
  const getClipboardSummary = useCallback(() => {
    if (clipboardItems.length === 0) return null

    const fileCount = clipboardItems.filter(item => item.node.type === 'file').length
    const folderCount = clipboardItems.filter(item => item.node.type === 'folder').length
    const operation = clipboardItems[0]?.operation

    return {
      fileCount,
      folderCount,
      totalCount: clipboardItems.length,
      operation,
      items: clipboardItems
    }
  }, [clipboardItems])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle if not in an input field
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement
      ) {
        return
      }

      // Ctrl+C - Copy (handled by parent component)
      // Ctrl+X - Cut (handled by parent component)
      // Ctrl+V - Paste
      if (event.ctrlKey && event.key === 'v') {
        event.preventDefault()
        // This will be handled by the parent component
        // as it needs to know the target path
      }

      // Escape - Clear clipboard
      if (event.key === 'Escape' && hasItems) {
        clearClipboard()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [hasItems, clearClipboard])

  // Auto-clear clipboard after 5 minutes
  useEffect(() => {
    if (clipboardItems.length === 0) return

    const timeout = setTimeout(() => {
      setClipboardItems([])
    }, 5 * 60 * 1000) // 5 minutes

    return () => clearTimeout(timeout)
  }, [clipboardItems])

  return {
    // State
    clipboardItems,
    hasItems,
    hasCopyItems,
    hasCutItems,
    isOperationInProgress,

    // Actions
    copyItems,
    cutItems,
    pasteItems,
    clearClipboard,

    // Utilities
    getClipboardSummary,

    // Helpers
    canPaste: (targetPath: string) => {
      if (clipboardItems.length === 0) return false
      
      // Check if any clipboard item is the same as target or a parent of target
      return !clipboardItems.some(item => 
        targetPath === item.node.path || 
        targetPath.startsWith(item.node.path + '/')
      )
    },

    // Get items by operation
    getCopyItems: () => clipboardItems.filter(item => item.operation === 'copy'),
    getCutItems: () => clipboardItems.filter(item => item.operation === 'cut'),

    // Check if specific node is in clipboard
    isNodeInClipboard: (nodePath: string) => 
      clipboardItems.some(item => item.node.path === nodePath),

    // Get operation for specific node
    getNodeOperation: (nodePath: string) => 
      clipboardItems.find(item => item.node.path === nodePath)?.operation
  }
}

// System clipboard utilities
export const useSystemClipboard = () => {
  const [clipboardText, setClipboardText] = useState<string>('')

  const writeText = useCallback(async (text: string) => {
    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(text)
        setClipboardText(text)
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        setClipboardText(text)
      }
    } catch (error) {
      console.error('Failed to write to clipboard:', error)
      throw error
    }
  }, [])

  const readText = useCallback(async () => {
    try {
      if (navigator.clipboard) {
        const text = await navigator.clipboard.readText()
        setClipboardText(text)
        return text
      }
    } catch (error) {
      console.error('Failed to read from clipboard:', error)
    }
    return ''
  }, [])

  const copyPath = useCallback(async (path: string) => {
    await writeText(path)
  }, [writeText])

  const copyPaths = useCallback(async (paths: string[]) => {
    await writeText(paths.join('\n'))
  }, [writeText])

  return {
    clipboardText,
    writeText,
    readText,
    copyPath,
    copyPaths
  }
}

// File operation utilities
export const createFileOperationHandlers = (
  fileSystemStore: any,
  clipboardHook: ReturnType<typeof useClipboard>,
  systemClipboard: ReturnType<typeof useSystemClipboard>
) => {
  const handleCopy = useCallback((nodes: FileNode[]) => {
    clipboardHook.copyItems(nodes)
    
    // Show toast notification
    const count = nodes.length
    const message = count === 1 
      ? `Copied "${nodes[0].name}"` 
      : `Copied ${count} items`
    
    // TODO: Show toast notification
    console.log(message)
  }, [clipboardHook])

  const handleCut = useCallback((nodes: FileNode[]) => {
    clipboardHook.cutItems(nodes)
    
    // Show toast notification
    const count = nodes.length
    const message = count === 1 
      ? `Cut "${nodes[0].name}"` 
      : `Cut ${count} items`
    
    // TODO: Show toast notification
    console.log(message)
  }, [clipboardHook])

  const handlePaste = useCallback(async (targetPath: string) => {
    if (!clipboardHook.hasItems) return

    try {
      await clipboardHook.pasteItems(targetPath)
      
      // Show success notification
      const summary = clipboardHook.getClipboardSummary()
      const message = summary 
        ? `${summary.operation === 'copy' ? 'Copied' : 'Moved'} ${summary.totalCount} item${summary.totalCount !== 1 ? 's' : ''}`
        : 'Paste completed'
      
      // TODO: Show toast notification
      console.log(message)
    } catch (error) {
      // TODO: Show error notification
      console.error('Paste failed:', error)
    }
  }, [clipboardHook])

  const handleCopyPath = useCallback(async (path: string) => {
    await systemClipboard.copyPath(path)
    
    // TODO: Show toast notification
    console.log(`Copied path: ${path}`)
  }, [systemClipboard])

  const handleCopyPaths = useCallback(async (paths: string[]) => {
    await systemClipboard.copyPaths(paths)
    
    // TODO: Show toast notification
    console.log(`Copied ${paths.length} path${paths.length !== 1 ? 's' : ''}`)
  }, [systemClipboard])

  return {
    handleCopy,
    handleCut,
    handlePaste,
    handleCopyPath,
    handleCopyPaths
  }
}
