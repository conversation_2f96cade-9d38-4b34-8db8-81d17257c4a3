/**
 * File System Store
 * Manages project structure, file operations, and file watching
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { FileNode, ProjectStructure, FileContent, FileEvent } from '../types'

interface FileSystemState {
  // Project state
  currentProject: ProjectStructure | null
  isProjectOpen: boolean
  projectPath: string | null
  
  // File tree state
  fileTree: FileNode[]
  expandedFolders: Set<string>
  selectedFile: string | null
  
  // File operations state
  isLoading: boolean
  error: string | null
  
  // Search state
  searchQuery: string
  searchResults: FileNode[]
  
  // File watching
  watchedFiles: Set<string>
  recentChanges: FileEvent[]
}

interface FileSystemActions {
  // Project operations
  openProject: (projectPath: string) => Promise<void>
  closeProject: () => Promise<void>
  refreshProject: () => Promise<void>
  
  // File tree operations
  expandFolder: (folderPath: string) => void
  collapseFolder: (folderPath: string) => void
  toggleFolder: (folderPath: string) => void
  selectFile: (filePath: string) => void
  
  // File operations
  createFile: (filePath: string, content?: string) => Promise<void>
  createFolder: (folderPath: string) => Promise<void>
  deleteFile: (filePath: string) => Promise<void>
  deleteFolder: (folderPath: string) => Promise<void>
  renameFile: (oldPath: string, newPath: string) => Promise<void>
  copyFile: (sourcePath: string, destinationPath: string) => Promise<void>
  moveFile: (sourcePath: string, destinationPath: string) => Promise<void>
  
  // File content operations
  readFile: (filePath: string) => Promise<FileContent>
  writeFile: (filePath: string, content: string) => Promise<void>
  
  // Search operations
  setSearchQuery: (query: string) => void
  searchFiles: (pattern: string) => Promise<void>
  clearSearch: () => void
  
  // File watching
  startWatching: () => void
  stopWatching: () => void
  handleFileChange: (event: FileEvent) => void
  
  // Utility
  setError: (error: string | null) => void
  setLoading: (loading: boolean) => void
  reset: () => void
}

type FileSystemStore = FileSystemState & FileSystemActions

const initialState: FileSystemState = {
  currentProject: null,
  isProjectOpen: false,
  projectPath: null,
  fileTree: [],
  expandedFolders: new Set(),
  selectedFile: null,
  isLoading: false,
  error: null,
  searchQuery: '',
  searchResults: [],
  watchedFiles: new Set(),
  recentChanges: [],
}

export const useFileSystemStore = create<FileSystemStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Project operations
      openProject: async (projectPath: string) => {
        set({ isLoading: true, error: null })
        try {
          const project = await window.electronAPI.openProject(projectPath)
          
          set({
            currentProject: project,
            isProjectOpen: true,
            projectPath,
            fileTree: project.files,
            isLoading: false,
            expandedFolders: new Set([project.root]), // Expand root by default
          })
          
          // Start file watching
          get().startWatching()
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to open project'
          set({ error: errorMessage, isLoading: false })
          throw error
        }
      },

      closeProject: async () => {
        try {
          await window.electronAPI.closeProject()
          get().stopWatching()
          set({
            currentProject: null,
            isProjectOpen: false,
            projectPath: null,
            fileTree: [],
            expandedFolders: new Set(),
            selectedFile: null,
            searchQuery: '',
            searchResults: [],
            watchedFiles: new Set(),
            recentChanges: [],
          })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to close project'
          set({ error: errorMessage })
          throw error
        }
      },

      refreshProject: async () => {
        const { projectPath } = get()
        if (!projectPath) return
        
        set({ isLoading: true })
        try {
          await window.electronAPI.refreshProject()
          const project = await window.electronAPI.getProjectStructure()
          
          if (project) {
            set({
              currentProject: project,
              fileTree: project.files,
              isLoading: false,
            })
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to refresh project'
          set({ error: errorMessage, isLoading: false })
        }
      },

      // File tree operations
      expandFolder: (folderPath: string) => {
        const { expandedFolders } = get()
        const newExpanded = new Set(expandedFolders)
        newExpanded.add(folderPath)
        set({ expandedFolders: newExpanded })
      },

      collapseFolder: (folderPath: string) => {
        const { expandedFolders } = get()
        const newExpanded = new Set(expandedFolders)
        newExpanded.delete(folderPath)
        set({ expandedFolders: newExpanded })
      },

      toggleFolder: (folderPath: string) => {
        const { expandedFolders } = get()
        if (expandedFolders.has(folderPath)) {
          get().collapseFolder(folderPath)
        } else {
          get().expandFolder(folderPath)
        }
      },

      selectFile: (filePath: string) => {
        set({ selectedFile: filePath })
      },

      // File operations
      createFile: async (filePath: string, content = '') => {
        try {
          await window.electronAPI.createFile(filePath, content)
          await get().refreshProject()
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to create file'
          set({ error: errorMessage })
          throw error
        }
      },

      createFolder: async (folderPath: string) => {
        try {
          await window.electronAPI.createDirectory(folderPath)
          await get().refreshProject()
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to create folder'
          set({ error: errorMessage })
          throw error
        }
      },

      deleteFile: async (filePath: string) => {
        try {
          await window.electronAPI.deleteFile(filePath)
          await get().refreshProject()
          
          // Clear selection if deleted file was selected
          const { selectedFile } = get()
          if (selectedFile === filePath) {
            set({ selectedFile: null })
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to delete file'
          set({ error: errorMessage })
          throw error
        }
      },

      deleteFolder: async (folderPath: string) => {
        try {
          await window.electronAPI.deleteDirectory(folderPath, true)
          await get().refreshProject()
          
          // Remove from expanded folders
          const { expandedFolders } = get()
          const newExpanded = new Set(expandedFolders)
          newExpanded.delete(folderPath)
          set({ expandedFolders: newExpanded })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to delete folder'
          set({ error: errorMessage })
          throw error
        }
      },

      renameFile: async (oldPath: string, newPath: string) => {
        try {
          await window.electronAPI.moveFile(oldPath, newPath)
          await get().refreshProject()
          
          // Update selection if renamed file was selected
          const { selectedFile } = get()
          if (selectedFile === oldPath) {
            set({ selectedFile: newPath })
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to rename file'
          set({ error: errorMessage })
          throw error
        }
      },

      copyFile: async (sourcePath: string, destinationPath: string) => {
        try {
          await window.electronAPI.copyFile(sourcePath, destinationPath)
          await get().refreshProject()
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to copy file'
          set({ error: errorMessage })
          throw error
        }
      },

      moveFile: async (sourcePath: string, destinationPath: string) => {
        try {
          await window.electronAPI.moveFile(sourcePath, destinationPath)
          await get().refreshProject()
          
          // Update selection if moved file was selected
          const { selectedFile } = get()
          if (selectedFile === sourcePath) {
            set({ selectedFile: destinationPath })
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to move file'
          set({ error: errorMessage })
          throw error
        }
      },

      // File content operations
      readFile: async (filePath: string) => {
        try {
          return await window.electronAPI.readFile(filePath)
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to read file'
          set({ error: errorMessage })
          throw error
        }
      },

      writeFile: async (filePath: string, content: string) => {
        try {
          await window.electronAPI.writeFile(filePath, content)
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to write file'
          set({ error: errorMessage })
          throw error
        }
      },

      // Search operations
      setSearchQuery: (query: string) => {
        set({ searchQuery: query })
      },

      searchFiles: async (pattern: string) => {
        try {
          const results = await window.electronAPI.searchFiles(pattern)
          // Convert search results to FileNode format
          const searchResults: FileNode[] = results.map(filePath => ({
            name: filePath.split('/').pop() || '',
            path: filePath,
            type: 'file' as const,
          }))
          set({ searchResults })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to search files'
          set({ error: errorMessage })
        }
      },

      clearSearch: () => {
        set({ searchQuery: '', searchResults: [] })
      },

      // File watching
      startWatching: () => {
        const unwatch = window.electronAPI.watchFiles((event: FileEvent) => {
          get().handleFileChange(event)
        })
        
        // Store unwatch function for cleanup
        set({ watchedFiles: new Set() })
      },

      stopWatching: () => {
        window.electronAPI.stopWatching()
        set({ watchedFiles: new Set() })
      },

      handleFileChange: (event: FileEvent) => {
        const { recentChanges } = get()
        const newChanges = [event, ...recentChanges.slice(0, 99)] // Keep last 100 changes
        set({ recentChanges: newChanges })
        
        // Auto-refresh project on file changes
        setTimeout(() => {
          get().refreshProject()
        }, 100) // Debounce refresh
      },

      // Utility
      setError: (error: string | null) => {
        set({ error })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      reset: () => {
        set(initialState)
      },
    }),
    {
      name: 'file-system-store',
      partialize: (state) => ({
        // Only persist these fields
        expandedFolders: Array.from(state.expandedFolders),
        selectedFile: state.selectedFile,
        projectPath: state.projectPath,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Convert expandedFolders array back to Set
          state.expandedFolders = new Set(state.expandedFolders as any)
          
          // Reopen project if there was one
          if (state.projectPath) {
            state.openProject(state.projectPath).catch(console.error)
          }
        }
      },
    }
  )
)
