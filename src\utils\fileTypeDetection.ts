/**
 * File Type Detection and Icon Mapping
 * Comprehensive file type recognition with language-specific icons
 */

import React from 'react'
import {
  File,
  FileText,
  FileCode,
  FileImage,
  FileVideo,
  FileAudio,
  FileArchive,
  Database,
  Settings,
  Globe,
  Palette,
  Package,
  Lock,
  GitBranch,
  Terminal,
  Cpu,
  Zap,
  Coffee,
  Hash,
  Layers,
  Box,
  Code2,
  Braces,
  FileJson,
  FileSpreadsheet,
  FileX,
  Folder,
  FolderOpen
} from 'lucide-react'

export interface FileTypeInfo {
  extension: string
  language: string
  category: FileCategory
  icon: React.ComponentType<any>
  color: string
  description: string
  isText: boolean
  isBinary: boolean
  canEdit: boolean
  syntax?: string
}

export type FileCategory = 
  | 'code'
  | 'markup'
  | 'data'
  | 'config'
  | 'image'
  | 'video'
  | 'audio'
  | 'archive'
  | 'document'
  | 'font'
  | 'executable'
  | 'unknown'

// Comprehensive file type mappings
export const FILE_TYPE_MAPPINGS: Record<string, FileTypeInfo> = {
  // JavaScript/TypeScript
  'js': {
    extension: 'js',
    language: 'javascript',
    category: 'code',
    icon: FileCode,
    color: '#f7df1e',
    description: 'JavaScript',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'javascript'
  },
  'jsx': {
    extension: 'jsx',
    language: 'javascript',
    category: 'code',
    icon: FileCode,
    color: '#61dafb',
    description: 'React JSX',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'javascript'
  },
  'ts': {
    extension: 'ts',
    language: 'typescript',
    category: 'code',
    icon: FileCode,
    color: '#3178c6',
    description: 'TypeScript',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'typescript'
  },
  'tsx': {
    extension: 'tsx',
    language: 'typescript',
    category: 'code',
    icon: FileCode,
    color: '#3178c6',
    description: 'TypeScript React',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'typescript'
  },

  // Python
  'py': {
    extension: 'py',
    language: 'python',
    category: 'code',
    icon: FileCode,
    color: '#3776ab',
    description: 'Python',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'python'
  },
  'pyw': {
    extension: 'pyw',
    language: 'python',
    category: 'code',
    icon: FileCode,
    color: '#3776ab',
    description: 'Python Windows',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'python'
  },
  'ipynb': {
    extension: 'ipynb',
    language: 'jupyter',
    category: 'data',
    icon: FileJson,
    color: '#ff6f00',
    description: 'Jupyter Notebook',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'json'
  },

  // Java
  'java': {
    extension: 'java',
    language: 'java',
    category: 'code',
    icon: Coffee,
    color: '#ed8b00',
    description: 'Java',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'java'
  },
  'class': {
    extension: 'class',
    language: 'java',
    category: 'executable',
    icon: Package,
    color: '#ed8b00',
    description: 'Java Bytecode',
    isText: false,
    isBinary: true,
    canEdit: false
  },
  'jar': {
    extension: 'jar',
    language: 'java',
    category: 'archive',
    icon: FileArchive,
    color: '#ed8b00',
    description: 'Java Archive',
    isText: false,
    isBinary: true,
    canEdit: false
  },

  // C/C++
  'c': {
    extension: 'c',
    language: 'c',
    category: 'code',
    icon: FileCode,
    color: '#a8b9cc',
    description: 'C',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'c'
  },
  'cpp': {
    extension: 'cpp',
    language: 'cpp',
    category: 'code',
    icon: FileCode,
    color: '#00599c',
    description: 'C++',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'cpp'
  },
  'h': {
    extension: 'h',
    language: 'c',
    category: 'code',
    icon: FileCode,
    color: '#a8b9cc',
    description: 'C Header',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'c'
  },
  'hpp': {
    extension: 'hpp',
    language: 'cpp',
    category: 'code',
    icon: FileCode,
    color: '#00599c',
    description: 'C++ Header',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'cpp'
  },

  // C#
  'cs': {
    extension: 'cs',
    language: 'csharp',
    category: 'code',
    icon: Hash,
    color: '#239120',
    description: 'C#',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'csharp'
  },

  // Go
  'go': {
    extension: 'go',
    language: 'go',
    category: 'code',
    icon: FileCode,
    color: '#00add8',
    description: 'Go',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'go'
  },

  // Rust
  'rs': {
    extension: 'rs',
    language: 'rust',
    category: 'code',
    icon: FileCode,
    color: '#ce422b',
    description: 'Rust',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'rust'
  },

  // PHP
  'php': {
    extension: 'php',
    language: 'php',
    category: 'code',
    icon: FileCode,
    color: '#777bb4',
    description: 'PHP',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'php'
  },

  // Ruby
  'rb': {
    extension: 'rb',
    language: 'ruby',
    category: 'code',
    icon: FileCode,
    color: '#cc342d',
    description: 'Ruby',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'ruby'
  },

  // Swift
  'swift': {
    extension: 'swift',
    language: 'swift',
    category: 'code',
    icon: FileCode,
    color: '#fa7343',
    description: 'Swift',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'swift'
  },

  // Kotlin
  'kt': {
    extension: 'kt',
    language: 'kotlin',
    category: 'code',
    icon: FileCode,
    color: '#7f52ff',
    description: 'Kotlin',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'kotlin'
  },

  // Dart
  'dart': {
    extension: 'dart',
    language: 'dart',
    category: 'code',
    icon: FileCode,
    color: '#0175c2',
    description: 'Dart',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'dart'
  },

  // Web Technologies
  'html': {
    extension: 'html',
    language: 'html',
    category: 'markup',
    icon: Globe,
    color: '#e34f26',
    description: 'HTML',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'html'
  },
  'htm': {
    extension: 'htm',
    language: 'html',
    category: 'markup',
    icon: Globe,
    color: '#e34f26',
    description: 'HTML',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'html'
  },
  'css': {
    extension: 'css',
    language: 'css',
    category: 'markup',
    icon: Palette,
    color: '#1572b6',
    description: 'CSS',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'css'
  },
  'scss': {
    extension: 'scss',
    language: 'scss',
    category: 'markup',
    icon: Palette,
    color: '#cf649a',
    description: 'Sass',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'scss'
  },
  'sass': {
    extension: 'sass',
    language: 'sass',
    category: 'markup',
    icon: Palette,
    color: '#cf649a',
    description: 'Sass',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'sass'
  },
  'less': {
    extension: 'less',
    language: 'less',
    category: 'markup',
    icon: Palette,
    color: '#1d365d',
    description: 'Less',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'less'
  },

  // Data formats
  'json': {
    extension: 'json',
    language: 'json',
    category: 'data',
    icon: FileJson,
    color: '#ffd500',
    description: 'JSON',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'json'
  },
  'xml': {
    extension: 'xml',
    language: 'xml',
    category: 'data',
    icon: FileCode,
    color: '#ff6600',
    description: 'XML',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'xml'
  },
  'yaml': {
    extension: 'yaml',
    language: 'yaml',
    category: 'data',
    icon: FileText,
    color: '#cb171e',
    description: 'YAML',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'yaml'
  },
  'yml': {
    extension: 'yml',
    language: 'yaml',
    category: 'data',
    icon: FileText,
    color: '#cb171e',
    description: 'YAML',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'yaml'
  },
  'toml': {
    extension: 'toml',
    language: 'toml',
    category: 'config',
    icon: Settings,
    color: '#9c4221',
    description: 'TOML',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'toml'
  },
  'csv': {
    extension: 'csv',
    language: 'csv',
    category: 'data',
    icon: FileSpreadsheet,
    color: '#207245',
    description: 'CSV',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'csv'
  },

  // Configuration files
  'env': {
    extension: 'env',
    language: 'dotenv',
    category: 'config',
    icon: Settings,
    color: '#ecd53f',
    description: 'Environment',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'dotenv'
  },
  'gitignore': {
    extension: 'gitignore',
    language: 'gitignore',
    category: 'config',
    icon: GitBranch,
    color: '#f05032',
    description: 'Git Ignore',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'gitignore'
  },
  'dockerfile': {
    extension: 'dockerfile',
    language: 'dockerfile',
    category: 'config',
    icon: Box,
    color: '#2496ed',
    description: 'Dockerfile',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'dockerfile'
  },

  // Package managers
  'package.json': {
    extension: 'package.json',
    language: 'json',
    category: 'config',
    icon: Package,
    color: '#cb3837',
    description: 'NPM Package',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'json'
  },
  'package-lock.json': {
    extension: 'package-lock.json',
    language: 'json',
    category: 'config',
    icon: Lock,
    color: '#cb3837',
    description: 'NPM Lock',
    isText: true,
    isBinary: false,
    canEdit: false,
    syntax: 'json'
  },
  'yarn.lock': {
    extension: 'yarn.lock',
    language: 'yaml',
    category: 'config',
    icon: Lock,
    color: '#2c8ebb',
    description: 'Yarn Lock',
    isText: true,
    isBinary: false,
    canEdit: false,
    syntax: 'yaml'
  },
  'requirements.txt': {
    extension: 'requirements.txt',
    language: 'plaintext',
    category: 'config',
    icon: Package,
    color: '#3776ab',
    description: 'Python Requirements',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'plaintext'
  },
  'cargo.toml': {
    extension: 'cargo.toml',
    language: 'toml',
    category: 'config',
    icon: Package,
    color: '#ce422b',
    description: 'Cargo Package',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'toml'
  },

  // Images
  'png': {
    extension: 'png',
    language: 'image',
    category: 'image',
    icon: FileImage,
    color: '#ff6b6b',
    description: 'PNG Image',
    isText: false,
    isBinary: true,
    canEdit: false
  },
  'jpg': {
    extension: 'jpg',
    language: 'image',
    category: 'image',
    icon: FileImage,
    color: '#ff6b6b',
    description: 'JPEG Image',
    isText: false,
    isBinary: true,
    canEdit: false
  },
  'jpeg': {
    extension: 'jpeg',
    language: 'image',
    category: 'image',
    icon: FileImage,
    color: '#ff6b6b',
    description: 'JPEG Image',
    isText: false,
    isBinary: true,
    canEdit: false
  },
  'gif': {
    extension: 'gif',
    language: 'image',
    category: 'image',
    icon: FileImage,
    color: '#ff6b6b',
    description: 'GIF Image',
    isText: false,
    isBinary: true,
    canEdit: false
  },
  'svg': {
    extension: 'svg',
    language: 'svg',
    category: 'image',
    icon: FileImage,
    color: '#ffb347',
    description: 'SVG Vector',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'xml'
  },
  'webp': {
    extension: 'webp',
    language: 'image',
    category: 'image',
    icon: FileImage,
    color: '#ff6b6b',
    description: 'WebP Image',
    isText: false,
    isBinary: true,
    canEdit: false
  },
  'ico': {
    extension: 'ico',
    language: 'image',
    category: 'image',
    icon: FileImage,
    color: '#ff6b6b',
    description: 'Icon',
    isText: false,
    isBinary: true,
    canEdit: false
  },

  // Documents
  'md': {
    extension: 'md',
    language: 'markdown',
    category: 'document',
    icon: FileText,
    color: '#083fa1',
    description: 'Markdown',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'markdown'
  },
  'txt': {
    extension: 'txt',
    language: 'plaintext',
    category: 'document',
    icon: FileText,
    color: '#6b7280',
    description: 'Text',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'plaintext'
  },
  'pdf': {
    extension: 'pdf',
    language: 'pdf',
    category: 'document',
    icon: FileText,
    color: '#dc2626',
    description: 'PDF',
    isText: false,
    isBinary: true,
    canEdit: false
  },

  // Archives
  'zip': {
    extension: 'zip',
    language: 'archive',
    category: 'archive',
    icon: FileArchive,
    color: '#fbbf24',
    description: 'ZIP Archive',
    isText: false,
    isBinary: true,
    canEdit: false
  },
  'tar': {
    extension: 'tar',
    language: 'archive',
    category: 'archive',
    icon: FileArchive,
    color: '#fbbf24',
    description: 'TAR Archive',
    isText: false,
    isBinary: true,
    canEdit: false
  },
  'gz': {
    extension: 'gz',
    language: 'archive',
    category: 'archive',
    icon: FileArchive,
    color: '#fbbf24',
    description: 'Gzip Archive',
    isText: false,
    isBinary: true,
    canEdit: false
  },
  'rar': {
    extension: 'rar',
    language: 'archive',
    category: 'archive',
    icon: FileArchive,
    color: '#fbbf24',
    description: 'RAR Archive',
    isText: false,
    isBinary: true,
    canEdit: false
  },

  // Executables
  'exe': {
    extension: 'exe',
    language: 'executable',
    category: 'executable',
    icon: Cpu,
    color: '#374151',
    description: 'Executable',
    isText: false,
    isBinary: true,
    canEdit: false
  },
  'dll': {
    extension: 'dll',
    language: 'executable',
    category: 'executable',
    icon: Cpu,
    color: '#374151',
    description: 'Dynamic Library',
    isText: false,
    isBinary: true,
    canEdit: false
  },
  'so': {
    extension: 'so',
    language: 'executable',
    category: 'executable',
    icon: Cpu,
    color: '#374151',
    description: 'Shared Object',
    isText: false,
    isBinary: true,
    canEdit: false
  },

  // Shell scripts
  'sh': {
    extension: 'sh',
    language: 'shell',
    category: 'code',
    icon: Terminal,
    color: '#4ade80',
    description: 'Shell Script',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'shell'
  },
  'bash': {
    extension: 'bash',
    language: 'bash',
    category: 'code',
    icon: Terminal,
    color: '#4ade80',
    description: 'Bash Script',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'bash'
  },
  'ps1': {
    extension: 'ps1',
    language: 'powershell',
    category: 'code',
    icon: Terminal,
    color: '#012456',
    description: 'PowerShell',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'powershell'
  },
  'bat': {
    extension: 'bat',
    language: 'batch',
    category: 'code',
    icon: Terminal,
    color: '#4ade80',
    description: 'Batch File',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'batch'
  },

  // Database
  'sql': {
    extension: 'sql',
    language: 'sql',
    category: 'code',
    icon: Database,
    color: '#336791',
    description: 'SQL',
    isText: true,
    isBinary: false,
    canEdit: true,
    syntax: 'sql'
  },
  'db': {
    extension: 'db',
    language: 'database',
    category: 'data',
    icon: Database,
    color: '#336791',
    description: 'Database',
    isText: false,
    isBinary: true,
    canEdit: false
  },
  'sqlite': {
    extension: 'sqlite',
    language: 'database',
    category: 'data',
    icon: Database,
    color: '#003b57',
    description: 'SQLite Database',
    isText: false,
    isBinary: true,
    canEdit: false
  }
}

// Default file type for unknown extensions
export const DEFAULT_FILE_TYPE: FileTypeInfo = {
  extension: 'unknown',
  language: 'plaintext',
  category: 'unknown',
  icon: File,
  color: '#6b7280',
  description: 'Unknown File',
  isText: true,
  isBinary: false,
  canEdit: true,
  syntax: 'plaintext'
}

// Folder type info
export const FOLDER_TYPE: FileTypeInfo = {
  extension: 'folder',
  language: 'folder',
  category: 'unknown',
  icon: Folder,
  color: '#3b82f6',
  description: 'Folder',
  isText: false,
  isBinary: false,
  canEdit: false
}

export const FOLDER_OPEN_TYPE: FileTypeInfo = {
  extension: 'folder-open',
  language: 'folder',
  category: 'unknown',
  icon: FolderOpen,
  color: '#3b82f6',
  description: 'Open Folder',
  isText: false,
  isBinary: false,
  canEdit: false
}

/**
 * Get file type information from file extension
 */
export function getFileTypeInfo(fileName: string): FileTypeInfo {
  if (!fileName) return DEFAULT_FILE_TYPE

  // Handle special files by full name
  const lowerFileName = fileName.toLowerCase()

  // Special file names
  const specialFiles: Record<string, string> = {
    'package.json': 'package.json',
    'package-lock.json': 'package-lock.json',
    'yarn.lock': 'yarn.lock',
    'requirements.txt': 'requirements.txt',
    'cargo.toml': 'cargo.toml',
    'dockerfile': 'dockerfile',
    '.gitignore': 'gitignore',
    '.env': 'env',
    '.env.local': 'env',
    '.env.development': 'env',
    '.env.production': 'env',
    'readme.md': 'md',
    'license': 'txt',
    'changelog.md': 'md',
    'makefile': 'sh'
  }

  if (specialFiles[lowerFileName]) {
    const mapping = FILE_TYPE_MAPPINGS[specialFiles[lowerFileName]]
    if (mapping) return mapping
  }

  // Extract extension
  const lastDotIndex = fileName.lastIndexOf('.')
  if (lastDotIndex === -1 || lastDotIndex === fileName.length - 1) {
    return DEFAULT_FILE_TYPE
  }

  const extension = fileName.substring(lastDotIndex + 1).toLowerCase()
  return FILE_TYPE_MAPPINGS[extension] || DEFAULT_FILE_TYPE
}

/**
 * Get file icon component
 */
export function getFileIcon(fileName: string, isFolder = false, isOpen = false): React.ComponentType<any> {
  if (isFolder) {
    return isOpen ? FolderOpen : Folder
  }

  const fileType = getFileTypeInfo(fileName)
  return fileType.icon
}

/**
 * Get file color
 */
export function getFileColor(fileName: string, isFolder = false): string {
  if (isFolder) {
    return '#3b82f6'
  }

  const fileType = getFileTypeInfo(fileName)
  return fileType.color
}

/**
 * Check if file is editable
 */
export function isFileEditable(fileName: string): boolean {
  const fileType = getFileTypeInfo(fileName)
  return fileType.canEdit
}

/**
 * Check if file is binary
 */
export function isFileBinary(fileName: string): boolean {
  const fileType = getFileTypeInfo(fileName)
  return fileType.isBinary
}

/**
 * Get syntax highlighting language
 */
export function getSyntaxLanguage(fileName: string): string {
  const fileType = getFileTypeInfo(fileName)
  return fileType.syntax || fileType.language
}

/**
 * Get file category
 */
export function getFileCategory(fileName: string): FileCategory {
  const fileType = getFileTypeInfo(fileName)
  return fileType.category
}

/**
 * Get all supported extensions for a category
 */
export function getExtensionsByCategory(category: FileCategory): string[] {
  return Object.entries(FILE_TYPE_MAPPINGS)
    .filter(([_, info]) => info.category === category)
    .map(([ext, _]) => ext)
}

/**
 * Get all supported programming languages
 */
export function getSupportedLanguages(): string[] {
  const languages = new Set<string>()
  Object.values(FILE_TYPE_MAPPINGS).forEach(info => {
    if (info.category === 'code') {
      languages.add(info.language)
    }
  })
  return Array.from(languages).sort()
}

/**
 * Check if file type is supported for syntax highlighting
 */
export function hasSyntaxHighlighting(fileName: string): boolean {
  const fileType = getFileTypeInfo(fileName)
  return Boolean(fileType.syntax && fileType.isText)
}

/**
 * Get file type statistics
 */
export function getFileTypeStats() {
  const stats = {
    total: Object.keys(FILE_TYPE_MAPPINGS).length,
    byCategory: {} as Record<FileCategory, number>,
    languages: getSupportedLanguages().length,
    editable: 0,
    binary: 0
  }

  Object.values(FILE_TYPE_MAPPINGS).forEach(info => {
    stats.byCategory[info.category] = (stats.byCategory[info.category] || 0) + 1
    if (info.canEdit) stats.editable++
    if (info.isBinary) stats.binary++
  })

  return stats
}

/**
 * Search file types by name or description
 */
export function searchFileTypes(query: string): FileTypeInfo[] {
  const lowerQuery = query.toLowerCase()
  return Object.values(FILE_TYPE_MAPPINGS).filter(info =>
    info.extension.toLowerCase().includes(lowerQuery) ||
    info.language.toLowerCase().includes(lowerQuery) ||
    info.description.toLowerCase().includes(lowerQuery)
  )
}
