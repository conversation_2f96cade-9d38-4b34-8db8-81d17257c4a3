@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme variables */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --success: 142.1 76.2% 36.3%;
    --success-foreground: 355.7 100% 97.3%;
    --warning: 32.6 94.6% 43.7%;
    --warning-foreground: 210 40% 98%;
    --error: 0 84.2% 60.2%;
    --error-foreground: 210 40% 98%;
    --info: 221.2 83.2% 53.3%;
    --info-foreground: 210 40% 98%;
    --editor: 0 0% 100%;
    --editor-foreground: 222.2 84% 4.9%;
    --editor-selection: 221.2 83.2% 53.3% / 0.2;
    --editor-line-highlight: 210 40% 96%;
    --sidebar: 210 40% 98%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --terminal: 222.2 84% 4.9%;
    --terminal-foreground: 210 40% 98%;
    --terminal-selection: 221.2 83.2% 53.3% / 0.3;
    --radius: 0.5rem;
  }

  .dark {
    /* Dark theme variables */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --success: 142.1 70.6% 45.3%;
    --success-foreground: 144.9 80.4% 10%;
    --warning: 32.6 94.6% 43.7%;
    --warning-foreground: 20.5 90.2% 4.3%;
    --error: 0 62.8% 30.6%;
    --error-foreground: 210 40% 98%;
    --info: 217.2 91.2% 59.8%;
    --info-foreground: 222.2 84% 4.9%;
    --editor: 222.2 84% 4.9%;
    --editor-foreground: 210 40% 98%;
    --editor-selection: 217.2 91.2% 59.8% / 0.2;
    --editor-line-highlight: 217.2 32.6% 17.5%;
    --sidebar: 217.2 32.6% 15.5%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --terminal: 222.2 84% 4.9%;
    --terminal-foreground: 142.1 70.6% 45.3%;
    --terminal-selection: 217.2 91.2% 59.8% / 0.3;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted/20;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-md;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
  
  /* Selection styles */
  ::selection {
    @apply bg-primary/20;
  }
  
  /* Focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }
}

@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  
  .btn-default {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }
  
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }
  
  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground;
  }
  
  .btn-outline {
    @apply border border-input hover:bg-accent hover:text-accent-foreground;
  }
  
  /* Input styles */
  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  /* Card styles */
  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }
  
  /* Panel styles */
  .panel {
    @apply bg-card border-r border-border;
  }
  
  .panel-header {
    @apply flex items-center justify-between p-4 border-b border-border;
  }
  
  .panel-content {
    @apply p-4;
  }
  
  /* Editor styles */
  .editor-container {
    @apply bg-editor text-editor-foreground;
  }
  
  /* Terminal styles */
  .terminal-container {
    @apply bg-terminal text-terminal-foreground font-mono;
  }
  
  /* Sidebar styles */
  .sidebar {
    @apply bg-sidebar text-sidebar-foreground border-r border-border;
  }
  
  /* Animation utilities */
  .animate-in {
    animation: animate-in 0.2s ease-out;
  }
  
  .animate-out {
    animation: animate-out 0.2s ease-out;
  }
  
  @keyframes animate-in {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes animate-out {
    from {
      opacity: 1;
      transform: scale(1);
    }
    to {
      opacity: 0;
      transform: scale(0.95);
    }
  }
  
  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-muted rounded;
  }
  
  /* Code highlighting */
  .code-highlight {
    @apply bg-accent/50 rounded px-1;
  }
  
  /* Drag and drop */
  .drag-over {
    @apply bg-accent/20 border-accent border-dashed;
  }
  
  /* Resizable panels */
  .resize-handle {
    @apply bg-border hover:bg-accent transition-colors cursor-col-resize;
  }
  
  .resize-handle:hover {
    @apply bg-accent;
  }
  
  /* Context menu */
  .context-menu {
    @apply bg-popover text-popover-foreground border border-border rounded-md shadow-lg;
  }
  
  /* Tooltip */
  .tooltip {
    @apply bg-popover text-popover-foreground border border-border rounded px-2 py-1 text-xs;
  }

  /* Layout utilities */
  .compact-layout {
    @apply text-sm;
  }

  .compact-layout .panel-header {
    @apply py-2;
  }

  .compact-layout .panel-content {
    @apply p-2;
  }

  /* Responsive utilities */
  @media (max-width: 768px) {
    .mobile-hidden {
      @apply hidden;
    }

    .mobile-collapsed {
      @apply w-auto;
    }
  }

  @media (max-width: 1024px) {
    .tablet-hidden {
      @apply hidden;
    }

    .tablet-collapsed {
      @apply w-auto;
    }
  }

  /* Drag region for Electron */
  .drag-region {
    -webkit-app-region: drag;
  }

  .no-drag {
    -webkit-app-region: no-drag;
  }
}
