import { contextBridge, ipcRenderer } from 'electron'

// Define the API that will be exposed to the renderer process
const electronAPI = {
  // App info
  getVersion: () => ipcRenderer.invoke('app:getVersion'),
  getPlatform: () => ipcRenderer.invoke('app:getPlatform'),

  // Dialog operations
  openFolderDialog: () => ipcRenderer.invoke('dialog:openFolder'),
  saveFileDialog: (defaultPath?: string) => ipcRenderer.invoke('dialog:saveFile', defaultPath),

  // File system operations
  // Project operations
  openProject: (path: string) => ipcRenderer.invoke('fs:openProject', path),
  closeProject: () => ipcRenderer.invoke('fs:closeProject'),
  getProjectStructure: () => ipcRenderer.invoke('fs:getProjectStructure'),
  refreshProject: () => ipcRenderer.invoke('fs:refreshProject'),
  analyzeProject: (path: string) => ipcRenderer.invoke('fs:analyzeProject', path),
  detectProjectType: (path: string) => ipcRenderer.invoke('fs:detectProjectType', path),

  // File operations
  readFile: (path: string) => ipcRenderer.invoke('fs:readFile', path),
  writeFile: (path: string, content: string) => ipcRenderer.invoke('fs:writeFile', path, content),
  createFile: (path: string, content?: string) => ipcRenderer.invoke('fs:createFile', path, content),
  deleteFile: (path: string) => ipcRenderer.invoke('fs:deleteFile', path),
  copyFile: (sourcePath: string, destinationPath: string) => ipcRenderer.invoke('fs:copyFile', sourcePath, destinationPath),
  moveFile: (sourcePath: string, destinationPath: string) => ipcRenderer.invoke('fs:moveFile', sourcePath, destinationPath),

  // Directory operations
  createDirectory: (path: string) => ipcRenderer.invoke('fs:createDirectory', path),
  deleteDirectory: (path: string, recursive?: boolean) => ipcRenderer.invoke('fs:deleteDirectory', path, recursive),
  copyDirectory: (sourcePath: string, destinationPath: string) => ipcRenderer.invoke('fs:copyDirectory', sourcePath, destinationPath),
  moveDirectory: (sourcePath: string, destinationPath: string) => ipcRenderer.invoke('fs:moveDirectory', sourcePath, destinationPath),

  // File type detection
  detectFileType: (path: string) => ipcRenderer.invoke('fs:detectFileType', path),
  getLanguageFromExtension: (extension: string) => ipcRenderer.invoke('fs:getLanguageFromExtension', extension),
  isBinaryFile: (path: string) => ipcRenderer.invoke('fs:isBinaryFile', path),

  // Utility operations
  exists: (path: string) => ipcRenderer.invoke('fs:exists', path),
  getStats: (path: string) => ipcRenderer.invoke('fs:getStats', path),
  searchFiles: (pattern: string, options?: any) => ipcRenderer.invoke('fs:searchFiles', pattern, options),
  getProjectDependencies: (path: string) => ipcRenderer.invoke('fs:getProjectDependencies', path),

  // File watching
  watchFiles: (callback: (event: any) => void) => {
    ipcRenderer.on('fs:fileChanged', callback)
    return () => ipcRenderer.removeListener('fs:fileChanged', callback)
  },
  startWatching: () => ipcRenderer.invoke('fs:startWatching'),
  stopWatching: () => ipcRenderer.invoke('fs:stopWatching'),

  // AI service operations
  // Provider management
  getAIProviders: () => ipcRenderer.invoke('ai:getProviders'),
  setActiveProvider: (provider: string) => ipcRenderer.invoke('ai:setActiveProvider', provider),
  getActiveProvider: () => ipcRenderer.invoke('ai:getActiveProvider'),
  isAIConfigured: () => ipcRenderer.invoke('ai:isConfigured'),
  validateAIConfiguration: () => ipcRenderer.invoke('ai:validateConfiguration'),

  // Model management
  getAvailableModels: () => ipcRenderer.invoke('ai:getAvailableModels'),
  getCurrentModel: () => ipcRenderer.invoke('ai:getCurrentModel'),
  setCurrentModel: (modelId: string) => ipcRenderer.invoke('ai:setCurrentModel', modelId),
  getRecommendedModels: (context?: any) => ipcRenderer.invoke('ai:getRecommendedModels', context),
  getModelsByProvider: (provider: string) => ipcRenderer.invoke('ai:getModelsByProvider', provider),

  // Chat operations
  sendAIMessage: (message: string, context?: any) => ipcRenderer.invoke('ai:sendMessage', message, context),
  streamAIMessage: (message: string, context?: any) => ipcRenderer.invoke('ai:streamMessage', message, context),
  countTokens: (text: string) => ipcRenderer.invoke('ai:countTokens', text),

  // Chat session management
  createChatSession: (projectId?: string) => ipcRenderer.invoke('chat:createSession', projectId),
  getChatSession: (sessionId: string) => ipcRenderer.invoke('chat:getSession', sessionId),
  getAllChatSessions: () => ipcRenderer.invoke('chat:getAllSessions'),
  deleteChatSession: (sessionId: string) => ipcRenderer.invoke('chat:deleteSession', sessionId),
  addChatMessage: (sessionId: string, message: any) => ipcRenderer.invoke('chat:addMessage', sessionId, message),
  getChatMessages: (sessionId: string) => ipcRenderer.invoke('chat:getMessages', sessionId),
  clearChatMessages: (sessionId: string) => ipcRenderer.invoke('chat:clearMessages', sessionId),
  updateChatContext: (sessionId: string, context: any) => ipcRenderer.invoke('chat:updateContext', sessionId, context),
  getChatContext: (sessionId: string) => ipcRenderer.invoke('chat:getContext', sessionId),

  // Token management
  getTokenUsage: (period?: 'daily' | 'monthly' | 'total') => ipcRenderer.invoke('tokens:getUsage', period),
  resetTokenUsage: (period?: 'daily' | 'monthly' | 'total') => ipcRenderer.invoke('tokens:resetUsage', period),
  setTokenLimit: (limit: number, period: 'daily' | 'monthly') => ipcRenderer.invoke('tokens:setLimit', limit, period),
  getTokenLimit: (period: 'daily' | 'monthly') => ipcRenderer.invoke('tokens:getLimit', period),
  isTokenLimitExceeded: (period: 'daily' | 'monthly') => ipcRenderer.invoke('tokens:isLimitExceeded', period),
  getRemainingTokens: (period: 'daily' | 'monthly') => ipcRenderer.invoke('tokens:getRemainingTokens', period),
  getTokenAnalytics: () => ipcRenderer.invoke('tokens:getAnalytics'),

  // API key management
  setAPIKey: (provider: string, key: string) => ipcRenderer.invoke('keys:setApiKey', provider, key),
  getAPIKey: (provider: string) => ipcRenderer.invoke('keys:getApiKey', provider),
  deleteAPIKey: (provider: string) => ipcRenderer.invoke('keys:deleteApiKey', provider),
  validateAPIKey: (provider: string, key: string) => ipcRenderer.invoke('keys:validateApiKey', provider, key),
  hasAPIKey: (provider: string) => ipcRenderer.invoke('keys:hasApiKey', provider),
  getConfiguredProviders: () => ipcRenderer.invoke('keys:getConfiguredProviders'),

  // Terminal operations (will be implemented later)
  createTerminal: (id: string) => ipcRenderer.invoke('terminal:create', id),
  executeCommand: (terminalId: string, command: string) => ipcRenderer.invoke('terminal:execute', terminalId, command),
  killTerminal: (terminalId: string) => ipcRenderer.invoke('terminal:kill', terminalId),
  onTerminalOutput: (callback: (data: any) => void) => {
    ipcRenderer.on('terminal:output', callback)
    return () => ipcRenderer.removeListener('terminal:output', callback)
  },

  // Settings operations
  // General settings
  getSetting: (key: string) => ipcRenderer.invoke('settings:get', key),
  setSetting: (key: string, value: any) => ipcRenderer.invoke('settings:set', key, value),
  deleteSetting: (key: string) => ipcRenderer.invoke('settings:delete', key),
  getAllSettings: () => ipcRenderer.invoke('settings:getAll'),
  clearAllSettings: () => ipcRenderer.invoke('settings:clear'),

  // Theme settings
  getTheme: () => ipcRenderer.invoke('settings:getTheme'),
  setTheme: (theme: 'light' | 'dark' | 'auto') => ipcRenderer.invoke('settings:setTheme', theme),

  // Editor settings
  getEditorSettings: () => ipcRenderer.invoke('settings:getEditorSettings'),
  setEditorSettings: (settings: any) => ipcRenderer.invoke('settings:setEditorSettings', settings),

  // Model settings
  getDefaultModel: () => ipcRenderer.invoke('settings:getDefaultModel'),
  setDefaultModel: (modelId: string) => ipcRenderer.invoke('settings:setDefaultModel', modelId),

  // Token settings
  getTokenLimitSetting: () => ipcRenderer.invoke('settings:getTokenLimit'),
  setTokenLimitSetting: (limit: number) => ipcRenderer.invoke('settings:setTokenLimit', limit),

  // Settings groups
  getSettingsGroup: (groupPrefix: string) => ipcRenderer.invoke('settings:getGroup', groupPrefix),
  setSettingsGroup: (groupPrefix: string, settings: any) => ipcRenderer.invoke('settings:setGroup', groupPrefix, settings),

  // Import/Export
  exportSettings: () => ipcRenderer.invoke('settings:export'),
  importSettings: (settingsJson: string) => ipcRenderer.invoke('settings:import', settingsJson),

  // Validation
  validateSettings: () => ipcRenderer.invoke('settings:validate'),

  // Statistics
  getSettingsStats: () => ipcRenderer.invoke('settings:getStats'),

  // Window operations
  minimizeWindow: () => ipcRenderer.invoke('window:minimize'),
  maximizeWindow: () => ipcRenderer.invoke('window:maximize'),
  closeWindow: () => ipcRenderer.invoke('window:close'),
  isMaximized: () => ipcRenderer.invoke('window:isMaximized'),

  // Dialog operations
  showOpenDialog: (options: any) => ipcRenderer.invoke('dialog:showOpen', options),
  showSaveDialog: (options: any) => ipcRenderer.invoke('dialog:showSave', options),
  showMessageBox: (options: any) => ipcRenderer.invoke('dialog:showMessage', options),

  // External integrations (will be implemented later)
  searchWeb: (query: string) => ipcRenderer.invoke('web:search', query),
  scrapeWebsite: (url: string) => ipcRenderer.invoke('web:scrape', url),
  createFigmaDesign: (prompt: string) => ipcRenderer.invoke('figma:create', prompt),
  analyzeFigmaDesign: (url: string) => ipcRenderer.invoke('figma:analyze', url),
}

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', electronAPI)

// Type definitions for the exposed API
export type ElectronAPI = typeof electronAPI
